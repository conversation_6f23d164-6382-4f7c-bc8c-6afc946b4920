package com.intelliquor.cloud.shop.job.util.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum TerminalShopTypeEnum {

    channel_terminal(0,"烟酒终端"),
    catering_terminal(1,"餐饮终端"),
    together_shopping_terminal(2,"团购终端"),
    enterprise_terminal(3,"企业终端"),
    chain_terminal(4,"连锁终端"),
    chanel_chain_terminal(14,"渠道连锁终端");

    private Integer type;

    private String typeName;

    public static List<Map<String,Object>> arrayList = new ArrayList<>();

    public static Map<Integer, TerminalShopTypeEnum> hashMap = new HashMap<>();

    //处理两个集合
    static{
        for(TerminalShopTypeEnum terminalShopTypeEnum : TerminalShopTypeEnum.values()){
            //创建map填入List
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("postType",terminalShopTypeEnum.type);
            paramMap.put("postTypeName",terminalShopTypeEnum.typeName);
            arrayList.add(paramMap);

            //然后处理hashMap
            hashMap.put(terminalShopTypeEnum.type,terminalShopTypeEnum);
        }
    }

}
