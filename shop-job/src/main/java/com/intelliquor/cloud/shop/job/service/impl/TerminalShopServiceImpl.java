package com.intelliquor.cloud.shop.job.service.impl;


import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.intelliquor.cloud.shop.common.constant.CommonConstant;
import com.intelliquor.cloud.shop.common.constant.RedisConstant;
import com.intelliquor.cloud.shop.common.dao.*;
import com.intelliquor.cloud.shop.common.enums.*;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.message.MessageUtil;
import com.intelliquor.cloud.shop.common.model.*;
import com.intelliquor.cloud.shop.common.model.req.TerminalShopNodeReq;
import com.intelliquor.cloud.shop.common.model.resp.TerminalShopNodeResp;
import com.intelliquor.cloud.shop.common.service.*;
import com.intelliquor.cloud.shop.common.service.guwen.IntegralSyncService;
import com.intelliquor.cloud.shop.common.service.resp.ProtocoTimeParamResp;
import com.intelliquor.cloud.shop.common.utils.*;
import com.intelliquor.cloud.shop.job.dao.*;
import com.intelliquor.cloud.shop.job.model.TerminalShopContractProductModel;
import com.intelliquor.cloud.shop.job.model.*;
import com.intelliquor.cloud.shop.job.model.req.*;
import com.intelliquor.cloud.shop.job.model.resp.*;
import com.intelliquor.cloud.shop.job.service.*;
import com.intelliquor.cloud.shop.job.util.MapUtils;
import com.intelliquor.cloud.shop.job.util.SignUtils;
import com.intelliquor.cloud.shop.job.util.ZTUtils;
import com.intelliquor.cloud.shop.job.util.constants.ColumnConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 终端采集信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-04
 */
@Slf4j
@Service
public class TerminalShopServiceImpl extends ServiceImpl<TerminalShopDao, TerminalShopModel> implements TerminalShopService {

    @Autowired
    private TerminalShopDao terminalShopDao;
    @Autowired
    private TerminalShopInfoScheduleDao terminalShopInfoScheduleDao;

    @Autowired
    private TerminalShopContractDao terminalShopContractDao;

    @Autowired
    private TerminalShopContractProductJobDao terminalShopContractProductDao;

    @Autowired
    private TerminalAccountManagerDealerDao terminalAccountManagerDealerDao;


    @Autowired
    private TerminalAccountManagerDao terminalAccountManagerDao;


    @Autowired
    private TerminalDealerShopDao terminalDealerShopDao;

    @Autowired
    private MemberShopUserRelationDao memberShopUserRelationDao;

    @Autowired
    private ShopUserDao shopUserDao;

    @Autowired
    private ShopDao shopDao;

    @Autowired
    private MemberShopDao memberShopDao;

    @Autowired
    private TerminalShopNodeDao terminalShopNodeDao;

    @Autowired
    private TerminalDataLogDao terminalDataLogDao;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private TerminalDataLogService terminalDataLogService;

    @Autowired
    private ZTUtils ztUtils;

    @Autowired
    private TerminalProtocolChangeDao terminalProtocolChangeDao;


    @Autowired
    private TerminalProtocolDao terminalProtocolDao;

    @Value("${gt_company_id}")
    private Integer gtCompanyId;
    @Autowired
    private UserContext userContext;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ShopDealerOrderCommonDao shopDealerOrderCommonDao;

    @Value("${drink_advisory_mgr_api.url}")
    private String drinkAdvisoryMgrApi;

    @Resource
    private TCloudDealerInfoDao cloudDealerInfoDao;

    @Resource
    private IShopDealerOrderService shopDealerOrderService;

    @Autowired
    private TerminalProductProtocolRelationDao terminalProductProtocolRelationDao;

    @Autowired
    private TerminalProductProtocolV2Service terminalProductProtocolV2Service;

    @Autowired
    private TerminalProductProtocolConfigService terminalProductProtocolConfigService;

    @Autowired
    private ICodeBaseService codeBaseService;

    @Autowired
    private TerminalShopLevelDao terminalShopLevelDao;

    @Value("${oaAccountID}")
    private String oaAccountID;

    @Value("${oaAccountPassword}")
    private String oaAccountPassword;

    /**
     * 登录秘钥
     */
    @Value("${terminal_sign_key}")
    private String SIGN_KEY;
    @Autowired
    private OaUtils oaUtils;

    /**
     * 对接玄武所需要的的参数
     */
    @Value("${xuanwu.sendTerminalData.url}")
    private String sendTerminalDataUrl;

    @Value("${send.oa.orderurl}")
    private String sendOaOrderUrl;

    @Value("${send.oa.orderapprovalurl}")
    private String sendOaOrderApprovalUrl;


    @Value("${gt.type}")
    private String gtType;

    //TimeUnit.MINUTES
    private TimeUnit timeUnit = TimeUnit.SECONDS;
    private long timeOut = 20;
    private boolean isReidsJudge = true;

    @Autowired
    private GtSynOrderService gtSynOrderService;


    @Resource
    private TerminalRewardRecordPrepareMapper terminalRewardRecordPrepareMapper;
    @Autowired
    private TerminalRewardRecordDao terminalRewardRecordDao;

    @Autowired
    private CloudDealerRewardRecordPrepareMapper cloudDealerRewardRecordPrepareMapper;
    @Autowired
    private CloudDealerRewardRecordDao cloudDealerRewardRecordDao;

    @Autowired
    private DealerInfoCommonDao dealerInfoCommonDao;

    @Autowired
    private IRequestLogService requestLogService;

    @Autowired
    private TerminalScanDetailCheckCodeMapper terminalScanDetailCheckCodeMapper;

    @Autowired
    private TerminalScanDetailPlusDao terminalScanDetailPlusDao;

    @Resource
    private IntegralSyncService integralSyncService;

    @Autowired
    private TerminalScanDetailCommonDao terminalScanDetailCommonDao;

    @Autowired
    private ITerminalLocationModifyService locationModifyService;

    @Autowired
    private TerminalShopService terminalShopService;

    @Autowired
    private TerminalLocationModifyDao terminalLocationModifyDao;

    @Resource
    private ITerminalShopDisableRecordCommonService terminalShopDisableRecordService;

    @Autowired
    private TerminalSkuCheckService terminalSkuCheckService;

    @Autowired
    private TerminalProductProtocolV2Service terminalProductProtocolV2CommonService;

    @Autowired
    private TerminalProductProtocolRelationChangeDetailCommonDao terminalProductProtocolRelationChangeDetailCommonDao;

    @Autowired
    private TerminalProductProtocolRelationChangeCommonDao terminalProductProtocolRelationChangeCommonDao;

    @Value("${user.login.pwd}")
    private String userLoginPwd;

    @Resource
    private ITerminalShopCommonService terminalShopCommonService;

    /**
     * @author: HLQ
     * @Date: 2023/2/24 20:15
     * @Description: 新增修改记录表
     */
    @Override
    public Integer insertDataLog(TerminalShopReq terminalShopReq, TerminalShopInfoScheduleModel terminalShopInfoScheduleModel, List<TerminalProtocolReq> protocolList, boolean isInsert) {
        TerminalShopLogResp terminalShopLogRespByShop = getTerminalShopLogRespByShop(terminalShopReq);
        //重新设置终端正式表ID和临时表的ID
        terminalShopLogRespByShop.setTerminalShopId(terminalShopReq.getId());
        terminalShopLogRespByShop.setId(terminalShopInfoScheduleModel.getId());

        //查找主协议
        if (CollectionUtils.isNotEmpty(protocolList)) {
            TerminalProtocolReq terminalProtocolReq = protocolList.stream().filter(e -> 0 == e.getProtocolType()).findAny().orElse(null);
            TerminalProtocolLogResp terminalProtocolLogResp = null;
            if (!Objects.isNull(terminalProtocolReq)) {
                terminalProtocolLogResp = getTerminalProtocolLogRespByProtocol(terminalProtocolReq);
            }
            terminalShopLogRespByShop.setTerminalProtocolLogResp(terminalProtocolLogResp);
        }
        TerminalDataLogModel terminalDataLogModel = new TerminalDataLogModel();
        terminalDataLogModel.setTerminalShopId(terminalShopInfoScheduleModel.getTerminalShopId());
        terminalDataLogModel.setScheduleShopId(terminalShopInfoScheduleModel.getId());
        terminalDataLogModel.setCompanyId(terminalShopReq.getCompanyId());
        terminalDataLogModel.setProtocolId(0);
        terminalDataLogModel.setCreateUser(terminalShopInfoScheduleModel.getCreateUser());
        terminalDataLogModel.setNewData(JSON.toJSONString(terminalShopLogRespByShop));
        terminalDataLogModel.setApprovalStatus(1);
        if (isInsert) {//新增
            terminalDataLogModel.setRawData(null);
            terminalDataLogModel.setCheckType(1);
        } else {//更新，需查询上次的记录
            List<TerminalDataLogModel> terminalDataLogModelList = terminalDataLogDao.selectList(new QueryWrapper<TerminalDataLogModel>()
                    .eq("terminal_shop_id", terminalDataLogModel.getTerminalShopId())
                    .eq("company_id", terminalDataLogModel.getCompanyId())
                    .eq("protocol_id", 0)
                    .orderByDesc("create_time"));
            if (CollectionUtils.isNotEmpty(terminalDataLogModelList)) {
                if (terminalDataLogModelList.stream().anyMatch(item -> item.getApprovalStatus().equals(1))) {
                    throw new BusinessException("400", "终端有审批未完成");
                }
                terminalDataLogModel.setRawData(terminalDataLogModelList.get(0).getNewData());
            }
            terminalDataLogModel.setCheckType(3);
        }
        terminalDataLogService.save(terminalDataLogModel);
        return terminalDataLogModel.getId();
    }



    /**
     * @author: HLQ
     * @Date: 2023/2/24 15:42
     * @Description: 组装修改记录表中的JSON字段数据
     * terminalShopModel: 终端信息表
     */
    private TerminalShopLogResp getTerminalShopLogRespByShop(TerminalShopReq terminalShopReq) {
        TerminalShopLogResp terminalShopLogResp = new TerminalShopLogResp();
        BeanUtils.copyProperties(terminalShopReq, terminalShopLogResp);
        terminalShopLogResp.setAreaName(terminalShopReq.getProvince() + terminalShopReq.getCity() + terminalShopReq.getDistrict());
        if (!Objects.isNull(terminalShopReq.getPrimaryContract())) {
            terminalShopLogResp.setDealerCode(terminalShopReq.getPrimaryContract().getDealerCode());
            terminalShopLogResp.setContractType(terminalShopReq.getPrimaryContract().getContractType());
        }
        terminalShopLogResp.setReceivingWarehouseAddress(terminalShopReq.getReceivingWarehouseProvince()
                + terminalShopReq.getReceivingWarehouseCity()
                + terminalShopReq.getReceivingWarehouseDistrict() + terminalShopReq.getReceivingWarehouseAddress());
        return terminalShopLogResp;
    }

    /**
     * @author: HLQ
     * @Date: 2023/2/24 16:49
     * @Description: 组装修改记录表中的JSON字段数据
     * terminalProtocolModel: 协议对象
     */
    private TerminalProtocolLogResp getTerminalProtocolLogRespByProtocol(TerminalProtocolReq terminalProtocolReq) {
        TerminalProtocolLogResp terminalProtocolLogResp = new TerminalProtocolLogResp();
        BeanUtils.copyProperties(terminalProtocolReq, terminalProtocolLogResp);
        return terminalProtocolLogResp;
    }


    /**
     * @author: HLQ
     * @Date: 2023/2/23 15:07
     * @Description: 查询客户经理信息
     */
    private TerminalAccountManagerModel setMangerInfo(Integer createUser) {
        TerminalAccountManagerModel terminalAccountManagerModel = terminalAccountManagerDao.selectOne(new QueryWrapper<TerminalAccountManagerModel>().eq("id", createUser));
        if (Objects.isNull(terminalAccountManagerModel)) {
            throw new BusinessException("400", "创建人用户数据异常，请联系相关人员");
        }
        if (terminalAccountManagerModel.getType().intValue() != 0) {//是客户经理
            TerminalAccountManagerModel accountManagerModel = terminalAccountManagerDao.selectOne(new QueryWrapper<TerminalAccountManagerModel>().eq("id", terminalAccountManagerModel.getParentId()));
            if (Objects.isNull(accountManagerModel)) {
                throw new BusinessException("400", "找不到当前数据创建人对应的客户经理数据，请联系相关人员");
            }
            return accountManagerModel;
        }
        return terminalAccountManagerModel;
    }


    @Override
    @Transactional
    public boolean approvalTerminalShop(TerminalShopNodeReq terminalShopNodeReq) {
        boolean rtnVal = false;
        String nodeRedisKey =  String.format(RedisConstant.TERMINAL_SHOP_NODE_REDIS_KEY, terminalShopNodeReq.getId());
        Boolean aBoolean = redisTemplate.opsForValue().setIfAbsent(nodeRedisKey, terminalShopNodeReq.getId(), 4, TimeUnit.MINUTES);
        Set<String> redisKeyList = new HashSet<>();
        redisKeyList.add(nodeRedisKey);
        if (!aBoolean) {
            throw new BusinessException("400", "数据正在终端信息审批处理中，不能重复操作...");
        }
        try {
            //获取DataLogId
            TerminalShopNodeModel terminalShopNodeModel = terminalShopNodeDao.selectById(terminalShopNodeReq.getId());
            if ("1".equals(terminalShopNodeModel.getNodeStatus())) {
                throw new BusinessException("400", "数据已审批,不能重复审批");
            }
            Integer nodeType = terminalShopNodeModel.getNodeType();
            if (nodeType == 5) {//下单
                rtnVal = approvalOrderInfo(terminalShopNodeReq, terminalShopNodeModel, rtnVal);
            } else if (nodeType == 6) {//收货
                rtnVal = approvalReceiptInfo(terminalShopNodeReq, terminalShopNodeModel, rtnVal);
            }else if (TerminalShopNodeEnum.TERMINAL_ADDRESS_EDIT.getType().equals(nodeType)) {// 终端地址变更
                rtnVal = changeAddress(terminalShopNodeReq, terminalShopNodeModel, rtnVal);
            } else {//终端或协议
                rtnVal = approvalTerminalOrProtocol(terminalShopNodeReq, terminalShopNodeModel, rtnVal, redisKeyList);
            }
            redisTemplate.opsForValue().setIfAbsent(nodeRedisKey, terminalShopNodeReq.getId(), 1, TimeUnit.SECONDS);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("400", e.getMessage());
        }
        return rtnVal;
    }

    /**
     *  20230818新需求  客户经理通过 就不用审核中心审核
     */
    private boolean approvalTerminalOrProtocol(TerminalShopNodeReq terminalShopNodeReq,
                                               TerminalShopNodeModel terminalShopNodeModel,
                                               boolean rtnVal, Set<String> redisKeyList) {
        //组装更新对象
        String updateMsg = terminalShopNodeReq.getUpdateMsg();
        TerminalShopNodeModel updateNodeModel = TerminalShopNodeModel.builder()
                .id(terminalShopNodeReq.getId())
                .isBack(terminalShopNodeReq.getIsBack())
                .updateMsg(updateMsg)
                .updateDate(new Date())
                .nodeStatus("1")
                .approvalUser(userContext.getTerminalModel().getId())
                .updateName(userContext.getTerminalModel().getName())
                .updatePhone(userContext.getTerminalModel().getPhone())
                .build();

        //0未激活 1激活 2客户经理审核中 3客户经理审核失败 4 中台审核中 5中台审核失败
        int status = "0".equals(terminalShopNodeReq.getIsBack()) ? 1 : 3;

        //更新状态和审批信息
        if (terminalShopNodeModel.getNodeType() == 2 || terminalShopNodeModel.getNodeType() == 4) {//协议的
            TerminalProtocolModel terminalProtocol = terminalProtocolDao.selectById(terminalShopNodeModel.getProtocolId());
            terminalProtocol.setCheckStatus(status);
            String remark = terminalProtocol.getRemark();
            if(StringUtils.isNotEmpty(updateMsg)){
                remark = StringUtils.isNotEmpty(remark) ? remark.concat(updateMsg) : updateMsg;
                terminalProtocol.setRemark(remark);
            }

            terminalProtocol.setEndStatus(2);

            Integer oldId = terminalProtocolChangeDao.getOldIdByNewId(terminalShopNodeModel.getProtocolId());
            if(Objects.nonNull(oldId) && status == 1){
                TerminalProtocolModel oldTerminalProtocol = terminalProtocolDao.selectById(oldId);
                if(Objects.nonNull(oldTerminalProtocol)){
                    Integer endStatus = oldTerminalProtocol.getEndStatus();
                    Integer checkStatus = oldTerminalProtocol.getCheckStatus();
                    if(endStatus==2 && checkStatus == 1){
                        oldTerminalProtocol.setRemark(DateUtils.convert2StringYYYYMMddHHmmss(new Date())+"激活了id="+terminalShopNodeModel.getProtocolId()+"的协议，此协议无效。");
                    }else{
                        oldTerminalProtocol.setRemark(DateUtils.convert2StringYYYYMMddHHmmss(new Date())+"激活了id="+terminalShopNodeModel.getProtocolId()+"的协议，此协议置为无效。");
                    }
                    oldTerminalProtocol.setDeleteStatus(1);
                    terminalProtocolDao.updateById(oldTerminalProtocol);
                }
            }

            if(terminalProtocol.getProtocolType() == 0 && status == 1){
                LambdaQueryWrapper<TerminalProtocolModel> queryWrapper = Wrappers.lambdaQuery();
                queryWrapper.eq(TerminalProtocolModel::getTerminalShopId, terminalProtocol.getTerminalShopId());
                queryWrapper.eq(TerminalProtocolModel::getProtocolType, 0);
                queryWrapper.eq(TerminalProtocolModel::getDeleteStatus, 0);
                queryWrapper.eq(TerminalProtocolModel::getCheckStatus, 1);
                if(Objects.nonNull(oldId)){
                    queryWrapper.ne(TerminalProtocolModel::getId,oldId);
                }
                List<TerminalProtocolModel> list = terminalProtocolDao.selectList(queryWrapper);
                //处理特殊情况  新增时也有生效的主协议
                for (TerminalProtocolModel protocolModel : list) {
                    protocolModel.setRemark(protocolModel.getRemark()+">"+DateUtils.convert2StringYYYYMMddHHmmss(new Date())+"客户经理审核通过,更新为无效,激活id="+terminalProtocol.getId());
                    protocolModel.setDeleteStatus(1);
                    terminalProtocolDao.updateById(protocolModel);
                }
            }

            // t_terminal_protocol_change 表的审批状态 approval_status 审批状态：0-未审批 1-审批中 2-审批失败 3-审批成功
            Integer approvalStatus = status == 3 ? 2 : 3;
            terminalProtocolChangeDao.updateChangeRecordApprovalStatusByNewId(terminalShopNodeModel.getProtocolId(), approvalStatus);
            /*if(status == 3){ //驳回时直接置为无效  否则会在终端详情种展示
                terminalProtocol.setDeleteStatus(1);
            }*/
            terminalProtocolDao.updateById(terminalProtocol);

            if(status == 1){ //数据推送中台
                TerminalShopModel terminalShopModel = terminalShopDao.selectById(terminalShopNodeModel.getTerminalShopId());
                TerminalShopInfoScheduleModel scheduleModel = new TerminalShopInfoScheduleModel();
                BeanUtils.copyProperties(terminalShopModel, scheduleModel);
                scheduleModel.setId(null);
                scheduleModel.setTerminalShopId(terminalShopNodeModel.getTerminalShopId());
                LambdaQueryWrapper<TerminalShopContractModel> qw = Wrappers.lambdaQuery();
                qw.eq(TerminalShopContractModel::getTerminalShopId, terminalShopNodeModel.getTerminalShopId());
                qw.orderByDesc(TerminalShopContractModel::getId);
                List<TerminalShopContractModel> contractModelList = terminalShopContractDao.selectList(qw);
                TerminalShopContractResp terminalShopContractResp = new TerminalShopContractResp();
                if (contractModelList.size() > 0) {
                    TerminalShopContractModel contractModel = contractModelList.get(0);
                    BeanUtils.copyProperties(contractModel, terminalShopContractResp);
                }
                Integer protocolType = terminalProtocol.getProtocolType();
                if (protocolType == 0) {
                    sendZtData(scheduleModel, terminalShopModel, terminalShopContractResp, 1, "协议", terminalProtocol);
                }
            }
            if (status == 1) { // 审批通过
                // sendProtocolMessage(terminalProtocol, 1);
                rtnVal = true;
            } else if (status == 3) { // 审批不通过
                sendProtocolMessage(terminalProtocol, 2);
            }
            // 确保之前新增或更新是的key 被删除
            String redisKey =  String.format(RedisConstant.REDIS_ADD_UPDATE_PROTOCOL, terminalProtocol.getTerminalShopId());
            redisTemplate.delete(redisKey);
        } else {
            TerminalShopModel shopModel = terminalShopDao.selectById(terminalShopNodeModel.getTerminalShopId());
            TerminalShopInfoScheduleModel scheduleModel = terminalShopInfoScheduleDao.selectById(terminalShopNodeModel.getScheduleShopId());
            if(status == 3){
                LambdaUpdateWrapper<TerminalDataLogModel> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(TerminalDataLogModel::getId, terminalShopNodeModel.getDataLogId());
                updateWrapper.set(TerminalDataLogModel::getApprovalStatus, 2); //状态(0:未审批1:审批中,2:审批失败;3:审批成功)
                terminalDataLogDao.update(new TerminalDataLogModel(), updateWrapper);
                TerminalProtocolModel mainProtocol = new TerminalProtocolModel();
                mainProtocol.setCheckStatus(3);
                mainProtocol.setEndStatus(2);
                mainProtocol.setDeleteStatus(1);
                mainProtocol.setRemark(DateUtils.convert2StringYYYYMMddHHmmss(new Date()) + "被" + userContext.getTerminalModel().getName() + "驳回");
                terminalProtocolDao.update(mainProtocol, new QueryWrapper<TerminalProtocolModel>()
                        .eq("terminal_shop_id", terminalShopNodeModel.getTerminalShopId())
                        .eq("company_id", terminalShopNodeModel.getCompanyId())
                        .eq("protocol_type", 0)
                        .ne("end_status", 2)
                        .ne("delete_status", 0)
                        .eq("check_status", 2));
                scheduleModel.setStatus(3);
                scheduleModel.setAuditResult(terminalShopNodeReq.getUpdateMsg());
                scheduleModel.setAuditUserId(userContext.getTerminalModel().getId());
                terminalShopInfoScheduleDao.updateById(scheduleModel);
            }else{
                //查询是否是第一次激活
                int insertOrUpdate = 0;
                TerminalShopNodeReq nodeReq = new TerminalShopNodeReq();
                nodeReq.setTerminalShopId(terminalShopNodeModel.getTerminalShopId());
                nodeReq.setProtocolId(0);
                nodeReq.setCompanyId(gtCompanyId);
                Integer num = terminalShopNodeDao.selectActivateNodeList(nodeReq);

                if (num > 0 || shopModel.getStatus().intValue() == 1) {//不是第一次激活    shopModel.getStatus().intValue()==1是为了结合以前的老数据
                    insertOrUpdate = 1;
                }

                TerminalShopReq shopReq = new TerminalShopReq();
                BeanUtils.copyProperties(scheduleModel, shopReq);
                shopReq.setId(scheduleModel.getTerminalShopId());

                //校验店铺终端是否存在
                judgeVerify(shopReq, insertOrUpdate);

                //处理主编码和副编码【新逻辑  存在编码  就不用生成新编码了】
                String mainCode = shopModel.getMainCode();
                if (!com.intelliquor.cloud.shop.job.util.StringUtils.hasNull(mainCode)) {
                    handleMainDeputyCode(scheduleModel, shopModel);
                }
                //查询合同数据
                TerminalShopContractResp terminalShopContractResp = terminalShopContractDao.selectTerminalShopContractResp(scheduleModel.getTerminalShopId());
                //查询合同数据
                TerminalProtocolModel terminalProtocolModel = terminalProtocolDao.selectProtocolByTerminalShopId(scheduleModel.getTerminalShopId());

                //处理t_member_shop 中的所有关系  添加到终端表和终端用户表
                Integer mShopId = insertHandleMemberShopAndMemberUser(scheduleModel, terminalShopContractResp, terminalProtocolModel, shopModel, true, redisKeyList);

                //把联盟终端店id添加到请求类
                shopReq.setMemberShopId(mShopId);
                shopModel.setMemberShopId(mShopId);
                scheduleModel.setMemberShopId(mShopId);

                //更新主协议的审批状态 t_terminal_protocol

                //可能有多条协议 terminal_shop_id = 98192
                LambdaQueryWrapper<TerminalProtocolModel> lqw = Wrappers.lambdaQuery();
                lqw.eq(TerminalProtocolModel::getTerminalShopId,scheduleModel.getTerminalShopId());
                lqw.eq(TerminalProtocolModel::getCompanyId,scheduleModel.getCompanyId());
                lqw.eq(TerminalProtocolModel::getProtocolType,0);
                lqw.orderByDesc(TerminalProtocolModel::getCreateTime);
                List<TerminalProtocolModel> terminalProtocolModelList = terminalProtocolDao.selectList(lqw);
                for (int i = 0; i < terminalProtocolModelList.size(); i++) {
                    TerminalProtocolModel protocolModel =  terminalProtocolModelList.get(i);
                    if(i == 0){

                        protocolModel.setCheckStatus(1);
                        protocolModel.setEndStatus(2);
                        protocolModel.setRemark("激活终端");
                        terminalProtocolDao.updateById(protocolModel);
                    }else{
                        protocolModel.setDeleteStatus(1);
                        protocolModel.setCheckStatus(0);
                        protocolModel.setRemark("激活终端时存在多个主协议,删除");
                        terminalProtocolDao.updateById(protocolModel);
                    }
                }

                /*TerminalProtocolModel mainProtocol = new TerminalProtocolModel();
                mainProtocol.setCheckStatus(1);
                mainProtocol.setEndStatus(2);
                mainProtocol.setMemberShopId(Long.valueOf(mShopId));
                terminalProtocolDao.update(mainProtocol, new QueryWrapper<TerminalProtocolModel>()
                        .eq("terminal_shop_id", scheduleModel.getTerminalShopId())
                        .eq("company_id", scheduleModel.getCompanyId())
                        .eq("protocol_type", 0));*/

                //处理合同信息
                if (shopModel.getStatus().intValue() == 0) {
                    TerminalShopContractModel terminalShopContractModel = new TerminalShopContractModel();
                    terminalShopContractModel.setId(terminalShopContractResp.getId());
                    terminalShopContractModel.setMemberShopId(mShopId);
                    terminalShopContractDao.updateById(terminalShopContractModel);
                }

                //正式表赋值中心审批通过的信息
                TerminalShopModel newShopModel = new TerminalShopModel();
                scheduleModel.setStatus(status);
                scheduleModel.setAuditResult(updateMsg);
                BeanUtils.copyProperties(scheduleModel, newShopModel);
                newShopModel.setId(shopModel.getId());

                Integer isPrepare = scheduleModel.getIsPrepare();
                if (isPrepare == 1) {
                    insertOrUpdate = 0;
                }

                log.info("终端激活后---{}", JSON.toJSONString(newShopModel));
                //中台审批通过的终端信息同步到中台 同时记录是否同步成功
                sendZtData(scheduleModel, newShopModel, terminalShopContractResp, insertOrUpdate, "终端", null);
                log.info("终端激活后---{}", JSON.toJSONString(newShopModel));
                //更新终端主表信息
                newShopModel.setIsPrepare(0);
                newShopModel.setStatus(1);
                scheduleModel.setIsPrepare(0);
                terminalShopDao.updateById(newShopModel);

                scheduleModel.setStatus(1);
                scheduleModel.setAuditResult(terminalShopNodeReq.getUpdateMsg());
                scheduleModel.setAuditUserId(userContext.getTerminalModel().getId());
                terminalShopInfoScheduleDao.updateById(scheduleModel);

                if (isPrepare == 1) {
                    //处理积分数据
                    handlePrepareTerminalData(shopModel.getMemberShopId());

                    //更新正式终端
                    shopDao.updateIsPrepareByShopId(shopModel.getMemberShopId());

                }
            }

            LambdaUpdateWrapper<TerminalDataLogModel> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(TerminalDataLogModel::getId, terminalShopNodeModel.getDataLogId());
            updateWrapper.set(TerminalDataLogModel::getApprovalStatus, status == 3 ? 2 : 3);
            terminalDataLogDao.update(new TerminalDataLogModel(), updateWrapper);

            if (status == 1) { // 审批通过
                //   sendMessage(scheduleModel, 1);
                rtnVal = true;
            } else if (status == 5) { // 审批不通过
                sendMessage(scheduleModel, 2);
            }
        }
        terminalShopNodeDao.updateById(updateNodeModel);
        return rtnVal;
    }



    private boolean approvalOrderInfo(TerminalShopNodeReq terminalShopNodeReq, TerminalShopNodeModel terminalShopNodeModel, boolean rtnVal) {
        String isBack = terminalShopNodeReq.getIsBack(); // 0 通过   1驳回  2 提交OA
        ShopDealerOrderCommonModel orderCommonModel = shopDealerOrderCommonDao.getOrderByOrderId(terminalShopNodeModel.getOrderId());
        Integer totalMonthQty = shopDealerOrderCommonDao.getMonthTotalQty(terminalShopNodeModel.getShopId(), orderCommonModel.getGoodsCode());
        //组装更新对象
        TerminalShopNodeModel updateNodeModel = TerminalShopNodeModel.builder()
                .id(terminalShopNodeReq.getId())
                .isBack("2".equals(isBack) ? "0" : isBack)
                .updateMsg(terminalShopNodeReq.getUpdateMsg())
                .updateDate(new Date())
                .nodeStatus("1")
                .approvalUser(userContext.getTerminalModel().getId())
                .updateName(userContext.getTerminalModel().getName())
                .updatePhone(userContext.getTerminalModel().getPhone())
                .approvalQty(totalMonthQty)
                .build();

        if ("2".equals(terminalShopNodeReq.getIsBack())) {//OA审批
            TerminalShopNodeModel nodeModel = TerminalShopNodeModel.builder()
                    .terminalShopId(terminalShopNodeReq.getTerminalShopId())
                    .scheduleShopId(terminalShopNodeReq.getScheduleShopId())
                    .orderNo(terminalShopNodeModel.getOrderNo())
                    .companyId(terminalShopNodeReq.getCompanyId())
                    .shopId(terminalShopNodeModel.getShopId())
                    .orderId(terminalShopNodeModel.getOrderId())
                    .createTime(new Date())
                    .nodeName("OA审批申请")
                    .nodeLevel("1")
                    .nodeType(terminalShopNodeModel.getNodeType())
                    .updateName("OA审批")
                    .updateUser(0)
                    .dataLogId(0)
                    .currentQty(totalMonthQty)
                    .thresholdVal(terminalShopNodeModel.getThresholdVal())
                    .build();

            //组装数据
            JSONObject formValues = getOaParam(terminalShopNodeModel, totalMonthQty);

            Map<String, Object> paramBody = new HashMap<String, Object>();

            paramBody.put("docSubject", userContext.getTerminalModel().getName() + "申请一条订单(" + formValues.getString("orderNo") + ")");
            JSONObject personJson = new JSONObject();
            personJson.put("LoginName", userContext.getTerminalModel().getPhone());
            paramBody.put("docCreator", personJson.toJSONString());
            paramBody.put("fdTemplateId", "187787752db223af965f6ac416d9eb2f");
            paramBody.put("formValues", formValues.toJSONString());

            HttpHeaders headers = oaUtils.getOaToken();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<Map<String, Object>>(paramBody, headers);
            RequestLog requestLog = new RequestLog();
            long ernterSystemTime = System.currentTimeMillis();
            requestLog.setReqName("下单审批:" + orderCommonModel.getOrderCode());
            requestLog.setReqType(1);
            requestLog.setReqUrlPath(sendOaOrderUrl);
            requestLog.setCreateDate(new Date());
            requestLog.setReqJson(JSON.toJSONString(paramBody));
            requestLog.setReqKey(orderCommonModel.getOrderCode());

            //请求参数打印
            log.info("OA下单审核的实体参数:{}", JSON.toJSONString(paramBody));
            try {
                RestTemplate template = new RestTemplate();
                ResponseEntity<Map> exchange = template.exchange(sendOaOrderUrl, HttpMethod.POST, entity, Map.class);
                requestLog.setResJson(exchange.getBody().toString());
                requestLog.setResCode(exchange.getBody().get("code").toString());
                requestLog.setResMsg(exchange.getBody().get("msg").toString());
                log.info("OA下单审核返回的数据---{}---{}", formValues.getString("orderNo"), JSON.toJSONString(exchange));
                if ("200".equals(exchange.getBody().get("code").toString())) {
                    String data = exchange.getBody().get("data").toString();
                    String msg = exchange.getBody().get("msg").toString();
                    if (StringUtils.isBlank(data)) {//失败
                        throw new BusinessException("400", "OA下单审核失败:" + msg);
                    }
                    nodeModel.setOaId(data);
                } else {
                    String msg = exchange.getBody().get("msg").toString();
                    throw new BusinessException("400", msg);
                }
            } catch (Exception e) {
                requestLog.setResCode("-1");
                requestLog.setResMsg("申请OA下单审核失败:" + e.getMessage());
                log.error("申请OA下单审核失败原因:{}", e.getMessage());
                throw new BusinessException("400", "申请OA下单审核失败原因:" + e.getMessage());
            } finally {
                log.info("RequestLog===>", JSONObject.toJSONString(requestLog));
                long outSystemTime = System.currentTimeMillis();
                String diffTime = DateUtils.longToHMS(outSystemTime - ernterSystemTime);
                requestLog.setReqTime(diffTime);
                requestLogService.insertLog(requestLog);
            }
            String currentYm = DateUtils.convert2String(new Date(), "yyyy-MM");
            nodeModel.setMonthYear(currentYm);
            terminalShopNodeDao.insert(nodeModel);
        } else {//修改订单的状态
            Integer orderStatus = "0".equals(isBack) ? 1 : 8;
            ShopDealerOrderCommonModel orderInfo = new ShopDealerOrderCommonModel();
            orderInfo.setId(terminalShopNodeModel.getOrderId());
            orderInfo.setOrderStatus(orderStatus);
            orderInfo.setApprovalUser(userContext.getTerminalModel().getId());
            orderInfo.setApprovalDate(new Date());
            orderInfo.setApprovalMsg(terminalShopNodeReq.getUpdateMsg());
            orderInfo.setApprovalPhone(userContext.getTerminalModel().getPhone());
            orderInfo.setApprovalUserName(userContext.getTerminalModel().getName());
            shopDealerOrderCommonDao.approvalOrderState(orderInfo);
            if (orderStatus == 1) {//同步订单到溯源
                //主装数据
                ShopDealerOrderModel model = new ShopDealerOrderModel();
                ShopDealerOrderDetailModel detailModel = new ShopDealerOrderDetailModel();
                model.setSenderCode(orderCommonModel.getSenderCode());
                model.setOrderCode(orderCommonModel.getOrderCode());
                model.setShopId(orderCommonModel.getShopId());
                model.setDealerCode(orderCommonModel.getDealerCode());

                detailModel.setQty(orderCommonModel.getQty());
                detailModel.setGoodsPrice(orderCommonModel.getGoodsPrice());
                detailModel.setGoodsCode(orderCommonModel.getGoodsCode());
                detailModel.setGoodsName(orderCommonModel.getGoodsName());
                log.info("【客户经理】需审批的下单同步溯源的参数：SenderCode={},OrderCode={},shopId={},DealerCode={},Qty={},GoodsPrice={},GoodsCode={},GoodsNam={}",
                        model.getSenderCode(), model.getOrderCode(), model.getShopId(), model.getDealerCode(), detailModel.getQty(), detailModel.getGoodsPrice(),
                        detailModel.getGoodsCode(), detailModel.getGoodsName());
                log.info("客户经理审批订单单号为:{},同步溯源开始", orderCommonModel.getOrderCode());
                gtSynOrderService.sycOrder2Gt(model, detailModel);
                log.info("客户经理审批订单单号为:{},同步溯源完成", orderCommonModel.getOrderCode());
            } else {
                // 如果积分下单 则退回积分
                BigDecimal virtualAmount = orderCommonModel.getVirtualAmount();
                if (virtualAmount.compareTo(BigDecimal.ZERO) > 0) {
                    //  正常情况下 只有1条
                    TerminalRewardRecordModel model = new TerminalRewardRecordModel();
                    model.setSourceId(terminalShopNodeModel.getOrderId() + "");
                    model.setSource(TerminalRewardRecordSourceEnum.PURCHASE_CONSUMPTION.getCode());
                    List<TerminalRewardRecordModel> recordModelList = terminalRewardRecordDao.getListBySourceParam(model);
                    for (TerminalRewardRecordModel terminalRewardRecordModel : recordModelList) {
                        terminalRewardRecordModel.setId(null);
                        terminalRewardRecordModel.setSource(TerminalRewardRecordSourceEnum.CANCEL_PURCHASE.getCode());
                        terminalRewardRecordModel.setType(1);
                        terminalRewardRecordModel.setCreateUserType(3);
                        terminalRewardRecordModel.setRemark("取消在线订货【客户经理驳回:" + terminalShopNodeReq.getUpdateMsg() + "】");
                        terminalRewardRecordModel.setSysState(0);
                        terminalRewardRecordModel.setIsAssign(0);
                        terminalRewardRecordModel.setAmount(terminalRewardRecordModel.getAmount().abs());
                        terminalRewardRecordModel.setOrderNo(orderCommonModel.getOrderCode());
                        terminalRewardRecordDao.insert(terminalRewardRecordModel);
                    }
                    shopDao.addVirtualAmount(virtualAmount.abs(), orderCommonModel.getShopId());
                }
            }
        }
        terminalShopNodeDao.updateById(updateNodeModel);
        if (!"0".equals(isBack)) {
            rtnVal = true;
        }
        return rtnVal;
    }


    private static String addAuth(HttpHeaders headers, String yourEncryptedWorlds) {
        byte[] encodedAuth = org.springframework.security.crypto.codec.Base64.encode(yourEncryptedWorlds.getBytes(Charset.forName("UTF-8")));
        String authHeader = "Basic " + new String(encodedAuth);
        headers.set("Authorization", authHeader);
        return authHeader;
    }

    /**
     * @author: HLQ
     * @Date: 2023/4/17 9:03
     * @Description: 组合推送OA的数据
     */
    private JSONObject getOaParam(TerminalShopNodeModel terminalShopNodeModel, Integer totalMonthQty) {
        TerminalShopModel terminalShopModel = terminalShopDao.selectById(terminalShopNodeModel.getTerminalShopId());
        ShopDealerOrderCommonModel orderCommonModel = shopDealerOrderCommonDao.getOrderByOrderId(terminalShopNodeModel.getOrderId());
        JSONObject reqParam = new JSONObject();
        //orderNo	string	订单编号
        reqParam.put("orderNo", orderCommonModel.getOrderCode());
        //shopName	string	终端/会员名称
        reqParam.put("shopName", terminalShopModel.getShopName());
        // shopTypeName	string	终端/会员类型
        reqParam.put("shopTypeName", terminalShopModel.getShopType() == 5 ? "会员" : "终端");
        //goodsName	string	下单商品
        reqParam.put("goodsName", orderCommonModel.getGoodsName());
        // goodsQty	int	下单数量（瓶）
        reqParam.put("goodsQty", orderCommonModel.getQty());
        // totalMonthQty	int	本月累计订货量包含当前订单（瓶）
        reqParam.put("totalMonthQty", totalMonthQty);
        // orderAmt	int	订单总额（元）
        reqParam.put("orderAmt", orderCommonModel.getOrderAmount());
        // scoreNum	int	积分抵扣
        reqParam.put("scoreNum", orderCommonModel.getVirtualAmount());
        // boundary	int	审核门槛（本月累计订货量大于等于该值分公司审核，小于则只需要大区审核）
        String thresholdVal = terminalShopNodeModel.getThresholdVal();
        String[] fzVals = thresholdVal.split("--");
        if (Integer.valueOf(fzVals[1]) <= totalMonthQty) {//分公司审核
            reqParam.put("boundary", Integer.valueOf(fzVals[1]));
        } else {
            reqParam.put("boundary", Integer.valueOf(fzVals[0]));
        }
        return reqParam;
    }


    /**
     * 收货
     */
    private JSONObject getReceiptOaParam(TerminalShopNodeModel terminalShopNodeModel, TerminalScanDetailModel terminalScanDetailModel, Integer totalMonthQty) {
        TerminalShopModel terminalShopModel = terminalShopDao.selectById(terminalShopNodeModel.getTerminalShopId());
        JSONObject reqParam = new JSONObject();
        //orderNo	string	订单编号
        reqParam.put("orderNo", terminalScanDetailModel.getReceivedOrderCode());
        //shopName	string	终端/会员名称
        reqParam.put("shopName", terminalShopModel.getShopName());
        // shopTypeName	string	终端/会员类型
        reqParam.put("shopTypeName", terminalShopModel.getShopType() == 5 ? "会员" : "终端");
        //goodsName	string	下单商品
        reqParam.put("goodsName", terminalScanDetailModel.getGoodsName());
        // goodsQty	int	下单数量（瓶）
        reqParam.put("goodsQty", terminalScanDetailModel.getQuantity());
        // totalMonthQty	int	本月累计订货量包含当前订单（瓶）
        reqParam.put("totalMonthQty", totalMonthQty);
        // orderAmt	int	订单总额（元）  todo 金额有问题
        reqParam.put("orderAmt", 0);
        // scoreNum	int	积分抵扣
        reqParam.put("scoreNum", 0);
        // boundary	int	审核门槛（本月累计订货量大于等于该值分公司审核，小于则只需要大区审核）
        String thresholdVal = terminalShopNodeModel.getThresholdVal();
        String[] fzVals = thresholdVal.split("--");
        if (Integer.valueOf(fzVals[1]) <= totalMonthQty) {//分公司审核
            reqParam.put("boundary", Integer.valueOf(fzVals[1]));
        } else {
            reqParam.put("boundary", Integer.valueOf(fzVals[0]));
        }
        return reqParam;
    }



    /**
     * 发送短信
     *
     * @param model  TerminalProtocolModel
     * @param status 状态（1为通过，2为不通过）
     */
    public void sendProtocolMessage(TerminalProtocolModel model, int status) {
        try {
            // 获取用户信息
            Integer createUserId = model.getCreateUser();
            TerminalAccountManagerModel accountManagerModel = terminalAccountManagerDao.selectById(createUserId);
            // 店铺名称
            TerminalShopModel terminalShopModel = terminalShopDao.selectById(model.getTerminalShopId());
            String shopName = terminalShopModel.getShopName();
            Integer type = accountManagerModel.getType();
            // 只给 业务代表 和 客户经理 发送短信
            if (type == 0 || type == 4) {
                boolean flag = false;
                TerminalAccountManagerModel accountManagerModel2 = null;
                // 如果是业务代表，则查询出业务代表关联的客户经理
                if (type == 4) {
                    accountManagerModel2 = terminalAccountManagerDao.selectById(accountManagerModel.getParentId());
                    if (!(accountManagerModel2 == null || accountManagerModel2.getPhone() == null)) {
                        flag = true;
                    }
                }
                // 获取创建时间
                LocalDateTime createTime = model.getCreateTime();
                String yearMonthDay = createTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                // 短信模板："【国台】您好，您于@提交的@终端协议变更已审批通过"
                StringBuilder sb = new StringBuilder();
                sb.append("【国台】您好，您于");
                sb.append(yearMonthDay.substring(0, 4));// 2023-04-15
                sb.append("年");
                sb.append(yearMonthDay.substring(5, 7));
                sb.append("月");
                sb.append(yearMonthDay.substring(8, 10));
                sb.append("日");
                sb.append("提交的");
                sb.append(shopName);
                if (status == 1) {
                    sb.append("终端协议变更已审批通过");
                } else if (status == 2) {
                    sb.append("终端协议变更审批不通过");
                }
                // 发送短信
                if (accountManagerModel.getPhone().length() == 11) {
                    MessageUtil.sendMessageByPhoneAndContent(accountManagerModel.getPhone(), sb.toString());
                    log.info("分销商审核：审核数据（model）：{}，失败。手机号：{}。",
                            model.toString(), accountManagerModel.getPhone());
                }
                if (flag && accountManagerModel2.getPhone().length() == 11) {
                    MessageUtil.sendMessageByPhoneAndContent(accountManagerModel2.getPhone(), sb.toString());
                    log.info("分销商审核：审核数据（model）：{}，失败。手机号：{}。",
                            model.toString(), accountManagerModel2.getPhone());
                }
            } else {
                log.info("该用户类型为：{}，用户id：{}，无需发送短信！审核数据（model）：{}。",
                        accountManagerModel.getType(), accountManagerModel.getId(), model.toString());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    /**
     * 发送短信
     *
     * @param model  TerminalShopInfoScheduleModel
     * @param status 状态（1为通过，2为不通过）
     */
    public void sendMessage(TerminalShopInfoScheduleModel model, int status) {
        try {
            // 获取用户信息
            Integer createUserId = model.getCreateUser();
            TerminalAccountManagerModel accountManagerModel = terminalAccountManagerDao.selectById(createUserId);
            // 店铺名称
            String shopName = model.getShopName();
            Integer type = accountManagerModel.getType();
            // 只给 业务代表 和 客户经理 发送短信
            if (type == 0 || type == 4) {
                boolean flag = false;
                TerminalAccountManagerModel accountManagerModel2 = null;
                // 如果是业务代表，则查询出业务代表关联的客户经理
                if (type == 4) {
                    accountManagerModel2 = terminalAccountManagerDao.selectById(accountManagerModel.getParentId());
                    if (!(accountManagerModel2 == null || accountManagerModel2.getPhone() == null)) {
                        flag = true;
                    }
                }
                // 短信模板："【国台】您好，您采集的终端信息“@”审核不通过。"
                StringBuilder sb = new StringBuilder();
                sb.append("【国台】您好，您采集的终端信息“");
                sb.append(shopName);
                if (status == 1) {
                    sb.append("”审核通过。");
                } else if (status == 2) {
                    sb.append("”审核不通过。");
                }
                // 发送短信
                if (accountManagerModel.getPhone().length() == 11) {
                    MessageUtil.sendMessageByPhoneAndContent(accountManagerModel.getPhone(), sb.toString());
                    log.info("分销商审核：审核数据（model）：{}，失败。手机号：{}。",
                            model.toString(), accountManagerModel.getPhone());
                }
                if (flag && accountManagerModel2.getPhone().length() == 11) {
                    MessageUtil.sendMessageByPhoneAndContent(accountManagerModel2.getPhone(), sb.toString());
                    log.info("分销商审核：审核数据（model）：{}，失败。手机号：{}。",
                            model.toString(), accountManagerModel2.getPhone());
                }
            } else {
                log.info("该用户类型为：{}，用户id：{}，无需发送短信！审核数据（model）：{}。",
                        accountManagerModel.getType(), accountManagerModel.getId(), model.toString());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }



    /**
     * @author: HLQ
     * @Date: 2023/3/5 16:44
     * @Description: 封装请求推送中台的终端信息
     */
    private JSONObject getReqParam(int insertOrUpdate, TerminalShopInfoScheduleModel scheduleModel, TerminalShopContractResp terminalShopContractResp,
                                   String status, List<TerminalProtocolModel> terminalProtocolModelList, TerminalProtocolModel mainProtocolModel) {
        if (Objects.isNull(terminalProtocolModelList)) {
            QueryWrapper<TerminalProtocolModel> queryWrapper = new QueryWrapper<TerminalProtocolModel>()
                    .eq("terminal_shop_id", scheduleModel.getTerminalShopId())
                    .eq("company_id", scheduleModel.getCompanyId())
                    .eq("check_status", 1)
                    .eq("end_status", 2)
                    .eq("delete_status", 0);
            if (Objects.nonNull(mainProtocolModel)) {
                queryWrapper.eq("protocol_type", 1);
            }
            queryWrapper.orderBy(true, true, "protocol_type", "create_time");
            terminalProtocolModelList = terminalProtocolDao.selectList(queryWrapper);
        }
        if (Objects.nonNull(mainProtocolModel)) {
            if (terminalProtocolModelList.isEmpty() || Objects.isNull(terminalProtocolModelList)) {
                terminalProtocolModelList = new ArrayList<>();
            }
            terminalProtocolModelList.add(mainProtocolModel);
        }
        JSONObject rtnJson = new JSONObject();
        JSONObject json = new JSONObject();
        // storecode	String	Y	终端主编码
        json.put("storecode", scheduleModel.getMainCode());
        //上级经销商/分销商信息
        if (Objects.nonNull(scheduleModel.getDistributorId()) && scheduleModel.getDistributorId() != 0) {
            TCloudDealerInfoModel cloudDealerInfoModel = cloudDealerInfoDao.selectById(scheduleModel.getDistributorId());
            json.put("parent_channelcode", cloudDealerInfoModel.getDealerCode());
            json.put("parent_channelname", cloudDealerInfoModel.getDealerName());
        } else if (Objects.nonNull(scheduleModel.getCopartnerId()) && scheduleModel.getCopartnerId() != 0) {
            TCloudDealerInfoModel cloudDealerInfoModel = cloudDealerInfoDao.selectById(scheduleModel.getCopartnerId());
            json.put("parent_channelcode", cloudDealerInfoModel.getDealerCode());
            json.put("parent_channelname", cloudDealerInfoModel.getDealerName());
        } else {
            json.put("parent_channelcode", terminalShopContractResp.getDealerCode());
            json.put("parent_channelname", terminalShopContractResp.getDealerName());
        }
        // 朱鹏说:如果跟换过经销商，副编码会重新生成新的   推送中台时 使用新的编码
        //deputy_code	String	Y	终端副编码
        //2024/01/30 终端不会再次使用新编码 直接用副编码
//        LambdaQueryWrapper<TerminalShopModifyRecordPlus> lqw = Wrappers.lambdaQuery();
//        lqw.eq(TerminalShopModifyRecordPlus::getTerminalShopId, scheduleModel.getTerminalShopId());
//        lqw.eq(TerminalShopModifyRecordPlus::getMemberShopId, scheduleModel.getMemberShopId());
//        lqw.orderByDesc(TerminalShopModifyRecordPlus::getId);
//        List<TerminalShopModifyRecordPlus> terminalShopModifyRecordList = terminalShopModifyRecordMapper.selectList(lqw);
//        if(CollectionUtils.isNotEmpty(terminalShopModifyRecordList)){
//            json.put("deputy_code", terminalShopModifyRecordList.get(0).getNewDeputyCode());
//        }else{
//            json.put("deputy_code", scheduleModel.getDeputyCode());
//        }
        json.put("deputy_code", scheduleModel.getDeputyCode());
        //storename	String	Y	终端名称
        json.put("storename", scheduleModel.getShopName());
        //  contracttype	String	Y	合同类型	合同类型(0:主品合同,1:酱酒合同 2：常规渠道经销合同 3：国台酱酒经销合同 4：专卖店经销合同 5：数智体验中心经销合同 6：团购特约经销合同 7：电商平台经销合同)
        json.put("contracttype", terminalShopContractResp.getContractType() + "");
        // contractcode	String	Y	合同编码
        json.put("contractcode", terminalShopContractResp.getContractCode());
        // contactname	String	Y	终端负责人姓名	存在校验：需在中台配置完成
        json.put("contactname", scheduleModel.getLeaderName());
        // contactphone	String	Y	终端负责人电话
        json.put("contactphone", scheduleModel.getLeaderPhone());
        //tag	Number	Y	终端标签	0:终端关注
        json.put("tag", scheduleModel.getTag() + "");
        //headimg	String	Y	门头照
        json.put("headimg", scheduleModel.getHeadImg());
        //remark String	N	备注
        json.put("remark", com.intelliquor.cloud.shop.job.util.StringUtils.hasNull(scheduleModel.getRemark()) ? scheduleModel.getRemark() : "");

        //province	String	Y	省
        json.put("province", scheduleModel.getProvince());
        //city	String	Y	市
        json.put("city", scheduleModel.getCity());
        //district	String	Y	区
        json.put("district", scheduleModel.getDistrict());
        //address	String	Y	详细地址
        json.put("address", scheduleModel.getAddress());
        //longitude	String	Y	经度
        json.put("longitude", scheduleModel.getLongitude() + "");
        //latitude	String	Y	纬度
        json.put("latitude", scheduleModel.getLatitude() + "");
        // is_image	String	Y	是否形象店	0- 否 1-是
        json.put("is_image", scheduleModel.getIsImage() + "");
        // storecontactname	String	N	店员姓名
        json.put("storecontactname", "");
        // storecontactphone	String	N	店员电话
        json.put("storecontactphone", "");
        //0- 否，无营业执照 1-是,有营业执照
        json.put("is_business_licence", scheduleModel.getWhetherLicense() + "");
        //license_code	String	N	营业执照编号
        json.put("license_code", com.intelliquor.cloud.shop.job.util.StringUtils.hasNull(scheduleModel.getLicenseCode()) ? scheduleModel.getLicenseCode() : "");

        //license_img	String	N	营业执照照片
        json.put("license_img", com.intelliquor.cloud.shop.job.util.StringUtils.hasNull(scheduleModel.getLicenseCode()) ? scheduleModel.getLicenseImg() : "");

        // payment_method	String	N	收款方式
        json.put("payment_method", CodeConstant.RECEIVING_PAYMENT_TYPE_MAP.get(scheduleModel.getReceivingPaymentType()));
        //payee_name	String	N	收款人姓名
        json.put("payee_name", scheduleModel.getReceivingPaymentName());
        //payee_account	String	N	收款人账号
        json.put("payee_account", com.intelliquor.cloud.shop.job.util.StringUtils.hasNull(scheduleModel.getReceivingPaymentAccount()) ? scheduleModel.getReceivingPaymentAccount() : "");
        //deposit_bank	String	N	开户行
        json.put("deposit_bank", com.intelliquor.cloud.shop.job.util.StringUtils.hasNull(scheduleModel.getReceivingPaymentBank()) ? scheduleModel.getReceivingPaymentBank() : "");

        //store_type	String	Y	终端类型 	1-烟酒店、2-专卖店、3-餐饮店、4-商超 5-渠道终端 6-企业终端 7-餐饮终端 8-连锁终端 9-团购终端 10-渠道终端会员 11-连锁终端会员 12-非会员虚拟终端 14-超级终端
        int shopType = scheduleModel.getShopType().intValue();
        String storeType = terminalShopCommonService.generateTerminalTypeByTerminalType(shopType);
        json.put("store_type", storeType);
        // 新终端类型 0:渠道终端 1:餐饮终端 2:团购终端 3:企业终端 4:连锁终端 5会员终端
        json.put("shop_type", shopType + "");
        //status	String	Y	激活状态 0- 未激活 1-已激活
        json.put("status", status);
        //option	String	Y	操作类型	1：新增；2：编辑；3：预采集
        json.put("option", (insertOrUpdate + 1) + "");
        //manager	String	Y	客户经理id
        TerminalAccountManagerModel accountManagerModel = setMangerInfo(scheduleModel.getCreateUser());
        if (Objects.nonNull(accountManagerModel)) {
            json.put("manager", accountManagerModel.getGtId() + "");
        }
        JSONArray gt_store_agreement = new JSONArray();
        for (TerminalProtocolModel terminalProtocolModel : terminalProtocolModelList) {
            JSONObject protocolJson = new JSONObject();
            //type	String	N	协议类型 主协议、附加协议
            protocolJson.put("type", terminalProtocolModel.getProtocolType() + "");
            //protocol_property	String	N 协议名称	陈列包量协议、陈列协议  0, "陈列包量协议",1, "陈列协议"
            protocolJson.put("protocol_property", CodeConstant.PRODUCT_PROTOCOL_TYPE_MAP.get(terminalProtocolModel.getProtocolProperty()));
            //productname	String	N	协议产品名称   国标、酱酒  1:国台国标 2:国台酱酒
            protocolJson.put("productname", CodeConstant.PROTOCOL_PRODUCT_TYPE_MAP.get(terminalProtocolModel.getProductType()));
            //storelevel  Number	N	终端等级
            /* TerminalShopLevelModel terminalShopLevelModel = terminalShopLevelDao.selectById(terminalProtocolModel.getLevelCode());*/
            protocolJson.put("storelevel", terminalProtocolModel.getLevelCode());
            //display_image	String	N	陈列协议照片
            protocolJson.put("display_image", terminalProtocolModel.getProtocolImage());
            //createtime	String	N	创建时间
            String stringYYYYMMdd = DateUtils.convert2StringYYYYMMddHHmmss(new Date());
            if (Objects.nonNull(terminalProtocolModel.getCreateTime())) {
                LocalDateTime createTime = terminalProtocolModel.getCreateTime();
                DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String dateStr = createTime.format(fmt);
                stringYYYYMMdd = dateStr;
                protocolJson.put("createtime", dateStr);
            } else {
                protocolJson.put("createtime", stringYYYYMMdd);
            }
            //updatetime	String	N	修改时间
            if (Objects.nonNull(terminalProtocolModel.getUpdateTime())) {
                LocalDateTime updateTime = terminalProtocolModel.getUpdateTime();
                DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String dateStr = updateTime.format(fmt);
                protocolJson.put("updatetime", dateStr);
            } else {
                protocolJson.put("updatetime", stringYYYYMMdd);
            }
            //添加到集合
            gt_store_agreement.add(protocolJson);
        }
        rtnJson.put("gt_store", json);
        rtnJson.put("gt_store_agreement", gt_store_agreement);
        return rtnJson;
    }



    /**
     * 以前中台审批的接口
     */
    @Deprecated
    public void gtAuditDataByShopId_old(Integer shopId,
                                        Integer status,
                                        String gtAuditResult,
                                        HttpServletRequest httpServletRequest) {

        //先走外部接口验证 验证没问题才可以继续走
        String token = httpServletRequest.getHeader("token");
        String timestampStr = httpServletRequest.getHeader("timestamp");
        GuotaiUtil.verifyToken(token, timestampStr);

        //国台要激活 先根据终端店id查询数据 然后拼接成req参数类
        TerminalShopReq terminalShopReq = disposeParamTerminalShopReq(shopId, 1,"");

        //把国台的状态赋值一下
        terminalShopReq.setStatus(status);
        //把国台审核的结果赋值一下
        terminalShopReq.setGtAuditResult(gtAuditResult);

        //校验是否已经存在的接口
        if (1 == terminalShopReq.getStatus()) {
            judgeVerify(terminalShopReq, 0);
        }

        //处理编码参数
        disposeMainCodeAndDeputyCode(terminalShopReq);

        //必须是审核通过的状态才可以 激活终端
        if (1 == terminalShopReq.getStatus()) {
            //添加到终端表和终端用户表
            Integer mShopId = insertMemberShopAndMemberUser(terminalShopReq);

            //把联盟终端店id添加到请求类
            terminalShopReq.setMemberShopId(mShopId);
        }

        //转换成添加用的类
        TerminalShopModel updateTerminalShopData = getTerminalShopModel(terminalShopReq);

        //如果终端名和手机号没有问题 就直接更新
        terminalShopDao.update(updateTerminalShopData, new QueryWrapper<TerminalShopModel>().eq("id", updateTerminalShopData.getId()));

        //先删除合同以及下级产品
        terminalShopContractDao.deleteByShopId(updateTerminalShopData.getId());

        //添加合同以及协议产品
        insertContract(terminalShopReq, updateTerminalShopData.getId());

        //必须是审核通过才同步中台
        if (1 == terminalShopReq.getStatus()) {
            sendTerminalData(terminalShopReq);
        }
    }


    /**
     * @author: HLQ
     * @Date: 2023/3/1 15:22
     * @Description: 处理主副编码
     */
    private void handleMainDeputyCode(TerminalShopInfoScheduleModel scheduleModel, TerminalShopModel shopModel) {
        //如果营业执照为空 不允许生成编码
        if (StringUtils.isNotBlank(scheduleModel.getLicenseCode()) && !Objects.isNull(shopModel)) {
            //看看主编码或者副编码是否为空
            if (StringUtils.isBlank(scheduleModel.getMainCode()) || StringUtils.isBlank(scheduleModel.getDeputyCode())) {
                //根据营业执照编号去查询有没有这个主编码
                String mainCode = shopDao.selectTerminalCodeByLicenseCode(scheduleModel.getLicenseCode());

                //副编码生成
                String deputyCode = makeTerminalCode();

                //如果主编码为空 说明营业执照没有被用过 用副编码
                if (StringUtils.isBlank(mainCode)) {
                    mainCode = deputyCode;
                }
                //转换实体类
                shopModel.setMainCode(mainCode);
                shopModel.setDeputyCode(deputyCode);
                scheduleModel.setMainCode(mainCode);
                scheduleModel.setDeputyCode(deputyCode);
            } else {
                //不等于空就用已经生成的
                scheduleModel.setMainCode(shopModel.getMainCode());
                scheduleModel.setDeputyCode(shopModel.getDeputyCode());
            }
        } else {
            //如果为空 说明选择的是没有营业执照 不去数据库查询
            //副编码生成
            String deputyCode = makeTerminalCode();
            //转换实体类
            shopModel.setMainCode(deputyCode);
            shopModel.setDeputyCode(deputyCode);
            scheduleModel.setMainCode(deputyCode);
            scheduleModel.setDeputyCode(deputyCode);
        }
    }


    @Override
    public GtOpenTerminalShopAuditResp selectAuditDataByShopId(Integer shopId, Integer terminalProtocolId, HttpServletRequest httpServletRequest, Integer status) {
        //先走外部接口验证 验证没问题才可以继续走
        String token = httpServletRequest.getHeader("token");
        String timestampStr = httpServletRequest.getHeader("timestamp");
        System.out.println("token=" + token + ";;timestampStr==" + timestampStr);
        GuotaiUtil.verifyToken(token, timestampStr);

        //然后查询并且返回
        GtOpenTerminalShopAuditResp resultData;
        // 查询newData
        QueryWrapper<TerminalDataLogModel> queryWrapper = new QueryWrapper<>();
        if (terminalProtocolId != null && terminalProtocolId != 0) {
            // 有协议则在t_terminal_shop查找
            resultData = terminalShopDao.selectAuditData(shopId);
            queryWrapper.eq("terminal_shop_id", shopId);
        } else {
            // 无则在t_terminal_shop_info_schedule查找
            resultData = terminalShopDao.selectAuditDataByShopId(shopId);
            queryWrapper.eq("schedule_shop_id", shopId);
        }
        queryWrapper.eq(terminalProtocolId != null && terminalProtocolId != 0, "protocol_id", terminalProtocolId);
        queryWrapper.orderByDesc("create_time");
        List<TerminalDataLogModel> dataLogModelList = terminalDataLogDao.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(dataLogModelList)) {
            resultData.setRawData(dataLogModelList.get(0).getRawData());
        }

        List<TerminalProtocolResp> protocolList = terminalProtocolChangeDao.selectTerminalProtocolListByShopId(resultData.getTerminalShopId(), 0);
        if (CollectionUtils.isNotEmpty(protocolList)) {
            resultData.setProtocolList(protocolList);
        }
        //处理协议产品
        // gtDisposeSubordinate(resultData);

        // 查询相似终端，status为 1 时才需要查询
        //if (status != null && status == 1) {
        SimilarTerminalReq req = new SimilarTerminalReq(); // 查询条件
        req.setShopId(shopId);
        req.setShopName(resultData.getShopName());
        req.setLeaderName(resultData.getLeaderName());
        req.setLeaderPhone(resultData.getLeaderPhone());
        req.setProvince(resultData.getProvince());
        req.setCity(resultData.getCity());
        req.setAddress(resultData.getAddress());
        req.setDealerName(resultData.getDealerName());
        req.setContractCode(resultData.getContractCode());
        List<SimilarTerminalResp> similarTerminalList = new ArrayList<>();
        try {
            similarTerminalList = terminalShopDao.selectSimilarTerminal(req); // 查询相似终端
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        resultData.setSimilarTerminalList(similarTerminalList);
        // }
        // 调用顾问团接口获取商品列表（根据经销商所签的合同类型获取该合同类型对应的产品列表信息）
        JSONObject jsonObject = new JSONObject();
        try {
            MapUtils goodsParam = new MapUtils();
            goodsParam.put(ColumnConstant.DEALER_CODE, resultData.getDealerCode())
                    .put("contractType", resultData.getContractType())
                    .put(ColumnConstant.PAGE, 1)
                    .put(ColumnConstant.LIMIT, 50);
            JSONObject jsonObjectModel = selectLeaderUpGoodsList(goodsParam, gtCompanyId+"");
            jsonObject = jsonObjectModel.getJSONObject(ColumnConstant.RESULT);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        resultData.setGoods(jsonObject);
        //返回数据
        return resultData;
    }


    /**
     * 功能描述: 商品上下架列表
     *
     * @param paramMap  参数内容
     * @param companyId 公司id
     * @return void
     * @auther: tms
     * @date: 2021/01/21 16:00
     */
    public JSONObject selectLeaderUpGoodsList(MapUtils paramMap, String companyId) {
        paramMap.put(ColumnConstant.USER_ID, companyId);
        for (Object key : paramMap.keySet()) {
            paramMap.put(key, paramMap.get(key) == null ? null : String.valueOf(paramMap.get(key)));
        }

        String reqUrl = drinkAdvisoryMgrApi + "/data-api/leaderDealer/goods/selectLeaderUpGoodsList";

        // 请求头内容
        MapUtils headMap = new MapUtils(ColumnConstant.AUTH_LOCAL, createAuthLocal(Integer.parseInt(companyId)))
                .put(ColumnConstant.USER_ID, companyId);

        // 发送请求获取数据
        String result = HttpUtils.sendGet(reqUrl, paramMap, headMap);

        log.info("InternalService | queryGoodsList | result:{}", result);

        // 返回结果解析
        JSONObject jsonObject = JSON.parseObject(result);

        if (jsonObject != null && HttpStatus.OK.value() == jsonObject.getInteger(ColumnConstant.CODE).intValue()) {
            return jsonObject;
        }

        return new JSONObject();
    }

    /**
     * 功能描述: 创建内容使用authLocal
     * Base64(companyId-当前时间戳)
     *
     * @param companyId 公司id
     * @return java.lang.String
     * @auther: tms
     * @date: 2021/01/21 16:02
     */
    private static String createAuthLocal(Integer companyId) {
        String encodeStr = companyId + "-" + TimeUtilis.getTimeStamp();
        return Base64.encode(encodeStr.getBytes());
    }


    /**
     * 校验接口 看看手机号和终端店名是不是重复了
     * insertOrUpdate 是添加调的还是更新调的 0是激活 1是更新
     */
    public void judgeVerify(TerminalShopReq terminalShopReq, Integer insertOrUpdate) {

        //终端名称需要做重复校验 大于0说明已经存在
        if (0 == insertOrUpdate) {
            if (terminalShopDao.selectCount(new QueryWrapper<TerminalShopModel>()
                    .eq("shop_name", terminalShopReq.getShopName())
                    .eq("is_delete", 0)
                    .ne("id", (terminalShopReq.getId() == null ? 0 : terminalShopReq.getId()))) > 0) {
                throw new BusinessException("400", "该终端店名称已经存在,建议名称后增加街道信息");
            }

            if (shopDao.countByShopName(terminalShopReq.getShopName()) > 0) {
                throw new BusinessException("400", "联盟该终端店名称已经存在,建议名称后增加街道信息");
            }
            //如果是更新 不判断自己
        } else if (1 == insertOrUpdate) {
            if (terminalShopDao.selectCount(new QueryWrapper<TerminalShopModel>()
                    .eq("shop_name", terminalShopReq.getShopName())
                    .eq("is_delete", 0)
                    .ne("id", terminalShopReq.getId())
            ) > 0) {
                throw new BusinessException("400", "该终端店名称已经存在,建议名称后增加街道信息");
            }

            if (shopDao.countByShopNameByShopId(terminalShopReq.getShopName(), terminalShopReq.getMemberShopId()) > 0) {
                throw new BusinessException("400", "联盟该终端店名称已经存在,建议名称后增加街道信息");
            }
        }

        //需要验证负责人手机号是不是已经注册过合伙人之类的号
        if (StringUtils.isNotBlank(terminalShopReq.getLeaderPhone())) {
            if (shopUserDao.selectAccountStatusByPhone(terminalShopReq.getLeaderPhone()) > 0) {
                throw new BusinessException("400", "该负责人的手机号已经注册过除终端以外其他类型账户");
            }
        }

        //然后验证店员手机号是不是已经注册过合伙人之类的号
        if (StringUtils.isNotBlank(terminalShopReq.getKeeperPhone())) {
            if (shopUserDao.selectAccountStatusByPhone(terminalShopReq.getKeeperPhone()) > 0) {
                throw new BusinessException("400", "该店员的手机号已经注册过除终端以外其他类型账户");
            }
        }
    }



    public void insertContract(TerminalShopReq terminalShopReq, Integer shopId) {
        //看看主合同是不是空 主合同不为空并且合同编码不为空
        if (null != terminalShopReq.getPrimaryContract() && StringUtils.isNotBlank(terminalShopReq.getPrimaryContract().getContractCode())) {
//            //先拼装一下 因为2022-12-21把采集改了 经销商合同和协议不在同一个地方
//            terminalShopReq.getPrimaryContract().setDisplayImage(terminalShopReq.getDisplayImage());
//            terminalShopReq.getPrimaryContract().setPackageQuantityImage(terminalShopReq.getPackageQuantityImage());

            //把合同信息转换成添加用的类
            TerminalShopContractModel insertTerminalShopContractData = getTerminalShopContractModel(terminalShopReq.getPrimaryContract());
            //填入采集终端表的主键id
            insertTerminalShopContractData.setTerminalShopId(shopId);
            //填入联盟终端表的主键id
            insertTerminalShopContractData.setMemberShopId(terminalShopReq.getMemberShopId());

//            //2023-01-06新增 包量和陈列的数据
//            //包量的产品名称
//            insertTerminalShopContractData.setPackageQuantityName(terminalShopReq.getPackageQuantityName());
//            //包量的陈列面数量
//            insertTerminalShopContractData.setPackageQuantityDisplaySurface(terminalShopReq.getPackageQuantityDisplaySurface());
//            //包量的陈列费用
//            insertTerminalShopContractData.setPackageQuantityDisplayCost(terminalShopReq.getPackageQuantityDisplayCost());
//            //包量的全年进货量
//            insertTerminalShopContractData.setPackageQuantityReplenishStockQuantity(terminalShopReq.getPackageQuantityReplenishStockQuantity());
//
//            //陈列的产品名称
//            insertTerminalShopContractData.setDisplayName(terminalShopReq.getDisplayName());
//            //陈列的陈列面数量
//            insertTerminalShopContractData.setDisplayDisplaySurface(terminalShopReq.getDisplayDisplaySurface());
//            //陈列的陈列费用
//            insertTerminalShopContractData.setDisplayDisplayCost(terminalShopReq.getDisplayDisplayCost());
//            //陈列的每月进货量
//            insertTerminalShopContractData.setDisplayReplenishStockQuantity(terminalShopReq.getDisplayReplenishStockQuantity());
//
//            //2023-01-16修改协议产品类型结构
//            //陈列面(不少于)
//            insertTerminalShopContractData.setDisplaySurface(terminalShopReq.getDisplaySurface());
//            //月进货数(箱)
//            insertTerminalShopContractData.setMonthScanInNum(terminalShopReq.getMonthScanInNum());
//            //年进货数(箱)
//            insertTerminalShopContractData.setYearScanInNum(terminalShopReq.getYearScanInNum());
//            //陈列奖励(分/月)
//            insertTerminalShopContractData.setDisplayAmount(terminalShopReq.getDisplayAmount());
//            //年度包量奖励(箱)
//            insertTerminalShopContractData.setPackageAmount(terminalShopReq.getPackageAmount());

            //插入数据
            terminalShopContractDao.insert(insertTerminalShopContractData);

//            //如果包量协议产品不为空 就去批量添加
//            if (null != terminalShopReq.getPackageQuantityAgreementProductList() && terminalShopReq.getPackageQuantityAgreementProductList().size() > 0) {
//                terminalShopContractProductDao.insertBatchTerminalShopContractProduct(terminalShopReq.getPackageQuantityAgreementProductList(), insertTerminalShopContractData.getId());
//            }
//
//            //如果陈列协议产品不为空 就去批量添加
//            if (null != terminalShopReq.getDisplayAgreementProductList() && terminalShopReq.getDisplayAgreementProductList().size() > 0) {
//                terminalShopContractProductDao.insertBatchTerminalShopContractProduct(terminalShopReq.getDisplayAgreementProductList(), insertTerminalShopContractData.getId());
//            }
        }
    }




    /**
     * @param oldShopModel terminal_shop表的终端的信息
     * @author: HLQ
     * @Date: 2023/3/15 9:55
     * @Description: 通过附属表的数据
     */
    public Integer insertHandleMemberShopAndMemberUser(TerminalShopInfoScheduleModel scheduleModel,
                                                       TerminalShopContractResp terminalShopContractResp,
                                                       TerminalProtocolModel terminalProtocolModel,
                                                       TerminalShopModel oldShopModel,
                                                       boolean createTgFlag,
                                                       Set<String> redisKeyList) {
        //创建终端店实体类
        ShopModel shopModel = getShopModelByTerminalShopInfoScheduleModel(scheduleModel, terminalShopContractResp, terminalProtocolModel);

        //1.添加终端店   t_member_shop中存在时，修改；否则新增
        if (scheduleModel.getMemberShopId().intValue() > 0) {//更新
            if (!createTgFlag) {
                shopModel.setIsPrepare(1);
            } else {
                shopModel.setIsPrepare(0);
            }
            shopDao.updateMemberShop(shopModel);
        } else { // 新增
            if (isReidsJudge) {
                String MEMBER_SHOP_INC_REDIS_KEY = "T_MEMBER_SHOP_INC_" + shopModel.getName();
                String MEMBER_SHOP_INC_VAL_REDIS_KEY = "T_MEMBER_SHOP_INC_VAL_" + shopModel.getName();
                long incNum1 = redisTemplate.boundValueOps(MEMBER_SHOP_INC_REDIS_KEY).increment();
                redisKeyList.add(MEMBER_SHOP_INC_REDIS_KEY);
                redisKeyList.add(MEMBER_SHOP_INC_VAL_REDIS_KEY);
                if (incNum1 > 1l) {//存在说明重复插入
                    Object idStr = redisTemplate.opsForValue().get(MEMBER_SHOP_INC_VAL_REDIS_KEY);
                    TerminalRepeatModel terminalRepeatModel = new TerminalRepeatModel();
                    terminalRepeatModel.setTableName("T_MEMBER_SHOP_" + incNum1);
                    terminalRepeatModel.setCreateTime(new Date());
                    terminalRepeatModel.setTerminalShopId(scheduleModel.getTerminalShopId());
                    if (Objects.nonNull(idStr)) {
                        terminalRepeatModel.setTableId(Integer.valueOf(idStr.toString()));
                        shopModel.setId(terminalRepeatModel.getTableId());
                    } else {
                        throw new BusinessException("400", "数据有误0");
                    }
                    terminalRepeatModel.setDataJson(JSONObject.toJSONString(shopModel));
                    shopDao.insertTerminalRepeat(terminalRepeatModel);
                } else {
                    if (!createTgFlag) {
                        shopModel.setIsPrepare(1);
                    }
                    Integer isHighMember = scheduleModel.getIsHighMember();
                    if (isHighMember == 1) {
                        shopModel.setIsMember(2);
                    }
                    shopDao.insertValueColumn(shopModel);
                    redisTemplate.opsForValue().setIfAbsent(MEMBER_SHOP_INC_VAL_REDIS_KEY, shopModel.getId() + "", timeOut, timeUnit);
                }
            } else {
                if (!createTgFlag) {
                    shopModel.setIsPrepare(1);
                }
                Integer isHighMember = scheduleModel.getIsHighMember();
                if (isHighMember == 1) {
                    shopModel.setIsMember(2);
                }
                shopDao.insertValueColumn(shopModel);
            }
        }

        //2、添加终端店顺便生成团购客户推送到中台
        if (createTgFlag) {
            insertTgData(shopModel, terminalShopContractResp, oldShopModel.getShopName(), redisKeyList);
        }

        //3.之后创建终端店在dealer_info里面的分销商信息
        Integer distributeId = disposeCloudDealer(shopModel, redisKeyList, scheduleModel.getTerminalShopId());

        //3.处理终端店和经销商表的关联关系
        if (distributeId != 0) {
            disposeCloudDealerRelationNew(distributeId, scheduleModel, terminalShopContractResp);
        }

        //先判断当前手机号有没有创建 如果有 就不创建新的 而是把以前的用户信息和新终端店的信息放到关联关系表
        ShopUserModel leaderModel = shopUserDao.getUserByPhone(scheduleModel.getLeaderPhone());

        //判断是否跟换手负责人机号  跟换时，删除原手机号的关联关系
        if (!scheduleModel.getLeaderPhone().equals(oldShopModel.getLeaderPhone())) {
            MemberShopUserRelationModel relationModel = new MemberShopUserRelationModel();
            //终端id
            relationModel.setShopId(oldShopModel.getMemberShopId());
            //用户手机号
            relationModel.setUserPhone(oldShopModel.getLeaderPhone());
            memberShopUserRelationDao.deleteByShopIdAndPhone(relationModel);
        }
        //没有这个手机号 创建新用户 有的话下面直接拿查询出来的信息去添加关联关系
        if (null == leaderModel) {
            //添加终端负责人用户
            leaderModel = new ShopUserModel();
            //添加人员名称
            leaderModel.setName(scheduleModel.getLeaderName());
            //添加人员手机号
            leaderModel.setPhone(scheduleModel.getLeaderPhone());
            //作为主账户
            leaderModel.setPrimaryAccount(1);
            //人员密码
            leaderModel.setUserPassword(SignUtils.md5(userLoginPwd, SIGN_KEY));
            //人员所属终端店
            leaderModel.setShopId(shopModel.getId());
            //添加
            if (isReidsJudge) {
                String MEMBER_USER_INC_REDIS_KEY = "T_MEMBER_USER_INC_" + leaderModel.getPhone();
                String MEMBER_USER_INC_VAL_REDIS_KEY = "T_MEMBER_USER_INC_VAL_" + leaderModel.getPhone();
                redisKeyList.add(MEMBER_USER_INC_REDIS_KEY);
                redisKeyList.add(MEMBER_USER_INC_VAL_REDIS_KEY);
                long incNum2 = redisTemplate.boundValueOps(MEMBER_USER_INC_REDIS_KEY).increment();
                if (incNum2 > 1l) {//存在说明重复插入
                    Object idStr = redisTemplate.opsForValue().get(MEMBER_USER_INC_VAL_REDIS_KEY);
                    TerminalRepeatModel terminalRepeatModel = new TerminalRepeatModel();
                    terminalRepeatModel.setTableName("T_MEMBER_USER_" + incNum2);
                    terminalRepeatModel.setCreateTime(new Date());
                    terminalRepeatModel.setTerminalShopId(scheduleModel.getTerminalShopId());
                    if (Objects.nonNull(idStr)) {
                        terminalRepeatModel.setTableId(Integer.valueOf(idStr.toString()));
                        leaderModel.setId(terminalRepeatModel.getTableId());
                    } else {
                        throw new BusinessException("400", "数据有误1");
                    }
                    terminalRepeatModel.setDataJson(JSONObject.toJSONString(leaderModel));
                    shopDao.insertTerminalRepeat(terminalRepeatModel);
                } else {
                    ShopUserModel userByMobile = shopUserDao.getUserByMobile(leaderModel.getPhone());
                    if (Objects.nonNull(userByMobile)) {
                        TerminalRepeatModel terminalRepeatModel = new TerminalRepeatModel();
                        terminalRepeatModel.setTableName("T_MEMBER_USER_" + leaderModel.getPhone());
                        terminalRepeatModel.setCreateTime(new Date());
                        terminalRepeatModel.setTerminalShopId(scheduleModel.getTerminalShopId());
                        terminalRepeatModel.setDataJson(JSONObject.toJSONString(userByMobile));
                        shopDao.insertTerminalRepeat(terminalRepeatModel);
                        leaderModel.setId(userByMobile.getId());
                    } else {
                        shopUserDao.insert(leaderModel);
                    }
                    redisTemplate.opsForValue().setIfAbsent(MEMBER_USER_INC_VAL_REDIS_KEY, leaderModel.getId() + "", timeOut, timeUnit);
                }
            } else {
                ShopUserModel userByMobile = shopUserDao.getUserByMobile(leaderModel.getPhone());
                if (Objects.nonNull(userByMobile)) {
                    TerminalRepeatModel terminalRepeatModel = new TerminalRepeatModel();
                    terminalRepeatModel.setTableName("T_MEMBER_USER_" + leaderModel.getPhone());
                    terminalRepeatModel.setCreateTime(new Date());
                    terminalRepeatModel.setTerminalShopId(scheduleModel.getTerminalShopId());
                    terminalRepeatModel.setDataJson(JSONObject.toJSONString(userByMobile));
                    shopDao.insertTerminalRepeat(terminalRepeatModel);
                    leaderModel.setId(userByMobile.getId());
                } else {
                    shopUserDao.insert(leaderModel);
                }
            }
            //如果有这个手机号 说明需要把他的名字同步更新成最后一次提交的名字
        } else {
            leaderModel.setName(scheduleModel.getLeaderName());
            //更新联盟用户表和联盟终端表名字
            shopUserDao.updateNameByUserId(scheduleModel.getLeaderName(), leaderModel.getId());
        }

        //4.处理联盟终端和终端用户关系 插入到关联关系表中
        if (shopModel.getId() != 0) {
            insertMemberShopUserRelation(shopModel.getId(), leaderModel.getId(), leaderModel.getPhone());
        }

        //5.处理国台终端申请信息表 用来保持dealerCode永不重复
        saveTerminalApply(shopModel, terminalShopContractResp, redisKeyList);

        //判断负责人和店员是不是一个手机号 同一个手机号说明只需要添加一次 不同需要再次添加一个
        if (StringUtils.isNotBlank(scheduleModel.getKeeperPhone()) &&
                StringUtils.isNotBlank(scheduleModel.getKeeperName()) &&
                !scheduleModel.getKeeperPhone().equals(scheduleModel.getLeaderPhone())) {
            //先查询一下是否有这个终端店员
            ShopUserModel keeperModel = shopUserDao.getUserByPhone(scheduleModel.getKeeperPhone());
            //如果没有这个终端店员 就去添加新加 有的话用之前的信息绑定
            if (null == keeperModel) {
                keeperModel = new ShopUserModel();
                //添加人员名称
                keeperModel.setName(scheduleModel.getKeeperName());
                //添加人员手机号
                keeperModel.setPhone(scheduleModel.getKeeperPhone());
                //人员密码
                keeperModel.setUserPassword(SignUtils.md5(userLoginPwd, SIGN_KEY));
                //作为主账户
                keeperModel.setPrimaryAccount(0);
                //人员所属终端店
                keeperModel.setShopId(shopModel.getId());
                //添加
                if (isReidsJudge) {
                    String MEMBER_USER_INC_REDIS_KEY = "T_MEMBER_USER_INC_1_" + keeperModel.getPhone();
                    String MEMBER_USER_INC_VAL_REDIS_KEY = "T_MEMBER_USER_INC_VAL_1_" + keeperModel.getPhone();
                    long incNum3 = redisTemplate.boundValueOps(MEMBER_USER_INC_REDIS_KEY).increment();
                    redisKeyList.add(MEMBER_USER_INC_REDIS_KEY);
                    redisKeyList.add(MEMBER_USER_INC_VAL_REDIS_KEY);
                    if (incNum3 > 1l) {//存在说明重复插入
                        Object idStr = redisTemplate.opsForValue().get(MEMBER_USER_INC_VAL_REDIS_KEY);
                        TerminalRepeatModel terminalRepeatModel = new TerminalRepeatModel();
                        terminalRepeatModel.setTableName("T_MEMBER_USER_02_" + incNum3);
                        terminalRepeatModel.setCreateTime(new Date());
                        terminalRepeatModel.setTerminalShopId(scheduleModel.getTerminalShopId());
                        if (Objects.nonNull(idStr)) {
                            terminalRepeatModel.setTableId(Integer.valueOf(idStr.toString()));
                            keeperModel.setId(terminalRepeatModel.getTableId());
                        } else {
                            throw new BusinessException("400", "数据有误2");
                        }
                        terminalRepeatModel.setDataJson(JSONObject.toJSONString(keeperModel));
                        shopDao.insertTerminalRepeat(terminalRepeatModel);

                    } else {
                        shopUserDao.insert(keeperModel);
                        redisTemplate.opsForValue().setIfAbsent(MEMBER_USER_INC_VAL_REDIS_KEY, keeperModel.getId() + "", timeOut, timeUnit);
                    }
                } else {
                    shopUserDao.insert(keeperModel);
                }
                //如果有这个 店员只修改自己member_user的名字
            } else {
                keeperModel.setName(scheduleModel.getKeeperName());
                shopUserDao.update(keeperModel);
            }
            //处理联盟终端和终端用户关系 插入到关联关系表中
            if (keeperModel.getId() != 0) {
                insertMemberShopUserRelation(shopModel.getId(), keeperModel.getId(), keeperModel.getPhone());
            }
        }
        //返回终端店id
        return shopModel.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void terminalDisableTask() {
        //查询超过30天的启用终端
        List<TerminalShopModel> terminalShopList = terminalShopDao.selectTerminalDisableTask();
        for (TerminalShopModel terminalShopModel : terminalShopList) {
            //筛选无历史进货数据
            Boolean finishStatus = shopDealerOrderService.getExistOrderFinish(terminalShopModel.getMemberShopId().longValue());
            if (Boolean.TRUE.equals(finishStatus)) {
                continue;
            }
            //无启用记录的才禁用
            LambdaQueryWrapper<TerminalShopDisableRecordCommonModel> notEnableExistQuery = Wrappers.lambdaQuery();
            notEnableExistQuery.eq(TerminalShopDisableRecordCommonModel::getTerminalShopId, terminalShopModel.getId());
            notEnableExistQuery.eq(TerminalShopDisableRecordCommonModel::getTerminalStatus, 0);
            List<TerminalShopDisableRecordCommonModel> list = terminalShopDisableRecordService.list(notEnableExistQuery);
            if (CollectionUtils.isNotEmpty(list)) {
                continue;
            }
            log.info("终端【{}】超过30天未进货,自动禁用", terminalShopModel.getShopName());
            //记录操作日志
            TerminalShopDisableRecordCommonModel terminalShopDisableRecord = new TerminalShopDisableRecordCommonModel();
            terminalShopDisableRecord.setTerminalShopId(terminalShopModel.getId().longValue());
            terminalShopDisableRecord.setMemberShopId(terminalShopModel.getMemberShopId().longValue());
            terminalShopDisableRecord.setTerminalStatus(TerminalShopStatusEnum.STATUS_DISABLE.getStatus());
            terminalShopDisableRecord.setCreateUserId(0L);
            terminalShopDisableRecord.setCreateUserName("系统");
            terminalShopDisableRecord.setCreateUserPhone("10000000000");
            terminalShopDisableRecord.setCheckUserId(0L);
            terminalShopDisableRecord.setCheckUserName("系统");
            terminalShopDisableRecord.setCheckUserPhone("10000000000");
            terminalShopDisableRecord.setCheckStatus(1);
            terminalShopDisableRecord.setCreateTime(new Date());
            terminalShopDisableRecord.setUpdateTime(new Date());
            terminalShopDisableRecord.setRemark("系统任务-自动禁用终端【" + terminalShopModel.getShopName() + "】," +
                    "操作人【系统】,审核人【系统】");
            terminalShopDisableRecordService.save(terminalShopDisableRecord);
            //禁用终端
            shopDao.updateMemberShopStatus(terminalShopModel.getMemberShopId().longValue(), MemberShopStatusEnum.STATUS_DISABLE.getStatus());
        }
    }


    /**
     * @author: HLQ
     * @Date: 2023/3/15 14:30
     * @Description: 生成团购数据
     */
    private void insertTgData(ShopModel shopModel, TerminalShopContractResp terminalShopContractResp, String shopName, Set<String> redisKeyList) {
        Integer tgId = memberShopDao.getMemberShopIdByName("团购客户" + shopName);
        ShopModel tgShopModel = new ShopModel();
        if (Objects.nonNull(tgId)) {
            tgShopModel.setId(tgId);
            //店铺名称
            tgShopModel.setName("团购客户" + shopModel.getName());
            //联系人
            tgShopModel.setLinkman("团购客户" + shopModel.getLinkman());
            tgShopModel.setDealerCode(ztUtils.makeTerminalCodeTwo());
            //上级的编码 这里是上级终端店的编码
            tgShopModel.setParentDealerCode(shopModel.getDealerCode());
            shopDao.updateMemberShop(tgShopModel);
        } else {
            //店铺名称
            tgShopModel.setName("团购客户" + shopModel.getName());
            //联系人
            tgShopModel.setLinkman("团购客户" + shopModel.getLinkman());
            //创建时间
            tgShopModel.setCreateTime(new Date());
            //状态
            tgShopModel.setStatus(0);
            //公司id
            tgShopModel.setCompanyId(shopModel.getCompanyId());
            //更新时间
            tgShopModel.setUpdateTime(new Date());
            //自己的编码 合伙人没有diyCode 记在dealerCode
            tgShopModel.setDealerCode(ztUtils.makeTerminalCodeTwo());
            //主编码 填入自己的副编码
            tgShopModel.setMainCode(tgShopModel.getDealerCode());
            //自己的名称
            tgShopModel.setDealerName("采集终端店团购");
            //上级的编码 这里是上级终端店的编码
            tgShopModel.setParentDealerCode(shopModel.getDealerCode());
            //添加团购客户
            if (isReidsJudge) {
                String MEMBER_SHOP_REDIS_KEY = "T_MEMBER_SHOP_TG_" + tgShopModel.getDealerName() + "__" + tgShopModel.getName();
                redisKeyList.add(MEMBER_SHOP_REDIS_KEY);
                if (redisTemplate.hasKey(MEMBER_SHOP_REDIS_KEY)) {//存在说明重复插入
                    Object idStr = redisTemplate.opsForValue().get(MEMBER_SHOP_REDIS_KEY);
                    TerminalRepeatModel terminalRepeatModel = new TerminalRepeatModel();
                    terminalRepeatModel.setTableName("T_MEMBER_SHOP_TG");
                    terminalRepeatModel.setCreateTime(new Date());
                    terminalRepeatModel.setTerminalShopId(terminalShopContractResp.getTerminalShopId());
                    if (Objects.nonNull(idStr)) {
                        terminalRepeatModel.setTableId(Integer.valueOf(idStr.toString()));
                        tgShopModel.setId(terminalRepeatModel.getTableId());
                    } else {
                        throw new BusinessException("400", "数据有误3");
                    }
                    terminalRepeatModel.setDataJson(JSONObject.toJSONString(tgShopModel));
                    shopDao.insertTerminalRepeat(terminalRepeatModel);
                } else {
                    shopDao.insertValueColumn(tgShopModel);
                    redisTemplate.opsForValue().setIfAbsent(MEMBER_SHOP_REDIS_KEY, tgShopModel.getId() + "", timeOut, timeUnit);
                }
            } else {
                shopDao.insertValueColumn(tgShopModel);
            }
        }

        //发送团购客户至中台
        TogetherClientModel togetherShoppingApplyModel = new TogetherClientModel();
        //经销商编码 一定是经销商
        togetherShoppingApplyModel.setChannelCode(terminalShopContractResp.getDealerCode());
        //玄武要求终端团购客户用2
        togetherShoppingApplyModel.setType("2");
        //团购类型
        togetherShoppingApplyModel.setTeamType("2");
        //分销商编码 也就是自己的编码
        togetherShoppingApplyModel.setDistributionCode(tgShopModel.getDealerCode());
        //分销商子编码 也就是自己的编码
        togetherShoppingApplyModel.setDeputyCode(tgShopModel.getDealerCode());
        //分销商名称
        togetherShoppingApplyModel.setDistributionApellation("团购客户" + shopModel.getName());
        //分销商姓名
        togetherShoppingApplyModel.setDistributionName("团购客户" + shopModel.getLinkman());
        togetherShoppingApplyModel.setHigherType("2");
        //上级编码
        togetherShoppingApplyModel.setHigherCode(shopModel.getDealerCode());

        //同步到中台
        log.info("开始将采集团购客户提报到中台，注意，后面的日志写的是分销商，因为团购客户和分销商同步用的是一个接口");
        try {
            ztUtils.sendTogetherClient(togetherShoppingApplyModel);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("400", "同步团购客户数据失败:" + e.getMessage());
        }
    }


    /**
     * @Description: 终端数据激活审批成功后新增终端用户表数据
     */
    public Integer insertMemberShopAndMemberUser(TerminalShopReq terminalShopReq) {
        //创建终端店实体类
        ShopModel shopModel = getShopModel(terminalShopReq);

        //1.添加终端店
        shopDao.insertValueColumn(shopModel);

        //添加终端店顺便生成团购客户给
        sendTogetherClient(shopModel, terminalShopReq.getPrimaryContract().getDealerCode());

        //2.之后创建终端店在dealer_info里面的分销商信息
        Integer distributeId = disposeCloudDealer(shopModel, new HashSet<>(), 0);

        //3.处理终端店和经销商表的关联关系
        disposeCloudDealerRelation(distributeId, terminalShopReq);

        //先判断当前手机号有没有创建 如果有 就不创建新的 而是把以前的用户信息和新终端店的信息放到关联关系表
        ShopUserModel leaderModel = shopUserDao.getUserByPhone(terminalShopReq.getLeaderPhone());

        //没有这个手机号 创建新用户 有的话下面直接拿查询出来的信息去添加关联关系
        if (null == leaderModel) {
            //添加终端负责人用户
            leaderModel = new ShopUserModel();
            //添加人员名称
            leaderModel.setName(terminalShopReq.getLeaderName());
            //添加人员手机号
            leaderModel.setPhone(terminalShopReq.getLeaderPhone());
            //作为主账户
            leaderModel.setPrimaryAccount(1);
            //人员密码
            leaderModel.setUserPassword(SignUtils.md5(userLoginPwd, SIGN_KEY));
            //人员所属终端店
            leaderModel.setShopId(shopModel.getId());
            //添加
            shopUserDao.insert(leaderModel);
            //如果有这个手机号 说明需要把他的名字同步更新成最后一次提交的名字
        } else {
            leaderModel.setName(terminalShopReq.getLeaderName());
            //更新联盟用户表和联盟终端表名字
            shopUserDao.updateNameByUserId(terminalShopReq.getLeaderName(), leaderModel.getId());
        }

        //4.处理联盟终端和终端用户关系 插入到关联关系表中
        insertMemberShopUserRelation(shopModel.getId(), leaderModel.getId(), leaderModel.getPhone());

        //5.处理国台终端申请信息表 用来保持dealerCode永不重复
        saveTerminalApply(shopModel, terminalShopReq.getPrimaryContract());

        //判断负责人和店员是不是一个手机号 同一个手机号说明只需要添加一次 不同需要再次添加一个
        if (StringUtils.isNotBlank(terminalShopReq.getKeeperPhone()) &&
                StringUtils.isNotBlank(terminalShopReq.getKeeperName()) &&
                !terminalShopReq.getKeeperPhone().equals(terminalShopReq.getLeaderPhone())) {
            //先查询一下是否有这个终端店员
            ShopUserModel keeperModel = shopUserDao.getUserByPhone(terminalShopReq.getKeeperPhone());
            //如果没有这个终端店员 就去添加新加 有的话用之前的信息绑定
            if (null == keeperModel) {
                keeperModel = new ShopUserModel();
                //添加人员名称
                keeperModel.setName(terminalShopReq.getKeeperName());
                //添加人员手机号
                keeperModel.setPhone(terminalShopReq.getKeeperPhone());
                //人员密码
                keeperModel.setUserPassword(SignUtils.md5(userLoginPwd, SIGN_KEY));
                //作为主账户
                keeperModel.setPrimaryAccount(0);
                //人员所属终端店
                keeperModel.setShopId(shopModel.getId());
                //添加
                shopUserDao.insert(keeperModel);
                //如果有这个 店员只修改自己member_user的名字
            } else {
                keeperModel.setName(terminalShopReq.getKeeperName());
                shopUserDao.update(keeperModel);
            }
            //处理联盟终端和终端用户关系 插入到关联关系表中
            insertMemberShopUserRelation(shopModel.getId(), keeperModel.getId(), keeperModel.getPhone());
        }

        //返回终端店id
        return shopModel.getId();
    }


    public Integer disposeCloudDealer(ShopModel shopModel, Set<String> redisKeyList, Integer terminalShopId) {
        TerminalDealerInfoModelReq terminalDealerInfoModelReq = new TerminalDealerInfoModelReq();
        terminalDealerInfoModelReq.setSuperiorId(shopModel.getDistributorId());
        terminalDealerInfoModelReq.setType(2);
        terminalDealerInfoModelReq.setAccountType(5);
        terminalDealerInfoModelReq.setDealerCode(shopModel.getDealerCode());
        terminalDealerInfoModelReq.setDealerName(shopModel.getName());
        terminalDealerInfoModelReq.setLinkman(shopModel.getLinkman());
        terminalDealerInfoModelReq.setPhone(shopModel.getLinkphone());
        terminalDealerInfoModelReq.setProvinces(shopModel.getProvince());
        terminalDealerInfoModelReq.setCity(shopModel.getCity());
        terminalDealerInfoModelReq.setDistrict(shopModel.getDistrict());
        terminalDealerInfoModelReq.setAddress(shopModel.getAddress());
        if (shopModel.getLongitude() != null) {
            terminalDealerInfoModelReq.setLongitude(shopModel.getLongitude());
        }
        if (shopModel.getLatitude() != null) {
            terminalDealerInfoModelReq.setLatitude(shopModel.getLatitude());
        }
        terminalDealerInfoModelReq.setStoresImg(shopModel.getStoresImg());
        terminalDealerInfoModelReq.setCompanyId(shopModel.getCompanyId());
        terminalDealerInfoModelReq.setLevel(2);
        terminalDealerInfoModelReq.setLicenseImg(shopModel.getLicenseImg());
        //处理添加或者修改
        TerminalDealerInfoModelReq dealer = terminalDealerShopDao.selectDealerIdByDealerCode(shopModel.getDealerCode());
        //没有就是添加
        if (null == dealer) {
            if (isReidsJudge) {
                String CLOUD_DEALER_INFO_INC_REDIS_KEY = "T_CLOUD_DEALER_INFO_INC_" + shopModel.getDealerCode();
                String CLOUD_DEALER_INFO_INC_VAL_REDIS_KEY = "T_CLOUD_DEALER_INFO_INC_VAL_" + shopModel.getDealerCode();
                long incNum4 = redisTemplate.boundValueOps(CLOUD_DEALER_INFO_INC_REDIS_KEY).increment();
                redisKeyList.add(CLOUD_DEALER_INFO_INC_REDIS_KEY);
                redisKeyList.add(CLOUD_DEALER_INFO_INC_VAL_REDIS_KEY);
                if (incNum4 > 1l) {//存在说明重复插入
                    Object idStr = redisTemplate.opsForValue().get(CLOUD_DEALER_INFO_INC_VAL_REDIS_KEY);
                    TerminalRepeatModel terminalRepeatModel = new TerminalRepeatModel();
                    terminalRepeatModel.setTableName("T_CLOUD_DEALER_INFO_" + incNum4);
                    terminalRepeatModel.setCreateTime(new Date());
                    terminalRepeatModel.setTerminalShopId(terminalShopId);
                    if (Objects.nonNull(idStr)) {
                        terminalRepeatModel.setTableId(Integer.valueOf(idStr.toString()));
                        terminalDealerInfoModelReq.setId(terminalRepeatModel.getTableId());
                    } else {
                        throw new BusinessException("400", "数据有误4");
                    }
                    terminalRepeatModel.setDataJson(JSONObject.toJSONString(terminalDealerInfoModelReq));
                    shopDao.insertTerminalRepeat(terminalRepeatModel);
                } else {
                    terminalDealerShopDao.insertCloudDealerInfo(terminalDealerInfoModelReq);
                    redisTemplate.opsForValue().setIfAbsent(CLOUD_DEALER_INFO_INC_VAL_REDIS_KEY, terminalDealerInfoModelReq.getId() + "", timeOut, timeUnit);
                }
            } else {
                terminalDealerShopDao.insertCloudDealerInfo(terminalDealerInfoModelReq);
            }
            //有就是修改
        } else {
            terminalDealerInfoModelReq.setId(dealer.getId());
            terminalDealerShopDao.updateCloudDealerInfo(terminalDealerInfoModelReq);
        }

        return terminalDealerInfoModelReq.getId();
    }

    public void disposeCloudDealerRelation(Integer distributeId, TerminalShopReq terminalShopReq) {
        //先根据自己的主键id 删除所有绑定关系
        terminalDealerShopDao.deleteCloudDealerRelationByDealerId(distributeId);

        //先看看主品合同或者酱酒合同是否为空 不为空添加关联关系
        if (null != terminalShopReq.getPrimaryContract() && StringUtils.isNotBlank(terminalShopReq.getPrimaryContract().getContractCode())) {
            CloudDealerRelationModelReq relationModel = new CloudDealerRelationModelReq();
            relationModel.setCompanyId(terminalShopReq.getCompanyId());
            relationModel.setDealerId(distributeId);
            //根据经销商或者分销商编码查出经销商或者分销商id
            TerminalDealerInfoModelReq dealer = terminalDealerShopDao.selectDealerIdByDealerCode(terminalShopReq.getPrimaryContract().getDealerCode());
            relationModel.setParentId(dealer.getId());
            relationModel.setCreateTime(new Date());
            relationModel.setRelationType(1);
            terminalDealerShopDao.insertCloudDealerRelation(relationModel);
        }
    }

    public void disposeCloudDealerRelationNew(Integer distributeId,
                                              TerminalShopInfoScheduleModel terminalShopInfoScheduleModel,
                                              TerminalShopContractResp terminalShopContractResp) {
        //先根据自己的主键id 删除所有绑定关系
        terminalDealerShopDao.deleteCloudDealerRelationByDealerId(distributeId);

        //先看看主品合同或者酱酒合同是否为空 不为空添加关联关系
        if (Objects.nonNull(terminalShopContractResp) && StringUtils.isNotBlank(terminalShopContractResp.getContractCode())) {
            CloudDealerRelationModelReq relationModel = new CloudDealerRelationModelReq();
            relationModel.setCompanyId(terminalShopInfoScheduleModel.getCompanyId());
            relationModel.setDealerId(distributeId);
            if (Objects.nonNull(terminalShopInfoScheduleModel.getDistributorId()) && terminalShopInfoScheduleModel.getDistributorId() != 0) {
                relationModel.setParentId(terminalShopInfoScheduleModel.getDistributorId());
            } else if (Objects.nonNull(terminalShopInfoScheduleModel.getCopartnerId()) && terminalShopInfoScheduleModel.getCopartnerId() != 0) {
                relationModel.setParentId(terminalShopInfoScheduleModel.getCopartnerId());
            } else {
                //根据经销商或者分销商编码查出经销商或者分销商id
                TerminalDealerInfoModelReq dealer = terminalDealerShopDao.selectDealerIdByDealerCode(terminalShopContractResp.getDealerCode());
                relationModel.setParentId(dealer.getId());
            }
            relationModel.setCreateTime(new Date());
            relationModel.setRelationType(1);
            terminalDealerShopDao.insertCloudDealerRelation(relationModel);
        }
    }

    /**
     * 添加联盟终端和终端用户的关联关系表
     *
     * @param shopId    终端id
     * @param userId    用户id
     * @param userPhone 用户手机号
     */
    public void insertMemberShopUserRelation(Integer shopId, Integer userId, String userPhone) {
        MemberShopUserRelationModel insertMemberShopUserRelationModel = new MemberShopUserRelationModel();
        //终端id
        insertMemberShopUserRelationModel.setShopId(shopId);
        //用户id
        insertMemberShopUserRelationModel.setUserId(userId);
        //用户手机号
        insertMemberShopUserRelationModel.setUserPhone(userPhone);
        //创建时间
        insertMemberShopUserRelationModel.setCreateTime(new Date());

        MemberShopUserRelationModel relationModel = memberShopUserRelationDao.selectOne(new QueryWrapper<MemberShopUserRelationModel>().eq("shop_id", shopId).eq("user_id", userId).eq("user_phone", userPhone));
        if (Objects.isNull(relationModel)) { //添加
            memberShopUserRelationDao.insert(insertMemberShopUserRelationModel);
        }
    }


    /**
     * @author: HLQ
     * @Date: 2023/3/15 11:29
     * @Description: 组合ShopModel 数据
     */
    public ShopModel getShopModelByTerminalShopInfoScheduleModel(TerminalShopInfoScheduleModel infoScheduleModel,
                                                                 TerminalShopContractResp terminalShopContractResp,
                                                                 TerminalProtocolModel terminalProtocolModel) {
        //创建终端店更新
        ShopModel shopModel = new ShopModel();
        //负责人电话
        shopModel.setLinkphone(infoScheduleModel.getLeaderPhone());
        shopModel.setName(infoScheduleModel.getShopName());
        List<ShopModel> memberList = shopDao.getMemberBeanByParam(shopModel);
        if (memberList.size() > 1) {
            throw new BusinessException("400", "member表存在多条数据");
        } else {
            if (memberList.size() == 1) {
                ShopModel memberInfo = memberList.get(0);
                BeanUtils.copyProperties(memberInfo, shopModel);
            } else {
                //终端id
                shopModel.setId(infoScheduleModel.getMemberShopId());
            }
        }
        //名称
        //负责人
        shopModel.setLinkman(infoScheduleModel.getLeaderName());
        //创建时间
        shopModel.setCreateTime(new Date());
        //省
        shopModel.setProvince(infoScheduleModel.getProvince());
        //市
        shopModel.setCity(infoScheduleModel.getCity());
        //区
        shopModel.setDistrict(infoScheduleModel.getDistrict());
        //详细地址
        shopModel.setAddress(infoScheduleModel.getAddress());
        //营业执照号
        shopModel.setLicense(infoScheduleModel.getLicenseCode());
        //营业执照图片
        shopModel.setLicenseImg(infoScheduleModel.getLicenseImg());
        //经度
        shopModel.setLongitude(infoScheduleModel.getLongitude());
        //纬度
        shopModel.setLatitude(infoScheduleModel.getLatitude());
        //终端店门头照
        shopModel.setStoresImg(infoScheduleModel.getHeadImg());
        //公司id
        shopModel.setCompanyId(infoScheduleModel.getCompanyId());
        //主编码
        shopModel.setMainCode(infoScheduleModel.getMainCode());
        //副编码
        shopModel.setDealerCode(infoScheduleModel.getDeputyCode());
        //合同不为空就赋值合同类型
        if (null != terminalShopContractResp && null != terminalShopContractResp.getContractType()) {
            shopModel.setContractType(terminalShopContractResp.getContractType() + "");
        }
        //终端店级别
        if (Objects.nonNull(terminalProtocolModel)) {
            shopModel.setLevelCode(terminalProtocolModel.getLevelCode());
        } else {
            shopModel.setLevelCode("0");
        }
        //返回
        return shopModel;
    }


    public ShopModel getShopModel(TerminalShopReq terminalShopReq) {
        //创建终端店更新
        ShopModel shopModel = new ShopModel();
        //终端id
        shopModel.setId(terminalShopReq.getMemberShopId());
        //名称
        shopModel.setName(terminalShopReq.getShopName());
        //负责人
        shopModel.setLinkman(terminalShopReq.getLeaderName());
        //负责人电话
        shopModel.setLinkphone(terminalShopReq.getLeaderPhone());
        //创建时间
        shopModel.setCreateTime(new Date());
        //省
        shopModel.setProvince(terminalShopReq.getProvince());
        //市
        shopModel.setCity(terminalShopReq.getCity());
        //区
        shopModel.setDistrict(terminalShopReq.getDistrict());
        //详细地址
        shopModel.setAddress(terminalShopReq.getAddress());
        //营业执照号
        shopModel.setLicense(terminalShopReq.getLicenseCode());
        //营业执照图片
        shopModel.setLicenseImg(terminalShopReq.getLicenseImg());
        //经度
        shopModel.setLongitude(terminalShopReq.getLongitude());
        //纬度
        shopModel.setLatitude(terminalShopReq.getLatitude());
        //终端店门头照
        shopModel.setStoresImg(terminalShopReq.getHeadImg());
        //公司id
        shopModel.setCompanyId(terminalShopReq.getCompanyId());
        //主编码
        shopModel.setMainCode(terminalShopReq.getMainCode());
        //副编码
        shopModel.setDealerCode(terminalShopReq.getDeputyCode());
        //合同不为空就赋值合同类型
        if (null != terminalShopReq.getPrimaryContract() && null != terminalShopReq.getPrimaryContract().getContractType()) {
            shopModel.setContractType(terminalShopReq.getPrimaryContract().getContractType() + "");
        }
        //终端店级别
        shopModel.setLevelCode(terminalShopReq.getLevelCode());
        //返回
        return shopModel;
    }

    public TerminalShopModel getTerminalShopModel(TerminalShopReq terminalShopReq) {
        TerminalShopModel modelData;
        if (Objects.nonNull(terminalShopReq.getId())) {
            modelData = terminalShopDao.selectById(terminalShopReq.getId());
        } else {
            modelData = new TerminalShopModel();
        }
        //主键id
        modelData.setId(terminalShopReq.getId());
        //终端名称
        modelData.setShopName(terminalShopReq.getShopName());
        //终端负责人姓名
        modelData.setLeaderName(terminalShopReq.getLeaderName());
        //终端负责人手机号
        modelData.setLeaderPhone(terminalShopReq.getLeaderPhone());
        //标记 1-普通、2-多关注、3-重点关注
        modelData.setTag(terminalShopReq.getTag());
        //终端门头照
        modelData.setHeadImg(terminalShopReq.getHeadImg());
        //备注
        modelData.setRemark(terminalShopReq.getRemark());
        //省
        modelData.setProvince(terminalShopReq.getProvince());
        //市
        modelData.setCity(terminalShopReq.getCity());
        //区
        modelData.setDistrict(terminalShopReq.getDistrict());
        //详细地址
        modelData.setAddress(terminalShopReq.getAddress());
        //经度
        modelData.setLongitude(terminalShopReq.getLongitude());
        //纬度
        modelData.setLatitude(terminalShopReq.getLatitude());
        //终端类型 1-烟酒店、2-专卖店、3-餐饮店、4-商超
        modelData.setType(terminalShopReq.getType());
        //是否形象店 0- 否 1-是
        modelData.setIsImage(terminalShopReq.getIsImage());
        //终端店员姓名
        modelData.setKeeperName(terminalShopReq.getKeeperName());
        //终端店员手机
        modelData.setKeeperPhone(terminalShopReq.getKeeperPhone());
        //店铺面积
        modelData.setShopArea(terminalShopReq.getShopArea());
        //营业执照照片
        modelData.setLicenseImg(terminalShopReq.getLicenseImg());
        //营业执照编号
        modelData.setLicenseCode(terminalShopReq.getLicenseCode());
        //状态
        modelData.setStatus(terminalShopReq.getStatus());
        //获取当前时间
        Date now = new Date();
        //更新时间
        modelData.setUpdateTime(now);
        //创建人id
        modelData.setCreateUser(terminalShopReq.getCreateUser());
        //公司id
        modelData.setCompanyId(terminalShopReq.getCompanyId());
        //member的终端店id
        modelData.setMemberShopId(terminalShopReq.getMemberShopId());
        //主编码
        modelData.setMainCode(terminalShopReq.getMainCode());
        //副编码
        modelData.setDeputyCode(terminalShopReq.getDeputyCode());
        //终端店类型
        modelData.setShopType(terminalShopReq.getShopType());
        //终端店有无营业执照
        modelData.setWhetherLicense(terminalShopReq.getWhetherLicense());
        //终端店企业名称
        modelData.setEnterpriseName(terminalShopReq.getEnterpriseName());
        //终端店食品经营许可证
        modelData.setFoodBusinessLicense(terminalShopReq.getFoodBusinessLicense());
        //终端店是否是自营
        modelData.setWhetherProprietaryTrading(terminalShopReq.getWhetherProprietaryTrading());
        //终端店形象店门头照
        modelData.setImageHeadPicture(terminalShopReq.getImageHeadPicture());
        //终端店收货仓库省
        modelData.setReceivingWarehouseProvince(terminalShopReq.getReceivingWarehouseProvince());
        //终端店收货仓库市
        modelData.setReceivingWarehouseCity(terminalShopReq.getReceivingWarehouseCity());
        //终端店收货仓库区
        modelData.setReceivingWarehouseDistrict(terminalShopReq.getReceivingWarehouseDistrict());
        //终端店收货仓库地址
        modelData.setReceivingWarehouseAddress(terminalShopReq.getReceivingWarehouseAddress());
        //终端等级编码
        modelData.setLevelCode(terminalShopReq.getLevelCode());
        //终端联系人姓名
        modelData.setContactName(terminalShopReq.getContactName());
        //终端联系人级别
        modelData.setContactLevel(terminalShopReq.getContactLevel());
        //终端联系人手机号
        modelData.setContactPhone(terminalShopReq.getContactPhone());
        //终端收款方式
        modelData.setReceivingPaymentType(terminalShopReq.getReceivingPaymentType());
        //终端收款人名称
        modelData.setReceivingPaymentName(terminalShopReq.getReceivingPaymentName());
        //终端收款人账户
        modelData.setReceivingPaymentAccount(terminalShopReq.getReceivingPaymentAccount());
        //终端收款人账户图片
        modelData.setReceivingPaymentAccountPicture(terminalShopReq.getReceivingPaymentAccountPicture());
        //终端审核人id
        modelData.setAuditUserId(terminalShopReq.getAuditUserId());
        //终端审核结果
        modelData.setAuditResult(terminalShopReq.getAuditResult());
        //获取终端协议 (0包量协议,1陈列协议 两个都有就是0,1)
        modelData.setShopAgreement(terminalShopReq.getShopAgreement());
        //会员终端特殊字段 0是男 1是女
        modelData.setGender(terminalShopReq.getGender());
        //会员终端特殊字段 生日
        modelData.setBirthday(terminalShopReq.getBirthday());
        //会员终端特殊字段 年龄
        modelData.setAge(terminalShopReq.getAge());
        //会员终端特殊字段 工作单位
        modelData.setWorkUnit(terminalShopReq.getWorkUnit());
        //会员终端特殊字段 职务
        modelData.setPosition(terminalShopReq.getPosition());
        //会员终端特殊字段 个人爱好
        modelData.setPersonalPreference(terminalShopReq.getPersonalPreference());
        //会员终端特殊字段 经销商联系方式
//        modelData.setDealerContactType(terminalShopReq.getDealerContactType());
//        //会员终端特殊字段 初次购买产品名称
//        modelData.setFirstBuyGoodsName(terminalShopReq.getFirstBuyGoodsName());
//        //会员终端特殊字段 初次购买产品数量(箱)
//        modelData.setFirstBuyGoodsNumber(terminalShopReq.getFirstBuyGoodsNumber());
//        //会员终端特殊字段 初次积分
//        modelData.setFirstScore(terminalShopReq.getFirstScore());
        //会员终端特殊字段 登记表图片
        modelData.setRegistrationFormPicture(terminalShopReq.getRegistrationFormPicture());
        //国台审核结果
        modelData.setGtAuditResult(terminalShopReq.getGtAuditResult());
        //收款银行
        modelData.setReceivingPaymentBank(terminalShopReq.getReceivingPaymentBank());
        //网点建立理由
        modelData.setNetworkPointEstablishReason(terminalShopReq.getNetworkPointEstablishReason());
        //返回
        if (terminalShopReq.getShopType() == 3) {
            modelData.setEnterpriseName(terminalShopReq.getShopName());
        }
        return modelData;
    }

    public TerminalShopContractModel getTerminalShopContractModel(TerminalShopContractReq terminalShopContractReq) {
        //创建返回类
        TerminalShopContractModel modelData = new TerminalShopContractModel();
        //合同类型
        modelData.setContractType(terminalShopContractReq.getContractType());
        //经销商编码
        modelData.setDealerCode(terminalShopContractReq.getDealerCode());
        //合同编码
        modelData.setContractCode(terminalShopContractReq.getContractCode());

        modelData.setContractCodeName(terminalShopContractReq.getContractCodeName());
//        //陈列协议照片
//        modelData.setDisplayImage(terminalShopContractReq.getDisplayImage());
//        //包量协议照片
//        modelData.setPackageQuantityImage(terminalShopContractReq.getPackageQuantityImage());
        //获取当前时间
        Date now = new Date();
        //创建时间
        modelData.setCreateTime(now);
        //更新时间
        modelData.setUpdateTime(now);
        //返回
        return modelData;
    }

    /**
     * 获取不重复的终端编号
     */
    private String makeTerminalCode() {
        while (true) {
            String terminalCode = RandomUtils.getTerminalCode();
            if (terminalShopDao.checkTerminalCode(terminalCode) == 0 && terminalShopDao.checkTerminalGtCode(terminalCode) == 0) {
                return terminalCode;
            }
        }
    }

    /**
     * 保存终端提报记录
     */
    private void saveTerminalApply(ShopModel shopModel, TerminalShopContractReq terminalShopContractReq) {
        TerminalDealerGtTerminalReq terminalDealerGtTerminalReq = new TerminalDealerGtTerminalReq();
        terminalDealerGtTerminalReq.setZhongtaiId(null);
        terminalDealerGtTerminalReq.setContractCode(terminalShopContractReq.getContractCode());
        terminalDealerGtTerminalReq.setChannelCode(terminalShopContractReq.getDealerCode());
        //查询一下经销商名称
        TerminalDealerInfoModelReq dealer = terminalDealerShopDao.selectDealerIdByDealerCode(terminalShopContractReq.getDealerCode());
        terminalDealerGtTerminalReq.setChannelName(dealer.getDealerName());
        terminalDealerGtTerminalReq.setTerminalCode(shopModel.getDealerCode());
        terminalDealerGtTerminalReq.setDeputyCode(shopModel.getDealerCode());
        terminalDealerGtTerminalReq.setTerminalName(shopModel.getName());
        terminalDealerGtTerminalReq.setLicenseCode(shopModel.getLicense());
        terminalDealerGtTerminalReq.setLicenseImg(shopModel.getLicenseImg());
        terminalDealerGtTerminalReq.setPhone(shopModel.getLinkphone());
        terminalDealerGtTerminalReq.setLinkman(shopModel.getLinkman());
        terminalDealerGtTerminalReq.setStoresImg(shopModel.getStoresImg());
        terminalDealerGtTerminalReq.setProvince(shopModel.getProvince());
        terminalDealerGtTerminalReq.setCity(shopModel.getCity());
        terminalDealerGtTerminalReq.setDistrict(shopModel.getDistrict());
        terminalDealerGtTerminalReq.setAddress(shopModel.getAddress());
        terminalDealerGtTerminalReq.setLongitude(shopModel.getLongitude());
        terminalDealerGtTerminalReq.setLatitude(shopModel.getLatitude());
        terminalDealerGtTerminalReq.setCompanyId(shopModel.getCompanyId());
        terminalDealerShopDao.insertCloudDealerGtTerminalApply(terminalDealerGtTerminalReq);
    }

    private void saveTerminalApply(ShopModel shopModel, TerminalShopContractResp terminalShopContractResp, Set<String> redisKeyList) {
        TerminalDealerGtTerminalReq terminalDealerGtTerminalReq = new TerminalDealerGtTerminalReq();
        terminalDealerGtTerminalReq.setZhongtaiId(null);
        terminalDealerGtTerminalReq.setContractCode(terminalShopContractResp.getContractCode());
        terminalDealerGtTerminalReq.setChannelCode(terminalShopContractResp.getDealerCode());
        //查询一下经销商名称
        TerminalDealerInfoModelReq dealer = terminalDealerShopDao.selectDealerIdByDealerCode(terminalShopContractResp.getDealerCode());
        terminalDealerGtTerminalReq.setChannelName(dealer.getDealerName());
        terminalDealerGtTerminalReq.setTerminalCode(shopModel.getDealerCode());
        terminalDealerGtTerminalReq.setDeputyCode(shopModel.getDealerCode());
        terminalDealerGtTerminalReq.setTerminalName(shopModel.getName());
        terminalDealerGtTerminalReq.setLicenseCode(shopModel.getLicense());
        terminalDealerGtTerminalReq.setLicenseImg(shopModel.getLicenseImg());
        terminalDealerGtTerminalReq.setPhone(shopModel.getLinkphone());
        terminalDealerGtTerminalReq.setLinkman(shopModel.getLinkman());
        terminalDealerGtTerminalReq.setStoresImg(shopModel.getStoresImg());
        terminalDealerGtTerminalReq.setProvince(shopModel.getProvince());
        terminalDealerGtTerminalReq.setCity(shopModel.getCity());
        terminalDealerGtTerminalReq.setDistrict(shopModel.getDistrict());
        terminalDealerGtTerminalReq.setAddress(shopModel.getAddress());
        terminalDealerGtTerminalReq.setLongitude(shopModel.getLongitude());
        terminalDealerGtTerminalReq.setLatitude(shopModel.getLatitude());
        terminalDealerGtTerminalReq.setCompanyId(shopModel.getCompanyId());

        if (isReidsJudge) {
            String CLOUD_DEALER_GT_TERMINAL_APPLY_INC_REDIS_KEY = "T_CLOUD_DEALER_GT_TERMINAL_APPLY_INC_" + terminalDealerGtTerminalReq.getTerminalCode() + "___" + terminalDealerGtTerminalReq.getDeputyCode();
            String CLOUD_DEALER_GT_TERMINAL_APPLY_INC_VAL_REDIS_KEY = "T_CLOUD_DEALER_GT_TERMINAL_APPLY_INC_VAL_" + terminalDealerGtTerminalReq.getTerminalCode() + "___" + terminalDealerGtTerminalReq.getDeputyCode();
            long incNum5 = redisTemplate.boundValueOps(CLOUD_DEALER_GT_TERMINAL_APPLY_INC_REDIS_KEY).increment();
            redisKeyList.add(CLOUD_DEALER_GT_TERMINAL_APPLY_INC_REDIS_KEY);
            redisKeyList.add(CLOUD_DEALER_GT_TERMINAL_APPLY_INC_VAL_REDIS_KEY);
            if (incNum5 > 1l) {//存在说明重复插入
                Object idStr = redisTemplate.opsForValue().get(CLOUD_DEALER_GT_TERMINAL_APPLY_INC_VAL_REDIS_KEY);
                TerminalRepeatModel terminalRepeatModel = new TerminalRepeatModel();
                terminalRepeatModel.setTableName("T_CLOUD_DEALER_GT_TERMINAL_APPLY");
                terminalRepeatModel.setTerminalShopId(terminalShopContractResp.getTerminalShopId());
                terminalRepeatModel.setCreateTime(new Date());
                if (Objects.nonNull(idStr)) {
                    terminalRepeatModel.setTableId(Integer.valueOf(idStr.toString()));
                }
                terminalRepeatModel.setDataJson(JSONObject.toJSONString(terminalDealerGtTerminalReq));
                shopDao.insertTerminalRepeat(terminalRepeatModel);
            } else {
                terminalDealerShopDao.insertCloudDealerGtTerminalApply(terminalDealerGtTerminalReq);
                redisTemplate.opsForValue().setIfAbsent(CLOUD_DEALER_GT_TERMINAL_APPLY_INC_VAL_REDIS_KEY, "1", timeOut, timeUnit);
            }
        } else {
            terminalDealerShopDao.insertCloudDealerGtTerminalApply(terminalDealerGtTerminalReq);
        }
    }

    /**
     * @Description: 处理主副编码   副编码是唯一的并提供其他系统使用
     */
    public void disposeMainCodeAndDeputyCode(TerminalShopReq terminalShopReq) {
        //查询实体类
        TerminalShopModel selectTerminalData = terminalShopDao.selectById(terminalShopReq.getId());

        //如果营业执照为空 不允许生成编码
        if (StringUtils.isNotBlank(terminalShopReq.getLicenseCode()) && null != selectTerminalData) {
            //看看主编码或者副编码是否为空
            if (StringUtils.isBlank(selectTerminalData.getMainCode()) || StringUtils.isBlank(selectTerminalData.getDeputyCode())) {
                //根据营业执照编号去查询有没有这个主编码
                String mainCode = shopDao.selectTerminalCodeByLicenseCode(terminalShopReq.getLicenseCode());

                //副编码生成
                String deputyCode = makeTerminalCode();

                //如果主编码为空 说明营业执照没有被用过 用副编码
                if (StringUtils.isBlank(mainCode)) {
                    mainCode = deputyCode;
                }
                //转换实体类
                terminalShopReq.setMainCode(mainCode);
                terminalShopReq.setDeputyCode(deputyCode);
            } else {
                //不等于空就用已经生成的
                terminalShopReq.setMainCode(selectTerminalData.getMainCode());
                terminalShopReq.setDeputyCode(selectTerminalData.getDeputyCode());
            }
        } else {
            //如果为空 说明选择的是没有营业执照 不去数据库查询
            //副编码生成
            String deputyCode = makeTerminalCode();
            //赋值主编码
            //转换实体类
            terminalShopReq.setMainCode(deputyCode);
            terminalShopReq.setDeputyCode(deputyCode);
        }
    }

    public void sendTerminalData(TerminalShopReq terminalShopReq) {
        //创建返回接受类
        ResponseEntity<String> responseEntity = null;

        //先获取token
        String token = ztUtils.getXwToken();
        //创建请求头
        HttpHeaders headers = new HttpHeaders();
        //请求头参数
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Token", token);

        //创建请求参数
        JSONObject reqParam = new JSONObject();
        setSendTerminalDataParam(reqParam, terminalShopReq);

        //请求实体类
        HttpEntity<JSONObject> entity = new HttpEntity<>(reqParam, headers);

        //请求参数打印
        log.info("我是请求同步终端店数据的实体参数:{}", JSON.toJSONString(reqParam));
        log.info("我是请求同步终端店数据的token参数:{}", token);

        try {
            responseEntity = restTemplate.exchange(sendTerminalDataUrl, HttpMethod.POST, entity, String.class);
            log.info("同步中台的采集终端---{}---{}", token, JSON.toJSONString(responseEntity));

            String body = responseEntity.getBody();
            JSONObject resp = JSONObject.parseObject(body);
            if (resp != null && resp.getJSONObject("resp_data") != null) {
                JSONObject result = resp.getJSONObject("resp_data").getJSONObject("result");
                if (null != result && result.get("success").equals("True")) {
                    return;
                } else {
                    throw new BusinessException("400", "同步中台的采集终端失败" + (result == null ? "" : result.get("message").toString()));
                }
            }

        } catch (Exception e) {
            log.error("我是同步中台的采集终端失败原因:{}", e.getMessage());
            throw new BusinessException("400", "同步中台的采集终端失败" + e.getMessage());
        }
    }

    /**
     * 终端-经销商、合同变更：推送中台处理
     *
     * @param terminalShopReq
     */
    public void sendTerminalDataV2(TerminalShopReq terminalShopReq) {
        //先获取token
        String token = ztUtils.getXwToken();
        //创建请求参数
        JSONObject reqParam = new JSONObject();
        setSendTerminalDataParamV2(reqParam, terminalShopReq);

        //请求参数打印
        log.info("我是请求同步终端店数据的实体参数:{}", JSON.toJSONString(reqParam));
        log.info("我是请求同步终端店数据的token参数:{}", token);

        try {
            HttpRequest httpRequest = HttpUtil.createPost(sendTerminalDataUrl).header("Token", token).body(reqParam.toJSONString());
            cn.hutool.http.HttpResponse executeResponse = httpRequest.execute();
            log.info("同步中台的采集终端---{}---{}", token, executeResponse);
            if (!executeResponse.isOk()) {
//                log.info("同步中台的采集终端请求失败---{}", executeResponse);
                throw new BusinessException("400", "同步中台的采集终端请求失败" + executeResponse.toString());
            }
            JSONObject resp = JSONObject.parseObject(executeResponse.body());
            if (resp == null || resp.getJSONObject("resp_data") == null ||
                    resp.getJSONObject("resp_data").getJSONObject("result") == null ||
                    !"True".equals(resp.getJSONObject("resp_data").getJSONObject("result").getString("success"))
            ) {
//                log.info("同步中台的采集终端失败---{}", resp);
                throw new BusinessException("400", "同步中台的采集终端失败" + resp);
            }
        } catch (Exception e) {
            log.error("我是同步中台的采集终端失败原因:{}", e.getMessage());
            throw new BusinessException("400", "同步中台的采集终端失败" + e.getMessage());
        }
    }

    public void setSendTerminalDataParam(JSONObject reqParam, TerminalShopReq terminalShopReq) {
        //创建终端信息参数集合
        JSONObject gtStore = new JSONObject();

        //终端主编码
        gtStore.put("storecode", terminalShopReq.getMainCode());
        //终端副编码
        gtStore.put("deputy_code", terminalShopReq.getDeputyCode());
        //终端名称
        gtStore.put("storename", terminalShopReq.getShopName());
        if (null != terminalShopReq.getPrimaryContract()) {
            //合同类型(0:主品合同,1:酱酒合同)
            gtStore.put("contracttype", terminalShopReq.getPrimaryContract().getContractType() + "");
            //合同编码
            gtStore.put("contractcode", terminalShopReq.getPrimaryContract().getContractCode());
            //陈列协议照片
            gtStore.put("display_image", terminalShopReq.getPrimaryContract().getDisplayImage());
            //包量协议照片
            gtStore.put("package_quantity_image", terminalShopReq.getPrimaryContract().getPackageQuantityImage());

        }
        //终端负责人姓名
        gtStore.put("contactname", terminalShopReq.getLeaderName());
        //终端负责人电话
        gtStore.put("contactphone", terminalShopReq.getLeaderPhone());
        //标记 1-普通、2-多关注、3-重点关注
        gtStore.put("tag", terminalShopReq.getTag() == null ? "" : terminalShopReq.getTag() + "");
        //终端等级
        gtStore.put("storelevel", terminalShopReq.getLevelCode() == null ? "" : terminalShopReq.getLevelCode());
        //门头照
        gtStore.put("headimg", terminalShopReq.getHeadImg() == null ? "" : terminalShopReq.getHeadImg());
        //备注
        gtStore.put("remark", terminalShopReq.getRemark() == null ? "" : terminalShopReq.getRemark());
        //省
        gtStore.put("province", terminalShopReq.getProvince());
        //市
        gtStore.put("city", terminalShopReq.getCity());
        //区
        gtStore.put("district", terminalShopReq.getDistrict());
        //详细地址
        gtStore.put("address", terminalShopReq.getAddress());
        //经度
        gtStore.put("longitude", null == terminalShopReq.getLongitude() ? "" : terminalShopReq.getLongitude() + "");
        //纬度
        gtStore.put("latitude", null == terminalShopReq.getLatitude() ? "" : terminalShopReq.getLatitude() + "");
        //是否形象店 0- 否 1-是
        gtStore.put("is_image", null == terminalShopReq.getIsImage() ? "" : terminalShopReq.getIsImage() + "");
        //店员名称
        gtStore.put("storecontactname", terminalShopReq.getKeeperName() == null ? "" : terminalShopReq.getKeeperName());
        //店长手机号
        gtStore.put("storecontactphone", terminalShopReq.getKeeperPhone() == null ? "" : terminalShopReq.getKeeperPhone());
        //店铺面积
        gtStore.put("store_area", terminalShopReq.getShopArea() == null ? "" : terminalShopReq.getShopArea());
        //营业执照编号
        gtStore.put("license_code", terminalShopReq.getLicenseCode() == null ? "" : terminalShopReq.getLicenseCode());
        //营业执照照片
        gtStore.put("license_img", terminalShopReq.getLicenseImg() == null ? "" : terminalShopReq.getLicenseImg());
        //终端类型 1-烟酒店、2-专卖店、3-餐饮店、4-商超
        gtStore.put("store_type", "");
        //激活状态 0- 未激活 1-已激活
        gtStore.put("status", terminalShopReq.getStatus() == null ? "" : terminalShopReq.getStatus() + "");
        //操作类型1：新增；2：编辑；3：状态变更
        gtStore.put("option", null);
        //客户经理id
        String accountManagerId = "";
        //根据主键id查出来信息
        TerminalShopModel selectTerminalData = terminalShopDao.selectById(terminalShopReq.getId());
        //获取创建这个终端店的人员信息
        TerminalAccountManagerModel selectTerminalAccountManagerData = terminalAccountManagerDao.selectById(selectTerminalData.getCreateUser());
        //如果人员是客户经理 直接填入客户经理id
        if (0 == selectTerminalAccountManagerData.getParentId()) {
            accountManagerId = selectTerminalAccountManagerData.getGtId();
            //如果不是客户经理 查出自己上级的客户经理
        } else {
            //根据parentId查询最上级的客户经理信息
            selectTerminalAccountManagerData = terminalAccountManagerDao.selectOne(new QueryWrapper<TerminalAccountManagerModel>().eq("id", selectTerminalAccountManagerData.getParentId()));
            accountManagerId = selectTerminalAccountManagerData.getGtId();

        }
        gtStore.put("manager", accountManagerId);

        //新增国台和懐酒的区分 是懐酒填1 不是填0
        if (gtType.equals("hj")) {
            gtStore.put("huaijiu_source", "1");
        } else {
            gtStore.put("huaijiu_source", "0");
        }

        //2022-12-21采集终端修改新加同步参数
        //有无营业执照
        gtStore.put("is_business_licence", terminalShopReq.getWhetherLicense() == null ? "" : terminalShopReq.getWhetherLicense() + "");
        //企业名称
        gtStore.put("companyname", terminalShopReq.getEnterpriseName() == null ? "" : terminalShopReq.getEnterpriseName() + "");
        //食品许可证
        gtStore.put("food_business_license", terminalShopReq.getFoodBusinessLicense() == null ? "" : terminalShopReq.getFoodBusinessLicense() + "");
        //是否自营
        gtStore.put("is_self_operated", terminalShopReq.getWhetherProprietaryTrading() == null ? "" : terminalShopReq.getWhetherProprietaryTrading() + "");
        //形象店门头照
        gtStore.put("storeimage", terminalShopReq.getImageHeadPicture() == null ? "" : terminalShopReq.getImageHeadPicture() + "");
        //收货仓库省
        gtStore.put("province_storehouse", terminalShopReq.getReceivingWarehouseProvince() == null ? "" : terminalShopReq.getReceivingWarehouseProvince() + "");
        //收货仓库市
        gtStore.put("city_storehouse", terminalShopReq.getReceivingWarehouseCity() == null ? "" : terminalShopReq.getReceivingWarehouseCity() + "");
        //收货仓库区
        gtStore.put("district_storehouse", terminalShopReq.getReceivingWarehouseDistrict() == null ? "" : terminalShopReq.getReceivingWarehouseDistrict() + "");
        //收货仓库地址
        gtStore.put("storehouse_address", terminalShopReq.getReceivingWarehouseAddress() == null ? "" : terminalShopReq.getReceivingWarehouseAddress() + "");
        //终端等级编码
        gtStore.put("store_grade", terminalShopReq.getLevelCode() == null ? "" : terminalShopReq.getLevelCode() + "");
        //联系人级别
        gtStore.put("contactgrade", terminalShopReq.getContactLevel() == null ? "" : terminalShopReq.getContactLevel() + "");
        //收款方式 0:支付宝 1:微信 2:银行
        gtStore.put("payment_method", terminalShopReq.getReceivingPaymentType() == null ? "" : terminalShopReq.getReceivingPaymentType() + "");
        //审核人id
        gtStore.put("approverid", terminalShopReq.getAuditUserId() == null ? "" : terminalShopReq.getAuditUserId() + "");
        //审核结果
        gtStore.put("auditout", terminalShopReq.getAuditResult() == null ? "" : terminalShopReq.getAuditResult() + "");
        //店铺类型
        gtStore.put("shop_type", terminalShopReq.getShopType() == null ? "" : terminalShopReq.getShopType() + "");
        //联系人
        gtStore.put("contact", terminalShopReq.getContactName() == null ? "" : terminalShopReq.getContactName() + "");
        //联系电话
        gtStore.put("tel", terminalShopReq.getContactPhone() == null ? "" : terminalShopReq.getContactPhone() + "");
        //终端协议
        gtStore.put("storeagreement", terminalShopReq.getShopAgreement() == null ? "" : terminalShopReq.getShopAgreement() + "");

        //会员年龄
        gtStore.put("member_age", terminalShopReq.getAge() == null ? "" : terminalShopReq.getAge() + "");
        //会员工作单位
        gtStore.put("member_workplace", terminalShopReq.getWorkUnit() == null ? "" : terminalShopReq.getWorkUnit() + "");
        //会员职务
        gtStore.put("membership", terminalShopReq.getPosition() == null ? "" : terminalShopReq.getPosition() + "");
        //会员个人爱好
        gtStore.put("member_hobby", terminalShopReq.getPersonalPreference() == null ? "" : terminalShopReq.getPersonalPreference() + "");
        //经销商联系方式
        gtStore.put("channel_concat", terminalShopReq.getDealerContactType() == null ? "" : terminalShopReq.getDealerContactType() + "");
        //初次购买产品名称
        gtStore.put("first_productname", terminalShopReq.getFirstBuyGoodsName() == null ? "" : terminalShopReq.getFirstBuyGoodsName() + "");
        //初次购买产品数量(箱)
        gtStore.put("first_productnum", terminalShopReq.getFirstBuyGoodsNumber() == null ? "" : terminalShopReq.getFirstBuyGoodsNumber() + "");
        //初次积分
        gtStore.put("first_integral", terminalShopReq.getFirstScore() == null ? "" : terminalShopReq.getFirstScore() + "");
        //登记表图片
        gtStore.put("register_photo", terminalShopReq.getRegistrationFormPicture() == null ? "" : terminalShopReq.getRegistrationFormPicture() + "");
        //会员性别
        gtStore.put("member_sex", terminalShopReq.getGender() == null ? "" : terminalShopReq.getGender() + "");
        //会员生日
        gtStore.put("member_bir", terminalShopReq.getBirthday() == null ? "" : terminalShopReq.getBirthday() + "");

        //填入
        reqParam.put("gt_store", gtStore);

        //把两个产品合成一下
        List<TerminalShopContractProductModel> agreementProductListAll = new ArrayList<TerminalShopContractProductModel>() {{
            if (null != terminalShopReq.getPackageQuantityAgreementProductList() && terminalShopReq.getPackageQuantityAgreementProductList().size() > 0) {
                addAll(terminalShopReq.getPackageQuantityAgreementProductList());
            }

            if (null != terminalShopReq.getDisplayAgreementProductList() && terminalShopReq.getDisplayAgreementProductList().size() > 0) {
                addAll(terminalShopReq.getDisplayAgreementProductList());
            }
        }};


        //创建返回集合
        List<JSONObject> gtStoreAgreementList = new ArrayList<>();

        //产品协议非必填
        if (null != terminalShopReq.getPrimaryContract() && agreementProductListAll.size() > 0) {

            //遍历
            for (TerminalShopContractProductModel productModel : agreementProductListAll) {
                //创建产品协议
                JSONObject gtStoreAgreement = new JSONObject();
                //终端副编码
                gtStoreAgreement.put("deputy_code", terminalShopReq.getDeputyCode());
                //协议产品类型 (0包量协议,1陈列协议)
                gtStoreAgreement.put("type", productModel.getAgreementType() + "");
                //协议产品编码
                gtStoreAgreement.put("productcode", productModel.getAgreementCode());
                //协议产品名称
                gtStoreAgreement.put("productname", productModel.getAgreementName());
                //单位
                gtStoreAgreement.put("unit", null == productModel.getUnit() ? "" : productModel.getUnit());
                //数量
                gtStoreAgreement.put("number", null == productModel.getNumber() ? "" : productModel.getNumber() + "");
                //获取当前时间
                String now = new Date().getTime() + "";
                //创建时间
                gtStoreAgreement.put("createtime", now);
                //更新时间
                gtStoreAgreement.put("updatetime", now);
                //陈列面
                gtStoreAgreement.put("display", null == productModel.getDisplaySurface() ? "" : productModel.getDisplaySurface() + "");
                //陈列费用
                gtStoreAgreement.put("money", null == productModel.getDisplayCost() ? "" : productModel.getDisplayCost() + "");
                //添加到集合
                gtStoreAgreementList.add(gtStoreAgreement);

            }
        }
        //填入
        reqParam.put("gt_store_agreement", gtStoreAgreementList);
    }

    /**
     * 终端-经销商、合同变更：推送中台请求参数构建
     *
     * @param reqParam
     * @param terminalShopReq
     */
    public void setSendTerminalDataParamV2(JSONObject reqParam, TerminalShopReq terminalShopReq) {
        //创建终端信息参数集合
        JSONObject gtStore = new JSONObject();
        //终端主编码
        gtStore.put("storecode", terminalShopReq.getMainCode());
        //终端副编码
        gtStore.put("deputy_code", terminalShopReq.getDeputyCode());
        //终端名称
        gtStore.put("storename", terminalShopReq.getShopName());
        if (null != terminalShopReq.getPrimaryContract()) {
            //合同类型(0:主品合同,1:酱酒合同 2：常规渠道经销合同 3：国台酱酒经销合同 4：专卖店经销合同 5：数智体验中心经销合同 6：团购特约经销合同 7：电商平台经销合同)
            gtStore.put("contracttype", terminalShopReq.getPrimaryContract().getContractType() + "");
            //合同编码
            gtStore.put("contractcode", terminalShopReq.getPrimaryContract().getContractCode());
            //终端负责人姓名
            gtStore.put("contactname", terminalShopReq.getLeaderName());
            //终端负责人电话
            gtStore.put("contactphone", terminalShopReq.getLeaderPhone());
        }
        //标记 1-普通、2-多关注、3-重点关注
        gtStore.put("tag", terminalShopReq.getTag() == null ? "" : terminalShopReq.getTag() + "");
        //门头照
        gtStore.put("headimg", terminalShopReq.getHeadImg() == null ? "" : terminalShopReq.getHeadImg());
        //备注
        gtStore.put("remark", terminalShopReq.getRemark() == null ? "" : terminalShopReq.getRemark());

        //省
        gtStore.put("province", terminalShopReq.getProvince());
        //市
        gtStore.put("city", terminalShopReq.getCity());
        //区
        gtStore.put("district", terminalShopReq.getDistrict());
        //详细地址
        gtStore.put("address", terminalShopReq.getAddress());
        //经度
        gtStore.put("longitude", null == terminalShopReq.getLongitude() ? "" : terminalShopReq.getLongitude() + "");
        //纬度
        gtStore.put("latitude", null == terminalShopReq.getLatitude() ? "" : terminalShopReq.getLatitude() + "");
        //是否形象店 0- 否 1-是
        gtStore.put("is_image", null == terminalShopReq.getIsImage() ? "" : terminalShopReq.getIsImage() + "");
        //店员名称
        gtStore.put("storecontactname", terminalShopReq.getKeeperName() == null ? "" : terminalShopReq.getKeeperName());
        //店长手机号
        gtStore.put("storecontactphone", terminalShopReq.getKeeperPhone() == null ? "" : terminalShopReq.getKeeperPhone());
        //0- 否，无营业执照 1-是,有营业执照
        gtStore.put("is_business_licence", terminalShopReq.getWhetherLicense() + "");
        //营业执照编号
        gtStore.put("license_code", terminalShopReq.getLicenseCode() == null ? "" : terminalShopReq.getLicenseCode());
        //营业执照照片
        gtStore.put("license_img", terminalShopReq.getLicenseImg() == null ? "" : terminalShopReq.getLicenseImg());

        // payment_method	String	N	收款方式
        gtStore.put("payment_method", CodeConstant.RECEIVING_PAYMENT_TYPE_MAP.get(terminalShopReq.getReceivingPaymentType()));
        //payee_name	String	N	收款人姓名
        gtStore.put("payee_name", terminalShopReq.getReceivingPaymentName());
        //payee_account	String	N	收款人账号
        gtStore.put("payee_account", StrUtil.blankToDefault(terminalShopReq.getReceivingPaymentAccount(), ""));
        //deposit_bank	String	N	开户行
        gtStore.put("deposit_bank", StrUtil.blankToDefault(terminalShopReq.getReceivingPaymentBank(), ""));

        //store_type	String	Y	终端类型 	1-烟酒店、2-专卖店、3-餐饮店、4-商超 5-渠道终端 6-企业终端 7-餐饮终端 8-连锁终端 9-团购终端  10-渠道终端会员 11-连锁终端会员 12-非会员虚拟终端
        int shopType = terminalShopReq.getShopType();
        String storeType = terminalShopCommonService.generateTerminalTypeByTerminalType(shopType);
        gtStore.put("store_type", storeType);
        // 新终端类型 0:渠道终端 1:餐饮终端 2:团购终端 3:企业终端 4:连锁终端 5会员终端
        gtStore.put("shop_type", shopType + "");
        //激活状态 0- 未激活 1-已激活
        gtStore.put("status", terminalShopReq.getStatus() == null ? "" : terminalShopReq.getStatus() + "");
        //操作类型1：新增；2：编辑；3：状态变更
        gtStore.put("option", null);
        String accountManagerId = "0";
        TerminalAccountManagerModel terminalAccountManagerModel = terminalAccountManagerDao.selectOne(new QueryWrapper<TerminalAccountManagerModel>().eq("id", terminalShopReq.getCreateUser()));
        if (terminalAccountManagerModel != null) {
            accountManagerId = terminalAccountManagerModel.getGtId();
            if (terminalAccountManagerModel.getType() != 0) {//是客户经理
                TerminalAccountManagerModel accountManagerModel = terminalAccountManagerDao.selectOne(new QueryWrapper<TerminalAccountManagerModel>().eq("id", terminalAccountManagerModel.getParentId()));
                accountManagerId = accountManagerModel == null ? "0" : accountManagerModel.getGtId();
            }
        }
        gtStore.put("manager", StrUtil.blankToDefault(accountManagerId, "0"));
        //查询终端协议
        List<TerminalProtocolModel> terminalProtocolModelList = terminalProtocolDao.selectList(new QueryWrapper<TerminalProtocolModel>()
                .eq("terminal_shop_id", terminalShopReq.getId())
                .eq("company_id", terminalShopReq.getCompanyId())
                .eq("check_status", 1)
                .eq("end_status", 2)
                .eq("delete_status", 0)
                .orderBy(true, true, "protocol_type", "create_time"));
        JSONArray gt_store_agreement = new JSONArray();
        for (TerminalProtocolModel terminalProtocolModel : terminalProtocolModelList) {
            JSONObject protocolJson = new JSONObject();
            //type	String	N	协议类型 主协议、附加协议
            protocolJson.put("type", terminalProtocolModel.getProtocolType() + "");
            //protocol_property	String	N 协议名称	陈列包量协议、陈列协议  0, "陈列包量协议",1, "陈列协议"
            protocolJson.put("protocol_property", CodeConstant.PRODUCT_PROTOCOL_TYPE_MAP.get(terminalProtocolModel.getProtocolProperty()));
            //productname	String	N	协议产品名称   国标、酱酒  1:国台国标 2:国台酱酒
            protocolJson.put("productname", CodeConstant.PROTOCOL_PRODUCT_TYPE_MAP.get(terminalProtocolModel.getProductType()));
            //storelevel  Number	N	终端等级
            /* TerminalShopLevelModel terminalShopLevelModel = terminalShopLevelDao.selectById(terminalProtocolModel.getLevelCode());*/
            protocolJson.put("storelevel", terminalProtocolModel.getLevelCode());
            //display_image	String	N	陈列协议照片
            protocolJson.put("display_image", terminalProtocolModel.getProtocolImage());
            //createtime	String	N	创建时间
            protocolJson.put("createtime", terminalProtocolModel.getCreateTime());
            //updatetime	String	N	修改时间
            protocolJson.put("updatetime", terminalProtocolModel.getUpdateTime());
            //添加到集合
            gt_store_agreement.add(protocolJson);
        }
        reqParam.put("gt_store", gtStore);
        reqParam.put("gt_store_agreement", gt_store_agreement);
    }

    /**
     * 推送团购客户
     *
     * @param parentModel 上级终端店信息
     * @param dealerCode  经销商的编码 一定是经销商
     */
    public void sendTogetherClient(ShopModel parentModel, String dealerCode) {
        //先创建团购客户
        ShopModel togetherShoppingShopModel = new ShopModel();
        //店铺名称
        togetherShoppingShopModel.setName("团购客户" + parentModel.getName());
        //联系人
        togetherShoppingShopModel.setLinkman("团购客户" + parentModel.getLinkman());
        Date now = new Date();
        //创建时间
        togetherShoppingShopModel.setCreateTime(now);
        //状态
        togetherShoppingShopModel.setStatus(0);
        //公司id
        togetherShoppingShopModel.setCompanyId(gtCompanyId);
        //更新时间
        togetherShoppingShopModel.setUpdateTime(now);
        //自己的编码 合伙人没有diyCode 记在dealerCode
        togetherShoppingShopModel.setDealerCode(ztUtils.makeTerminalCodeTwo());
        //主编码 填入自己的副编码
        togetherShoppingShopModel.setMainCode(togetherShoppingShopModel.getDealerCode());
        //自己的名称
        togetherShoppingShopModel.setDealerName("采集终端店团购");
        //上级的编码 这里是上级终端店的编码
        togetherShoppingShopModel.setParentDealerCode(parentModel.getDealerCode());
        //添加团购客户
        shopDao.insertValueColumn(togetherShoppingShopModel);

        //发送团购客户至中台
        TogetherClientModel togetherShoppingApplyModel = new TogetherClientModel();
        //经销商编码 一定是经销商
        togetherShoppingApplyModel.setChannelCode(dealerCode);
        //玄武要求终端团购客户用2
        togetherShoppingApplyModel.setType("2");
        //团购类型
        togetherShoppingApplyModel.setTeamType("2");
        //分销商编码 也就是自己的编码
        togetherShoppingApplyModel.setDistributionCode(togetherShoppingShopModel.getDealerCode());
        //分销商子编码 也就是自己的编码
        togetherShoppingApplyModel.setDeputyCode(togetherShoppingShopModel.getDealerCode());
        //分销商名称
        togetherShoppingApplyModel.setDistributionApellation("团购客户" + parentModel.getName());
        //分销商姓名
        togetherShoppingApplyModel.setDistributionName("团购客户" + parentModel.getLinkman());
        togetherShoppingApplyModel.setHigherType("2");
        //上级编码
        togetherShoppingApplyModel.setHigherCode(parentModel.getDealerCode());


        //同步到中台
        log.info("开始将采集团购客户提报到中台，注意，后面的日志写的是分销商，因为团购客户和分销商同步用的是一个接口");
        try {
            // TODO HLQ 服务中心审批注释
            ztUtils.sendTogetherClient(togetherShoppingApplyModel);
            togetherShoppingShopModel.setSysState("2");
        } catch (Exception e) {
            e.printStackTrace();
            togetherShoppingShopModel.setSysState("1");
            togetherShoppingShopModel.setSysMsg(e.getMessage());
        }
        shopDao.updateSysShopMember(togetherShoppingShopModel.getId(), togetherShoppingShopModel.getSysState(), togetherShoppingShopModel.getSysMsg());
    }


    /**
     * @param userId 用户id
     */
    public List<Integer> disposeTerminalAccountManagerList(Integer userId) {
        //先根据客户经理id查询自己绑定的所有经销商关系
        List<TerminalAccountManagerDealerModel> selectDealerList = terminalAccountManagerDealerDao.selectList(new QueryWrapper<TerminalAccountManagerDealerModel>().eq("account_manager_id", userId));

        //创建返回类
        List<Integer> selectData = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(selectDealerList)) {
            //然后根据经销商关系查出来所有的 有关联关系的合同信息上的 采集终端id
            List<Integer> terminalShopIdList = terminalShopContractDao.selectTShopIdListByDealerCode(selectDealerList);

            //在根据客户经理的id查询出来客户经理创建的角色
            List<Integer> terminalShopIdListByAccountManagerList = terminalShopDao.selectShopIdByCreateUser(userId);

            //把两个list组合起来
            if (CollectionUtils.isNotEmpty(terminalShopIdList)) {
                selectData.addAll(terminalShopIdList);
            }

            if (CollectionUtils.isNotEmpty(terminalShopIdListByAccountManagerList)) {
                selectData.addAll(terminalShopIdListByAccountManagerList);
            }
        }

        return selectData;
    }

    @Override
    public void sendZtData(TerminalShopInfoScheduleModel scheduleModel, TerminalShopModel terminalShop, TerminalProtocolModel protocolModel, String name) {
        TerminalShopContractResp contractResp = terminalShopContractDao.selectTerminalShopContractResp(scheduleModel.getTerminalShopId());
        sendZtData(scheduleModel, terminalShop, contractResp, 1, name, protocolModel);
    }

    public void verifyGatherParam(TerminalShopReq terminalShopReq) {

        if (Objects.isNull(terminalShopReq.getTag())) {
            throw new BusinessException("400", "终端标记为空");
        }
        if (null == terminalShopReq.getShopType()) {
            throw new BusinessException("400", "终端类型为空");
        }

        //先判断终端类型 因为会员终端类型和其他终端类型不同
        if (5 != terminalShopReq.getShopType()) {

            //国台让去掉区的校验
//            if (StringUtils.isBlank(terminalShopReq.getDistrict())) {
//                throw new BusinessException("400", "终端店区为空");
//            }

            if (StringUtils.isBlank(terminalShopReq.getProvince())) {
                throw new BusinessException("400", "终端店省为空");
            }

            if (StringUtils.isBlank(terminalShopReq.getCity())) {
                throw new BusinessException("400", "终端店市为空");
            }

            if (StringUtils.isBlank(terminalShopReq.getAddress())) {
                throw new BusinessException("400", "终端店地址为空");
            }

            if (StringUtils.isBlank(terminalShopReq.getShopName())) {
                throw new BusinessException("400", "终端名称为空");
            }

            //团购终端不判断营业执照等信息
            if (2 != terminalShopReq.getShopType()) {
                if (StringUtils.isBlank(terminalShopReq.getHeadImg())) {
                    throw new BusinessException("400", "终端门头照为空");
                }
            } else {
                //2023-01-17 如果是团购终端 网店建立理由也是必填参数
//                if (StringUtils.isBlank(terminalShopReq.getNetworkPointEstablishReason())) {
//                    throw new BusinessException("400", "团购终端的网店建立理由为空");
//                }
            }

        } else {
            //如果走else 说明终端类型是5 是会员终端
            //判断必填参数
            if (StringUtils.isBlank(terminalShopReq.getLeaderName())) {
                throw new BusinessException("400", "会员终端店姓名为空");
            }
            if (null == terminalShopReq.getGender()) {
                throw new BusinessException("400", "会员终端店性别为空");
            }

            if (null == terminalShopReq.getBirthday()) {
                throw new BusinessException("400", "会员终端店出生年月为空");
            }

            if (null == terminalShopReq.getAge()) {
                throw new BusinessException("400", "会员终端店年龄为空");
            }

            if (StringUtils.isBlank(terminalShopReq.getWorkUnit())) {
                throw new BusinessException("400", "会员终端店工作单位为空");
            }

            if (StringUtils.isBlank(terminalShopReq.getAddress())) {
                throw new BusinessException("400", "会员终端店联系地址为空");
            }

            if (StringUtils.isBlank(terminalShopReq.getPosition())) {
                throw new BusinessException("400", "会员终端店职务为空");
            }
        }

    }


    /**
     * @param shopId 终端店id
     * @param status 是否检测已经激活 0不检测 1检测
     */
    public TerminalShopReq disposeParamTerminalShopReq(Integer shopId,
                                                       Integer status,
                                                       String memberShopId) {
        //先根据终端店id查询数据
        TerminalShopModel selectData = terminalShopDao.selectById(shopId);

        if(null == selectData && StringUtils.isNotBlank(memberShopId)){
            // 如果shopId查出来为空 用memberShopId去查询一下
            LambdaQueryWrapper<TerminalShopModel> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(TerminalShopModel::getMemberShopId,memberShopId);
            selectData = terminalShopDao.selectOne(lambdaQueryWrapper);
        }


        //判断是否为空
        if (null == selectData) {
            throw new BusinessException("400", "根据该终端店id查询不到终端店数据");
        }

        //判断是否需要检测激活
        if (1 == status) {
            if (1 == selectData.getStatus()) {
                throw new BusinessException("400", "该终端店已经审核通过并且激活 拒绝二次激活");
            }
        }

        //如果不为空 拼接req参数
        TerminalShopReq terminalShopReq = new TerminalShopReq();
        //主键id
        terminalShopReq.setId(selectData.getId());
        //终端店铺名称
        terminalShopReq.setShopName(selectData.getShopName());
        //终端负责人姓名
        terminalShopReq.setLeaderName(selectData.getLeaderName());
        //终端负责人电话
        terminalShopReq.setLeaderPhone(selectData.getLeaderPhone());
        //标记 1-普通、2-多关注、3-重点关注
        terminalShopReq.setTag(selectData.getTag());
        //终端门头照
        terminalShopReq.setHeadImg(selectData.getHeadImg());
        //备注
        terminalShopReq.setRemark(selectData.getRemark());
        //省
        terminalShopReq.setProvince(selectData.getProvince());
        //市
        terminalShopReq.setCity(selectData.getCity());
        //区
        terminalShopReq.setDistrict(selectData.getDistrict());
        //详细地址
        terminalShopReq.setAddress(selectData.getAddress());
        //经度
        terminalShopReq.setLongitude(selectData.getLongitude());
        //纬度
        terminalShopReq.setLatitude(selectData.getLatitude());
        //终端类型 1-烟酒店、2-专卖店、3-餐饮店、4-商超
        terminalShopReq.setType(selectData.getType());
        //是否形象店 0- 否 1-是
        terminalShopReq.setIsImage(selectData.getIsImage());
        //形象店门头照
        terminalShopReq.setImageHeadPicture(selectData.getImageHeadPicture());
        //终端店长姓名
        terminalShopReq.setKeeperName(selectData.getKeeperName());
        //终端店长手机
        terminalShopReq.setKeeperPhone(selectData.getKeeperPhone());
        //店铺面积
        terminalShopReq.setShopArea(selectData.getShopArea());
        //有无营业执照
        terminalShopReq.setWhetherLicense(selectData.getWhetherLicense());
        //营业执照照片
        terminalShopReq.setLicenseImg(selectData.getLicenseImg());
        //营业执照编号
        terminalShopReq.setLicenseCode(selectData.getLicenseCode());
        //0未激活 1激活 2客户经理审核中 3客户经理审核失败 4 中台审核中 5中台审核失败
        terminalShopReq.setStatus(selectData.getStatus());
        //是否删除 0-未删除 1-已删除
        terminalShopReq.setIsDelete(selectData.getIsDelete());
        //创建时间
        terminalShopReq.setCreateTime(selectData.getCreateTime());
        //创建人
        terminalShopReq.setCreateUser(selectData.getCreateUser());
        //修改时间
        terminalShopReq.setUpdateTime(selectData.getUpdateTime());
        //商户Id
        terminalShopReq.setCompanyId(selectData.getCompanyId());
        //联盟终端店的id
        terminalShopReq.setMemberShopId(selectData.getMemberShopId());
        //主编码
        terminalShopReq.setMainCode(selectData.getMainCode());
        //副编码
        terminalShopReq.setDeputyCode(selectData.getDeputyCode());
        //新终端类型 0:渠道终端 1:餐饮终端 2:团购终端 3:企业终端 4:连锁终端 5会员终端
        terminalShopReq.setShopType(selectData.getShopType());
        //企业名称
        terminalShopReq.setEnterpriseName(selectData.getEnterpriseName());
        //食品经营许可证
        terminalShopReq.setFoodBusinessLicense(selectData.getFoodBusinessLicense());
        //是否是自营终端
        terminalShopReq.setWhetherProprietaryTrading(selectData.getWhetherProprietaryTrading());
        //收货仓库省
        terminalShopReq.setReceivingWarehouseProvince(selectData.getReceivingWarehouseProvince());
        //收货仓库市
        terminalShopReq.setReceivingWarehouseCity(selectData.getReceivingWarehouseCity());
        //收货仓库区
        terminalShopReq.setReceivingWarehouseDistrict(selectData.getReceivingWarehouseDistrict());
        //收货仓库地址
        terminalShopReq.setReceivingWarehouseAddress(selectData.getReceivingWarehouseAddress());
        //终端等级编码
        terminalShopReq.setLevelCode(selectData.getLevelCode());
        //联系人姓名
        terminalShopReq.setContactName(selectData.getContactName());
        //联系人级别
        terminalShopReq.setContactLevel(selectData.getContactLevel());
        //联系人手机号
        terminalShopReq.setContactPhone(selectData.getContactPhone());
        //收款方式 0:支付宝 1:微信 2:银行
        terminalShopReq.setReceivingPaymentType(selectData.getReceivingPaymentType());
        //收款人名称
        terminalShopReq.setReceivingPaymentName(selectData.getReceivingPaymentName());
        //收款人账户
        terminalShopReq.setReceivingPaymentAccount(selectData.getReceivingPaymentAccount());
        //开户行
        terminalShopReq.setReceivingPaymentBank(selectData.getReceivingPaymentBank());
        //收款人账户图片
        terminalShopReq.setReceivingPaymentAccountPicture(selectData.getReceivingPaymentAccountPicture());
        //审核人id
        terminalShopReq.setAuditUserId(selectData.getAuditUserId());
        //审核结果
        terminalShopReq.setAuditResult(selectData.getAuditResult());
        //终端协议 (0包量协议,1陈列协议 两个都有就是0,1)
        terminalShopReq.setShopAgreement(selectData.getShopAgreement());
        //会员终端特殊字段 0是男 1是女
        terminalShopReq.setGender(selectData.getGender());
        //会员终端特殊字段 生日
        terminalShopReq.setBirthday(selectData.getBirthday());
        //会员终端特殊字段 年龄
        terminalShopReq.setAge(selectData.getAge());
        //会员终端特殊字段 工作单位
        terminalShopReq.setWorkUnit(selectData.getWorkUnit());
        //会员终端特殊字段 职务
        terminalShopReq.setPosition(selectData.getPosition());
        //会员终端特殊字段 个人爱好
        terminalShopReq.setPersonalPreference(selectData.getPersonalPreference());
        //会员终端特殊字段 经销商联系方式
        terminalShopReq.setDealerContactType(selectData.getDealerContactType());
        //会员终端特殊字段 初次购买产品名称
        terminalShopReq.setFirstBuyGoodsName(selectData.getFirstBuyGoodsName());
        //会员终端特殊字段 初次购买产品数量(箱)
        terminalShopReq.setFirstBuyGoodsNumber(selectData.getFirstBuyGoodsNumber());
        //会员终端特殊字段 初次积分
        terminalShopReq.setFirstScore(selectData.getFirstScore());
        //会员终端特殊字段 登记表图片
        terminalShopReq.setRegistrationFormPicture(selectData.getRegistrationFormPicture());

        //查询下属的合同数据
        TerminalShopContractResp primaryContractModel = terminalShopContractDao.selectTerminalShopContractResp(terminalShopReq.getId());
        //如果合同不为空 根据主键查出下面的协议产品
        if (null != primaryContractModel) {

            //查询包量协议产品
            List<TerminalShopContractProductModel> packageQuantityAgreementProductList =
                    terminalShopContractProductDao.selectList(new QueryWrapper<TerminalShopContractProductModel>()
                            .eq("contract_id", primaryContractModel.getId())
                            .eq("agreement_type", 0)
                    );

            //填入包量协议产品
            terminalShopReq.setPackageQuantityAgreementProductList(packageQuantityAgreementProductList);


            //查询陈列协议产品
            List<TerminalShopContractProductModel> displayAgreementProductList =
                    terminalShopContractProductDao.selectList(new QueryWrapper<TerminalShopContractProductModel>()
                            .eq("contract_id", primaryContractModel.getId())
                            .eq("agreement_type", 1)
                    );

            //填入陈列协议产品
            terminalShopReq.setDisplayAgreementProductList(displayAgreementProductList);

            //创建合同请求类
            TerminalShopContractReq terminalShopContractReq = new TerminalShopContractReq();
            //填入数据
            //主键id
            terminalShopContractReq.setId(primaryContractModel.getId());
            //合同类型(0:主品合同,1:酱酒合同)
            terminalShopContractReq.setContractType(primaryContractModel.getContractType());
            //经销商编码
            terminalShopContractReq.setDealerCode(primaryContractModel.getDealerCode());
            //合同编码
            terminalShopContractReq.setContractCode(primaryContractModel.getContractCode());
            //陈列协议照片
            terminalShopContractReq.setDisplayImage(primaryContractModel.getDisplayImage());
            //包量协议照片
            terminalShopContractReq.setPackageQuantityImage(primaryContractModel.getPackageQuantityImage());

            //要把照片放到外层
            terminalShopReq.setDisplayImage(primaryContractModel.getDisplayImage());
            terminalShopReq.setPackageQuantityImage(primaryContractModel.getPackageQuantityImage());

            //包量的产品名称
            terminalShopReq.setPackageQuantityName(primaryContractModel.getPackageQuantityName());
            //包量的陈列面数量
            terminalShopReq.setPackageQuantityDisplaySurface(primaryContractModel.getPackageQuantityDisplaySurface());
            //包量的陈列费用
            terminalShopReq.setPackageQuantityDisplayCost(primaryContractModel.getPackageQuantityDisplayCost());
            //包量的全年进货量
            terminalShopReq.setPackageQuantityReplenishStockQuantity(primaryContractModel.getPackageQuantityReplenishStockQuantity());
            //陈列的产品名称
            terminalShopReq.setDisplayName(primaryContractModel.getDisplayName());
            //陈列的陈列面数量
            terminalShopReq.setDisplayDisplaySurface(primaryContractModel.getDisplayDisplaySurface());
            //陈列的陈列费用
            terminalShopReq.setDisplayDisplayCost(primaryContractModel.getDisplayDisplayCost());
            //陈列的每月进货量
            terminalShopReq.setDisplayReplenishStockQuantity(primaryContractModel.getDisplayReplenishStockQuantity());

            //2023-01-16修改协议产品类型结构
            //陈列面(不少于)
            terminalShopReq.setDisplaySurface(primaryContractModel.getDisplaySurface());
            //月进货数(箱)
            terminalShopReq.setMonthScanInNum(primaryContractModel.getMonthScanInNum());
            //年进货数(箱)
            terminalShopReq.setYearScanInNum(primaryContractModel.getYearScanInNum());
            //陈列奖励(分/月)
            terminalShopReq.setDisplayAmount(primaryContractModel.getDisplayAmount());
            //年度包量奖励(箱)
            terminalShopReq.setPackageAmount(primaryContractModel.getPackageAmount());

            //

            //填入合同
            terminalShopReq.setPrimaryContract(terminalShopContractReq);
        }

        //返回
        return terminalShopReq;
    }


    /**
     * @author: HLQ
     * @Date: 2023/3/24 10:07
     * @Description:待办审批列表
     */
    @Override
    public List<TerminalShopNodeResp> selectTerminalShopNode(TerminalShopNodeReq terminalShopNodeReq, Integer page, Integer limit) {
        Integer userType = terminalShopNodeReq.getUserType();
        PageHelper.startPage(page, limit);
        List<TerminalShopNodeResp> shopNodeRespList = new ArrayList<>();
        if (userType == 0) { //客户经理
            shopNodeRespList = terminalShopNodeDao.selectAccountManagerTerminalShopNode(terminalShopNodeReq);
            List<TerminalShopNodeResp> collect = shopNodeRespList.stream().filter(e -> e.getProtocolId() > 0).collect(Collectors.toList());
            List<Integer> ids = collect.stream().map(TerminalShopNodeResp::getProtocolId).collect(Collectors.toList());
            List<TerminalShopNodeResp> respList = new ArrayList<>();
            if (ids.size() > 0) {
                respList = terminalShopNodeDao.selectProtocolCheckStatus(ids);
            }
            List<TerminalShopNodeResp> finalRespList = respList;
            shopNodeRespList.forEach(e -> {
                // 节点类型 1:终端激活 2:终端协议新增 3:终端资料变更 4:终端协议变更 5下单审批 6收货审批 7 地址变更
                // 后期需要整理  现在没有时间处理
                Integer nodeType = e.getNodeType();
                String managerShowNodeName = "";
                Integer managerShowNodeState = 0;
                switch (nodeType){
                    case 1:
                    case 3:
                    case 2:
                    case 4:
                        LambdaQueryWrapper<TerminalShopNodeModel> lqw = Wrappers.lambdaQuery();
                        lqw.eq(TerminalShopNodeModel::getTerminalShopId, e.getTerminalShopId());
                        lqw.eq(TerminalShopNodeModel::getScheduleShopId, e.getScheduleShopId());
                        lqw.gt(TerminalShopNodeModel::getId, e.getId());
                        lqw.eq(TerminalShopNodeModel::getNodeType,e.getNodeType());
                        lqw.eq(TerminalShopNodeModel::getNodeLevel,1);
                        lqw.last(" limit 1");
                        TerminalShopNodeModel shopNode = terminalShopNodeDao.selectOne(lqw);
                        if(Objects.nonNull(shopNode)){
                            String nodeStatus = shopNode.getNodeStatus();
                            String isBack = shopNode.getIsBack();
                            if("0".equals(nodeStatus) ){
                                managerShowNodeState = 0;
                                managerShowNodeName = "审核中心审批中";
                            }else if("1".equals(nodeStatus) && "1".equals(isBack)){
                                managerShowNodeState = 2;
                                managerShowNodeName = "审核中心驳回";
                            }else {
                                managerShowNodeState = 1;
                                managerShowNodeName = "通过";
                            }
                        }else{
                            // 兼容老数据问题
                            LambdaQueryWrapper<TerminalShopNodeModel> qw = Wrappers.lambdaQuery();
                            qw.eq(TerminalShopNodeModel::getTerminalShopId, e.getTerminalShopId());
                            qw.eq(TerminalShopNodeModel::getScheduleShopId, e.getScheduleShopId());
                            qw.eq(TerminalShopNodeModel::getNodeType,e.getNodeType());
                            qw.orderByDesc(TerminalShopNodeModel::getId);
                            qw.last(" limit 1");
                            TerminalShopNodeModel shopNodeModel = terminalShopNodeDao.selectOne(qw);
                            String nodeStatus = shopNodeModel.getNodeStatus();
                            String isBack = shopNodeModel.getIsBack();
                            if("1".equals(nodeStatus) && "1".equals(isBack)){
                                managerShowNodeState = 1;
                                managerShowNodeName = "客户经理驳回";
                            }else if("1".equals(nodeStatus)){
                                managerShowNodeState = 1;
                                managerShowNodeName = "通过";
                            }else{
                                managerShowNodeState = 0;
                                managerShowNodeName = "客户经理审批中";
                            }
                        }
                        break;
                    case 5:
                    case 6:
                        // 5下单审批 6收货审批 7 地址变更
                        LambdaQueryWrapper<TerminalShopNodeModel> lqwm = Wrappers.lambdaQuery();
                        lqwm.eq(TerminalShopNodeModel::getShopId, e.getShopId());
                        lqwm.eq(TerminalShopNodeModel::getOrderId, e.getNodeSourceId());
                        lqwm.gt(TerminalShopNodeModel::getId, e.getId());
                        lqwm.eq(TerminalShopNodeModel::getNodeType,e.getNodeType());
                        lqwm.eq(TerminalShopNodeModel::getNodeLevel,1);
                        lqwm.last(" limit 1");
                        TerminalShopNodeModel node = terminalShopNodeDao.selectOne(lqwm);
                        if(Objects.nonNull(node)){
                            String status = node.getNodeStatus();
                            String isback = node.getIsBack();
                            if("0".equals(status) ){
                                managerShowNodeState = 0;
                                managerShowNodeName = "OA审批中";
                            }else if("1".equals(status) && "1".equals(isback)){
                                managerShowNodeState = 2;
                                managerShowNodeName = "OA驳回";
                            }else {
                                managerShowNodeState = 1;
                                managerShowNodeName = "通过";
                            }
                        }else{
                            // 兼容老数据问题
                            LambdaQueryWrapper<TerminalShopNodeModel> qw = Wrappers.lambdaQuery();
                            qw.eq(TerminalShopNodeModel::getShopId, e.getShopId());
                            qw.eq(TerminalShopNodeModel::getOrderId, e.getNodeSourceId());
                            qw.eq(TerminalShopNodeModel::getNodeType,e.getNodeType());
                            qw.orderByDesc(TerminalShopNodeModel::getId);
                            qw.last(" limit 1");
                            TerminalShopNodeModel shopNodeModel = terminalShopNodeDao.selectOne(qw);
                            String nodeStatus = shopNodeModel.getNodeStatus();
                            String isBack = shopNodeModel.getIsBack();
                            if("1".equals(nodeStatus) && "1".equals(isBack)){
                                managerShowNodeState = 1;
                                managerShowNodeName = "客户经理驳回";
                            }else if("1".equals(nodeStatus)){
                                managerShowNodeState = 1;
                                managerShowNodeName = "通过";
                            }else{
                                managerShowNodeState = 0;
                                managerShowNodeName = "客户经理审批中";
                            }
                        }
                        break;
                    case 7:
                        // 7 地址变更
                        LambdaQueryWrapper<TerminalShopNodeModel> lqwdz = Wrappers.lambdaQuery();
                        lqwdz.eq(TerminalShopNodeModel::getTerminalShopId, e.getTerminalShopId());
                        lqwdz.eq(TerminalShopNodeModel::getNodeSourceId, e.getNodeSourceId());
                        lqwdz.gt(TerminalShopNodeModel::getId, e.getId());
                        lqwdz.eq(TerminalShopNodeModel::getNodeType,e.getNodeType());
                        lqwdz.eq(TerminalShopNodeModel::getNodeLevel,1);
                        lqwdz.last(" limit 1");
                        TerminalShopNodeModel nodeDz = terminalShopNodeDao.selectOne(lqwdz);
                        if(Objects.nonNull(nodeDz)){
                            String status = nodeDz.getNodeStatus();
                            String isback = nodeDz.getIsBack();
                            if("0".equals(status) ){
                                managerShowNodeState = 0;
                                managerShowNodeName = "OA审批中";
                            }else if("1".equals(status) && "1".equals(isback)){
                                managerShowNodeState = 2;
                                managerShowNodeName = "OA驳回";
                            }else {
                                managerShowNodeState = 1;
                                managerShowNodeName = "通过";
                            }
                        }else{
                            // 兼容老数据问题
                            LambdaQueryWrapper<TerminalShopNodeModel> qw = Wrappers.lambdaQuery();
                            qw.eq(TerminalShopNodeModel::getTerminalShopId, e.getTerminalShopId());
                            qw.eq(TerminalShopNodeModel::getNodeSourceId, e.getNodeSourceId());
                            qw.eq(TerminalShopNodeModel::getNodeType,e.getNodeType());
                            qw.orderByDesc(TerminalShopNodeModel::getId);
                            qw.last(" limit 1");
                            TerminalShopNodeModel shopNodeModel = terminalShopNodeDao.selectOne(qw);
                            String nodeStatus = shopNodeModel.getNodeStatus();
                            String isBack = shopNodeModel.getIsBack();
                            if("1".equals(nodeStatus) && "1".equals(isBack)){
                                managerShowNodeState = 1;
                                managerShowNodeName = "客户经理驳回";
                            }else if("1".equals(nodeStatus)){
                                managerShowNodeState = 1;
                                managerShowNodeName = "通过";
                            }else{
                                managerShowNodeState = 0;
                                managerShowNodeName = "客户经理审批中";
                            }
                        }
                        break;

                    default:
                        break;
                }
                e.setIsOa(0);
                e.setManagerShowNodeName(managerShowNodeName);
                e.setManagerShowNodeState(managerShowNodeState);
                if (nodeType == 5 || nodeType == 6) {
                    if ("0".equals(e.getNodeStatus())) {
                        Integer monthTatolQty = shopDealerOrderCommonDao.getMonthTotalQty(e.getShopId(), e.getGoodsCode());
                        if (Objects.isNull(monthTatolQty)) {
                            monthTatolQty = 0;
                        }

                        e.setMonthTatolQty(monthTatolQty);
                        String thresholdVal = e.getThresholdVal();
                        if (StringUtils.isBlank(thresholdVal)) {//未配置OA审批的参数
                            e.setIsOa(0);
                        } else {
                            String[] fzVals = thresholdVal.split("--");
                            if ("0".equals(fzVals[0]) && !"0".equals(fzVals[1]) && Integer.valueOf(fzVals[1]) <= monthTatolQty) {
                                e.setIsOa(1);
                            } else if (!"0".equals(fzVals[0]) && Integer.valueOf(fzVals[0]) <= monthTatolQty) {
                                e.setIsOa(1);
                            } else {
                                e.setIsOa(0);
                            }
                        }
                    } else {
                        e.setMonthTatolQty(e.getApprovalQty());
                    }
                } else {
                    Integer protocolId = e.getProtocolId();
                    if (protocolId > 0) {
                        TerminalShopNodeResp resp = finalRespList.stream().filter(el -> protocolId.toString().equals(el.getProtocolId().toString())).findFirst().orElse(null);
                        if (Objects.nonNull(resp)) {
                            e.setApprovalStatus(Integer.valueOf(resp.getNodeStatus()));
                        }
                    }
                }
            });
        } else {
            if (userType == 5) {
                terminalShopNodeReq.setUserType(2);
            } else {
                terminalShopNodeReq.setUserType(1);
            }
            shopNodeRespList = terminalShopNodeDao.selectStaffTerminalShopNode(terminalShopNodeReq);
            shopNodeRespList.forEach(e -> {
                Integer nodeType = e.getNodeType();
                e.setManagerShowNodeName("");
                e.setManagerShowNodeState(-1);
            });
        }
        log.info(JSONObject.toJSONString(shopNodeRespList));
        return shopNodeRespList;
    }

    /**
     * @author: HLQ
     * @Date: 2023/5/8 15:09
     * @Description: 处理预备终端转正式终端时的积分数据
     */
    @Transactional
    @Override
    public void handlePrepareTerminalData(Integer shopId) {
        //处理终端的积分 t_terminal_reward_record_prepare
        QueryWrapper<TerminalRewardRecordPrepare> tqw = new QueryWrapper<>();
        tqw.eq("is_delete", 0);
        tqw.eq("record_id", 0);
        tqw.eq("is_flag", 0);
        tqw.eq("shop_id", shopId);
        List<TerminalRewardRecordPrepare> recordPrepareList = terminalRewardRecordPrepareMapper.selectList(tqw);
        if (CollectionUtils.isNotEmpty(recordPrepareList)) {
            log.info("终端id为:{},共有{}终端积分记录", shopId, recordPrepareList.size());
            for (TerminalRewardRecordPrepare prepare : recordPrepareList) {
                TerminalRewardRecordModel rewardRecord = new TerminalRewardRecordModel();
                BeanUtils.copyProperties(prepare, rewardRecord);
                rewardRecord.setId(null);
                terminalRewardRecordDao.insert(rewardRecord);
                prepare.setRecordId(rewardRecord.getId());
                terminalRewardRecordPrepareMapper.updateById(prepare);
            }
            //处理终端得的奖励数据之和加到各个终端上
            Map<Integer, List<TerminalRewardRecordPrepare>> recordListMap = recordPrepareList.stream().collect(Collectors.groupingBy(TerminalRewardRecordPrepare::getShopId));
            for (Integer itemShop : recordListMap.keySet()) {
                List<TerminalRewardRecordPrepare> prepareList = recordListMap.get(itemShop);
                BigDecimal sumAwardVirtualAmount = Optional.ofNullable(prepareList)
                        .orElse(new ArrayList<>())
                        .stream()
                        .filter(x -> x.getAmount() != null)
                        .map(TerminalRewardRecordPrepare::getAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                shopDao.setVirtualAmount(sumAwardVirtualAmount, itemShop);
            }
        }
        //处理经销商得的奖励数据 t_cloud_dealer_reward_record_prepare
        QueryWrapper<CloudDealerRewardRecordPrepare> cqw = new QueryWrapper<>();
        cqw.eq("is_delete", 0);
        cqw.eq("record_id", 0);
        cqw.eq("is_flag", 0);
        cqw.eq("shop_id", shopId);
        List<CloudDealerRewardRecordPrepare> dealerRewardList = cloudDealerRewardRecordPrepareMapper.selectList(cqw);
        if (CollectionUtils.isNotEmpty(dealerRewardList)) {
            log.info("终端id为:{},共有{}经销商积分记录", shopId, dealerRewardList.size());
            for (CloudDealerRewardRecordPrepare prepare : dealerRewardList) {
                CloudDealerRewardRecordModel cloudDealerRewardRecordModel = new CloudDealerRewardRecordModel();
                BeanUtils.copyProperties(prepare, cloudDealerRewardRecordModel);
                cloudDealerRewardRecordModel.setId(null);
                cloudDealerRewardRecordDao.insert(cloudDealerRewardRecordModel);
                prepare.setRecordId(cloudDealerRewardRecordModel.getId());
                cloudDealerRewardRecordPrepareMapper.updateById(prepare);
            }
            //处理经销商得的奖励数据之和加到各个经销商上
            Map<String, List<CloudDealerRewardRecordPrepare>> recordListMap = dealerRewardList.stream().collect(Collectors.groupingBy(CloudDealerRewardRecordPrepare::getDealerCode));
            for (String dealerCode : recordListMap.keySet()) {
                List<CloudDealerRewardRecordPrepare> prepareList = recordListMap.get(dealerCode);
                BigDecimal sumAwardVirtualAmount = Optional.ofNullable(prepareList)
                        .orElse(new ArrayList<>())
                        .stream()
                        .filter(x -> x.getRewardAmount() != null)
                        .map(CloudDealerRewardRecordPrepare::getRewardAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                dealerInfoCommonDao.addVirtualAmount(dealerCode, sumAwardVirtualAmount);
            }
        }
    }

    /**
     * @author: HLQ
     * @Date: 2023/5/8 17:08
     * @Description: 数据推送到中台
     */
    public void sendZtData(TerminalShopInfoScheduleModel scheduleModel, TerminalShopModel terminalShopModel,
                           TerminalShopContractResp terminalShopContractResp, int insertOrUpdate, String name, TerminalProtocolModel terminalProtocolModel) {
        RequestLog requestLog = new RequestLog();
        long ernterSystemTime = System.currentTimeMillis();
        try {
            //封装请求参数
            //JSONObject reqParam = getReqParam(insertOrUpdate, scheduleModel, terminalShopContractResp, "1", null, terminalProtocolModel);
            // 使用新协议
            JSONObject reqParam = getReqParamNewProtocol(insertOrUpdate, scheduleModel, terminalShopContractResp, "1", null, null);

            String token = ztUtils.getXwToken();
            //请求参数打印
            log.info("我是请求同步终端店数据的实体参数:{}", JSON.toJSONString(reqParam));
            log.info("我是请求同步终端店数据的token参数:{}", token);

            requestLog.setReqName("【" + name + "】推送终端信息:" + scheduleModel.getShopName());
            requestLog.setReqType(4);
            requestLog.setReqUrlPath(sendTerminalDataUrl);
            requestLog.setCreateDate(new Date());
            requestLog.setReqJson(reqParam.toJSONString());
            requestLog.setReqKey(scheduleModel.getDeputyCode());
            String rtnJson = HttpUtil.createPost(sendTerminalDataUrl).header("token", token).header("Content-Type", "application/json").body(reqParam.toJSONString()).execute().body();
            requestLog.setResJson(rtnJson);
            terminalShopModel.setSysState("1");
            if (com.intelliquor.cloud.shop.job.util.StringUtils.hasNull(rtnJson)) {
                JSONObject resp = JSONObject.parseObject(rtnJson);
                log.info("[返回参数]同步中台的采集终端---{}", JSON.toJSONString(resp));
                if (resp != null && resp.getJSONObject("resp_data") != null) {
                    JSONObject result = resp.getJSONObject("resp_data").getJSONObject("result");
                    if (null != result && result.get("success").equals("True")) {
                        terminalShopModel.setSysState("2");
                        requestLog.setResCode("0");
                    } else {
                        requestLog.setResCode("-1");
                        requestLog.setResMsg(resp.toJSONString());
                        throw new BusinessException("400", "返回的参数:" + resp.toJSONString());
                    }
                } else {
                    requestLog.setResCode("-1");
                    requestLog.setResMsg(resp.toJSONString());
                    throw new BusinessException("400", "返回的参数:" + resp.toJSONString());
                }
            } else {
                requestLog.setResCode("-1");
                requestLog.setResMsg("没有返回值");
                throw new BusinessException("400", "没有返回值");
            }
        } catch (Exception e) {
            e.printStackTrace();
            requestLog.setResCode("-1");
            requestLog.setResMsg(e.getMessage());
            throw new BusinessException("400", "同步中台的采集终端数据失败:" + e.getMessage());
        } finally {
            long outSystemTime = System.currentTimeMillis();
            String diffTime = DateUtils.longToHMS(outSystemTime - ernterSystemTime);
            requestLog.setReqTime(diffTime);
            requestLogService.insertLog(requestLog);
        }
    }


    @Override
    @Transactional
    public void syncReceiptOAApproval() {
        LambdaQueryWrapper<TerminalShopNodeModel> qw = Wrappers.lambdaQuery();
        qw.eq(TerminalShopNodeModel::getNodeStatus, 0);
        qw.eq(TerminalShopNodeModel::getNodeType, 6);
        qw.eq(TerminalShopNodeModel::getNodeLevel, 1);
        /*qw.eq(TerminalShopNodeModel::getOaId, "189dd22966498be0ad27d444e1a99d5c");*/
        /*// 测试代码
        int[] arr = {63361,63306,63218,63207,63145,63109,63106,63105};
        List<Integer> idList = Arrays.stream(arr).boxed().collect(Collectors.toList());
        sendOaOrderApprovalUrl = "http://oa.guotaijiu.com/api/km-review/kmReviewRestService/queryApproveProcess";
        qw.in("order_id", idList);*/
        List<TerminalShopNodeModel> shopNodeModelList = terminalShopNodeDao.selectList(qw);
        log.info("共有" + shopNodeModelList.size() + "条数据需要处理");
        HttpHeaders headers = oaUtils.getOaToken();
        for (int i = 0; i < shopNodeModelList.size(); i++) {
            TerminalShopNodeModel terminalShopNodeModel = shopNodeModelList.get(i);
            log.info("共有" + shopNodeModelList.size() + "条数据需要，当前处理到" + i + "条，id=" + terminalShopNodeModel.getId());
            String oaId = terminalShopNodeModel.getOaId();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", oaId);
            log.info("查询OA--->{}的流程信息地址:::{}---->参数:{}", terminalShopNodeModel.getOaId(), sendOaOrderApprovalUrl, JSON.toJSONString(jsonObject));
            HttpEntity<Map<String, Object>> entity = new HttpEntity<Map<String, Object>>(jsonObject, headers);
            RestTemplate template = new RestTemplate();
           /* String post = HttpUtil.createPost(sendTerminalDataUrl).header("Authorization",token).body(jsonObject.toJSONString()).execute().body();
            log.info("返回值:{}",post);*/
            ResponseEntity<Map> exchange = template.exchange(sendOaOrderApprovalUrl, HttpMethod.POST, entity, Map.class);
            Boolean hasAbandon = false;
            List<Map<String, Object>> list = (List<Map<String, Object>>) exchange.getBody().get("data");
            for (Map<String, Object> flowJson : list) {
                String actionkey = flowJson.get("actionKey").toString();
                if(actionkey.endsWith(CommonConstant.FLOW_TYPE_MAP.get("废弃"))){
                    hasAbandon = true;
                    break;
                }
            }

            log.info("查询OA--->{}的流程信息有{}条,返回信息:{}", terminalShopNodeModel.getOaId(), list.size(), exchange.getBody().toString());

            Map<String, Object> flowJson = list.get(list.size() - 1);
            log.info("最后一条审批节点:{}", JSONObject.toJSONString(flowJson));
            String actionkey = flowJson.get("actionKey").toString();
            if (CommonConstant.FLOW_TYPE_MAP.get("流程结束").equals(actionkey) && !hasAbandon) {
                if (list.size() > 3) {
                    flowJson = list.get(list.size() - 2);
                }
                Object handlername = flowJson.get("handlerName");
                String handlerName = "";
                if (Objects.nonNull(handlername)) {
                    handlerName = flowJson.get("handlerName").toString();
                }
                String auditNote = flowJson.get("auditNote").toString();
                String actionDate = flowJson.get("actionDate").toString() + ":00";
                Date date = DateUtils.convert2Date(actionDate, "yyyy-MM-dd HH:mm:ss");
                terminalShopNodeModel.setUpdateName(handlerName);
                terminalShopNodeModel.setUpdateDate(date);
                terminalShopNodeModel.setNodeStatus("1");
                terminalShopNodeModel.setUpdateMsg(auditNote);
                terminalShopNodeDao.updateById(terminalShopNodeModel);

                agreeHandleScore(terminalShopNodeModel);
            } else if (hasAbandon || CommonConstant.FLOW_TYPE_MAP.get("审批人驳回").equals(actionkey)) {
                String handlerName = flowJson.get("handlerName").toString();
                String auditNote = flowJson.get("auditNote").toString();
                String actionDate = flowJson.get("actionDate").toString() + ":00";
                Date date = DateUtils.convert2Date(actionDate, "yyyy-MM-dd HH:mm:ss");
                terminalShopNodeModel.setUpdateName(handlerName);
                terminalShopNodeModel.setUpdateDate(date);
                terminalShopNodeModel.setNodeStatus("1");
                terminalShopNodeModel.setUpdateMsg(auditNote);
                terminalShopNodeModel.setIsBack("1");
                terminalShopNodeDao.updateById(terminalShopNodeModel);

                rejectHandleScore(terminalShopNodeModel);
            } else {
                if (list.size() > 1) {
                    RequestLog requestLog = new RequestLog();
                    long ernterSystemTime = System.currentTimeMillis();
                    requestLog.setReqName("收货审批OA流程:单号=" + terminalShopNodeModel.getOrderNo() + ";oaId=" + oaId);
                    requestLog.setReqType(33);
                    requestLog.setReqUrlPath(sendOaOrderApprovalUrl);
                    requestLog.setCreateDate(new Date());
                    requestLog.setResCode("0");
                    requestLog.setReqJson(JSONObject.toJSONString(entity));
                    requestLog.setResJson(exchange.getBody().toString());
                    long outSystemTime = System.currentTimeMillis();
                    String diffTime = DateUtils.longToHMS(outSystemTime - ernterSystemTime);
                    requestLog.setReqTime(diffTime);
                    requestLogService.insertLog(requestLog);
                }
            }
        }
    }

    private void rejectHandleScore(TerminalShopNodeModel terminalShopNodeModel) {
        String auditNote = terminalShopNodeModel.getUpdateMsg();
        // 积分到正式表  收货记录添加积分
        QueryWrapper<TerminalScanDetailCheckCode> cqwOne = new QueryWrapper<>();
        cqwOne.eq("order_code", terminalShopNodeModel.getOrderNo());
        cqwOne.eq("detail_id", terminalShopNodeModel.getOrderId());
        TerminalScanDetailCheckCode terminalScanDetailCheckCode = terminalScanDetailCheckCodeMapper.selectOne(cqwOne);

        String monthYear = terminalScanDetailCheckCode.getMonthYear();
        QueryWrapper<TerminalScanDetailCheckCode> cqwTwo = new QueryWrapper<>();
        cqwTwo.eq("order_code", terminalShopNodeModel.getOrderNo());
        cqwTwo.eq("month_year", monthYear);
        cqwTwo.eq("has_score", 0);
        List<TerminalScanDetailCheckCode> terminalScanDetailCheckCodeList = terminalScanDetailCheckCodeMapper.selectList(cqwTwo);

        TerminalScanDetailCheckCode updateCode = new TerminalScanDetailCheckCode();
        updateCode.setHasScore(1);
        updateCode.setHandleTime(new Date());
        updateCode.setRemark(auditNote);
        terminalScanDetailCheckCodeMapper.update(updateCode, cqwTwo);


        List<Integer> detailIdList = terminalScanDetailCheckCodeList.stream().map(TerminalScanDetailCheckCode::getDetailId).collect(Collectors.toList());

        QueryWrapper<TerminalRewardRecordPrepare> terminalQW = new QueryWrapper<>();
        terminalQW.in("detail_id", detailIdList);
        terminalQW.eq("record_id", 0);

        QueryWrapper<CloudDealerRewardRecordPrepare> dealerQW = new QueryWrapper<>();
        dealerQW.in("detail_id", detailIdList);
        dealerQW.eq("record_id", 0);

        noGrantScore("审批被驳回【" + auditNote + ",不需要处理】", terminalQW, dealerQW);
    }

    private void agreeHandleScore(TerminalShopNodeModel terminalShopNodeModel) {
        String auditNote = terminalShopNodeModel.getUpdateMsg();
        Integer shopId = terminalShopNodeModel.getShopId();

        // 积分到正式表  收货记录添加积分
        QueryWrapper<TerminalScanDetailCheckCode> cqwOne = new QueryWrapper<>();
        /*  cqwOne.eq("order_code", terminalShopNodeModel.getOrderNo());*/
        cqwOne.eq("detail_id", terminalShopNodeModel.getOrderId());
        TerminalScanDetailCheckCode terminalScanDetailCheckCode = terminalScanDetailCheckCodeMapper.selectOne(cqwOne);

        String monthYear = terminalScanDetailCheckCode.getMonthYear();
        QueryWrapper<TerminalScanDetailCheckCode> cqwTwo = new QueryWrapper<>();
        cqwTwo.eq("order_code", terminalScanDetailCheckCode.getOrderCode());
        cqwTwo.eq("month_year", monthYear);
        cqwTwo.eq("has_score", 0);
        List<TerminalScanDetailCheckCode> terminalScanDetailCheckCodeList = terminalScanDetailCheckCodeMapper.selectList(cqwTwo);

        List<Integer> detailIdList = terminalScanDetailCheckCodeList.stream().map(TerminalScanDetailCheckCode::getDetailId).collect(Collectors.toList());
        QueryWrapper<TerminalScanDetailModel> detailQW = new QueryWrapper<>();
        detailQW.in("id", detailIdList);
        List<TerminalScanDetailModel> detailModelList = terminalScanDetailPlusDao.selectList(detailQW);

        QueryWrapper<TerminalRewardRecordPrepare> terminalQW = new QueryWrapper<>();
        terminalQW.in("detail_id", detailIdList);
        terminalQW.eq("record_id", 0);
        List<TerminalRewardRecordPrepare> terminalPrepareList = terminalRewardRecordPrepareMapper.selectList(terminalQW);

        QueryWrapper<CloudDealerRewardRecordPrepare> dealerQW = new QueryWrapper<>();
        dealerQW.in("detail_id", detailIdList);
        dealerQW.eq("record_id", 0);
        List<CloudDealerRewardRecordPrepare> dealerPrepareList = cloudDealerRewardRecordPrepareMapper.selectList(dealerQW);

        //退货积分不用处理
        Map<Integer, List<TerminalScanDetailModel>> listMap = detailModelList.stream().collect(Collectors.groupingBy(TerminalScanDetailModel::getReturnGoods));
        List<TerminalScanDetailModel> detailListOne = new ArrayList<>();
        List<TerminalScanDetailModel> detailListTwo = new ArrayList<>();
        for (Integer returnGoods : listMap.keySet()) {
            List<TerminalScanDetailModel> detailList = listMap.get(returnGoods);
            if (returnGoods == 0) {
                detailListOne.addAll(detailList);
            } else {
                detailListTwo.addAll(detailList);
            }
        }

        List<TerminalScanDetailModel> gwList = new ArrayList<>();

        if (detailListOne.size() == detailModelList.size() || detailListTwo.size() == detailModelList.size()) {//全正常  全退不处理积分
            TerminalScanDetailCheckCode updateCode = new TerminalScanDetailCheckCode();
            updateCode.setHandleTime(new Date());
            updateCode.setRemark(auditNote);
            // 扫码收货添加积分
            if (detailListOne.size() > 0) {
                updateCode.setHasScore(1);
                grantALLScore(shopId, terminalScanDetailCheckCodeList, detailModelList, terminalPrepareList, dealerPrepareList, gwList);
            } else { //全部退货
                String msg = "【已退货,不需要处理】";
                updateCode.setHasScore(2);
                updateCode.setRemark(auditNote + msg);
                noGrantScore(msg, terminalQW, dealerQW);
            }
            terminalScanDetailCheckCodeMapper.update(updateCode, cqwTwo);
        } else {//退部分
            List<TerminalScanDetailCheckCode> terminalScanDetailCheckCodeOneList = new ArrayList<>();
            List<TerminalRewardRecordPrepare> terminalPrepareOneList = new ArrayList<>();
            List<CloudDealerRewardRecordPrepare> dealerPrepareOneList = new ArrayList<>();
            for (TerminalScanDetailModel terminalScanDetailModel : detailListOne) {
                Integer id = terminalScanDetailModel.getId();
                TerminalScanDetailCheckCode detailCheckCode = terminalScanDetailCheckCodeList.stream().filter(e -> id.equals(e.getDetailId())).findFirst().orElse(null);
                detailCheckCode.setRemark(detailCheckCode.getRemark() + "->" + auditNote);
                detailCheckCode.setHandleTime(new Date());
                detailCheckCode.setHasScore(1);
                terminalScanDetailCheckCodeMapper.updateById(detailCheckCode);
                terminalScanDetailCheckCodeOneList.add(detailCheckCode);

                TerminalRewardRecordPrepare terminalRewardRecordPrepare = terminalPrepareList.stream().filter(e -> id.equals(e.getDetailId())).findFirst().orElse(null);
                terminalPrepareOneList.add(terminalRewardRecordPrepare);

                CloudDealerRewardRecordPrepare cloudDealerRewardRecordPrepare = dealerPrepareList.stream().filter(e -> id.equals(e.getDetailId())).findFirst().orElse(null);
                dealerPrepareOneList.add(cloudDealerRewardRecordPrepare);
            }
            grantALLScore(shopId, terminalScanDetailCheckCodeOneList, detailListOne, terminalPrepareOneList, dealerPrepareOneList, gwList);

            // 已退
            String msg = "【已退货,不需要处理】";
            List<Integer> noScoreIds = new ArrayList<>();
            for (TerminalScanDetailModel terminalScanDetailModel : detailListTwo) {
                Integer id = terminalScanDetailModel.getId();
                TerminalScanDetailCheckCode detailCheckCode = terminalScanDetailCheckCodeList.stream().filter(e -> id.equals(e.getDetailId())).findFirst().orElse(null);
                detailCheckCode.setRemark(detailCheckCode.getRemark() + "->" + auditNote + "->" + msg);
                detailCheckCode.setHandleTime(new Date());
                detailCheckCode.setHasScore(2);
                terminalScanDetailCheckCodeMapper.updateById(detailCheckCode);

                noScoreIds.add(id);
            }
            QueryWrapper<TerminalRewardRecordPrepare> tQW = new QueryWrapper<>();
            tQW.in("detail_id", noScoreIds);
            tQW.eq("record_id", 0);
            QueryWrapper<CloudDealerRewardRecordPrepare> dQW = new QueryWrapper<>();
            dQW.in("detail_id", noScoreIds);
            dQW.eq("record_id", 0);
            noGrantScore(msg, tQW, dQW);
        }
        //同步到顾问
        integralSyncService.send(gwList);
    }

    private void grantALLScore(Integer shopId, List<TerminalScanDetailCheckCode> terminalScanDetailCheckCodeList, List<TerminalScanDetailModel> detailModelList, List<TerminalRewardRecordPrepare> terminalPrepareList, List<CloudDealerRewardRecordPrepare> dealerPrepareList, List<TerminalScanDetailModel> gwList) {
        Map<Integer, BigDecimal> scoreMap = new HashMap<>(); //记录主表的积分之和

        for (TerminalScanDetailModel terminalScanDetailModel : detailModelList) {
            Integer id = terminalScanDetailModel.getId();
            TerminalScanDetailCheckCode checkCode = terminalScanDetailCheckCodeList.stream().filter(e -> id.equals(e.getDetailId())).findFirst().orElse(null);
            terminalScanDetailModel.setVirtualAmount(checkCode.getScore());
            terminalScanDetailPlusDao.updateById(terminalScanDetailModel);
            gwList.add(terminalScanDetailModel);
            Integer balanceId = terminalScanDetailModel.getBalanceId();
            if (scoreMap.containsKey(balanceId)) {
                BigDecimal score = scoreMap.get(balanceId);
                score = score.add(checkCode.getScore());
                scoreMap.put(balanceId, score);
            } else {
                scoreMap.put(balanceId, checkCode.getScore());
            }
        }

        //更新主表的积分
        for (Integer balanceId : scoreMap.keySet()) {
            BigDecimal score = scoreMap.get(balanceId);
            terminalScanDetailPlusDao.updateTerminalScanBalanceScore(balanceId, score);
        }

        // 奖励发到终端
        BigDecimal totalScore = BigDecimal.ZERO;
        for (TerminalRewardRecordPrepare prepare : terminalPrepareList) {
            TerminalRewardRecordModel recordModel = new TerminalRewardRecordModel();
            BeanUtils.copyProperties(prepare, recordModel, "id");
            terminalRewardRecordDao.insert(recordModel);
            prepare.setRecordId(recordModel.getId());
            terminalRewardRecordPrepareMapper.updateById(prepare);
            totalScore = totalScore.add(prepare.getAmount());
        }
        // 更新终端总积分
        if (totalScore.compareTo(BigDecimal.ZERO) > 0) {
            shopDao.addAmount(totalScore, BigDecimal.ZERO, shopId);
        }

        // 奖励发到经销商
        BigDecimal sumAwardVirtualAmount = BigDecimal.ZERO;
        String dealerCode = "";
        for (CloudDealerRewardRecordPrepare prepare : dealerPrepareList) {
            CloudDealerRewardRecordModel cloudDealerRewardRecordModel = new CloudDealerRewardRecordModel();
            BeanUtils.copyProperties(prepare, cloudDealerRewardRecordModel, "id");
            cloudDealerRewardRecordDao.insert(cloudDealerRewardRecordModel);
            prepare.setRecordId(cloudDealerRewardRecordModel.getId());
            cloudDealerRewardRecordPrepareMapper.updateById(prepare);
            sumAwardVirtualAmount = sumAwardVirtualAmount.add(prepare.getRewardAmount());
            dealerCode = prepare.getDealerCode();
        }
        // 更新经销商总积分
        dealerInfoCommonDao.addVirtualAmount(dealerCode, sumAwardVirtualAmount);
    }

    private void noGrantScore(String msg, QueryWrapper<TerminalRewardRecordPrepare> terminalQW, QueryWrapper<CloudDealerRewardRecordPrepare> dealerQW) {
        TerminalRewardRecordPrepare terminalPrepare = new TerminalRewardRecordPrepare();
        terminalPrepare.setRemark(msg + terminalPrepare.getRemark());
        terminalPrepare.setRecordId(-1L);
        terminalRewardRecordPrepareMapper.update(terminalPrepare, terminalQW);

        CloudDealerRewardRecordPrepare dealerPrepare = new CloudDealerRewardRecordPrepare();
        dealerPrepare.setRemark(msg + dealerPrepare.getRemark());
        dealerPrepare.setRecordId(-1L);
        cloudDealerRewardRecordPrepareMapper.update(dealerPrepare, dealerQW);
    }

    /**
     * 终端地址变更
     * @param terminalShopNodeReq
     * @param terminalShopNodeModel
     * @param rtnVal
     * @return
     */
    private boolean changeAddress(TerminalShopNodeReq terminalShopNodeReq, TerminalShopNodeModel terminalShopNodeModel, boolean rtnVal) {
        String isBack = terminalShopNodeReq.getIsBack(); // 0 通过   1驳回

        TerminalLocationModifyModel terminalLocationModifyModel = locationModifyService.getById(terminalShopNodeModel.getNodeSourceId());

        //组装更新对象
        TerminalShopNodeModel updateNodeModel = TerminalShopNodeModel.builder()
                .id(terminalShopNodeReq.getId())
                .isBack("2".equals(isBack) ? "0" : isBack)
                .updateMsg(terminalShopNodeReq.getUpdateMsg())
                .updateDate(new Date())
                .nodeStatus("1")
                .approvalUser(userContext.getTerminalModel().getId())
                .updateName(userContext.getTerminalModel().getName())
                .updatePhone(userContext.getTerminalModel().getPhone())
                .build();
        terminalShopNodeDao.updateById(updateNodeModel);
        // 通过
        if("0".equals(isBack)){

            // 2023-08-31 注释 sunshine 地址变更 客户经理通过后修改地址
//            // 设置状态为审批中，
//            terminalLocationModifyModel.setStatus(3);
//            locationModifyService.updateById(terminalLocationModifyModel);
//            // 给oa发送数据
//            // 获取终端信息
//            TerminalShopResp terminalShopResp = terminalShopDao.selectTerminalShopById(terminalLocationModifyModel.getTerminalShopId());
//            // 获取合同信息
//            TerminalShopContractCommonModel shopContract = terminalShopContractCommonService.getShopContractByMemberShopId(terminalShopResp.getMemberShopId());
//            // 经销商信息
//            CloudDealerInfoModel cloudDealerInfoModel = dealerInfoCommonDao.getDealerInfoByDealerCode(shopContract.getDealerCode());
//            //组装数据
//            ChangeAddressZt changeAddressZt = new ChangeAddressZt();
//            changeAddressZt.setTerminalName(terminalShopResp.getShopName());
//            changeAddressZt.setDeputyCode(terminalShopResp.getDeputyCode());
//            changeAddressZt.setOldAddress(terminalLocationModifyModel.getOldAddress());
//            changeAddressZt.setNewHeadImg(new ChangeAddressZt.Attachment(terminalLocationModifyModel.getNewHeadImg(), "file"));
//            changeAddressZt.setNewAddress(terminalLocationModifyModel.getNewAddress());
//            changeAddressZt.setLiveVideo(new ChangeAddressZt.Attachment(terminalLocationModifyModel.getLiveVideo(), "file"));
//            changeAddressZt.setRemark(terminalLocationModifyModel.getRemark());
//            changeAddressZt.setTerminalLevel(terminalShopResp.getLevelName());
//            changeAddressZt.setContractType(ContractTypeEnum.getTypeNameByCode(shopContract.getContractType()));
//            changeAddressZt.setContractCode(shopContract.getContractCode());
//            changeAddressZt.setChannelName(cloudDealerInfoModel.getDealerName());
//            String oaId = changeAddressZtService.sendConfirmAddress(userContext.getTerminalModel().getPhone(), "终端地址修改", changeAddressZt);
//
//            TerminalShopNodeModel nodeModel = TerminalShopNodeModel.builder()
//                    .terminalShopId(terminalShopNodeReq.getTerminalShopId())
//                    .companyId(terminalShopNodeReq.getCompanyId())
//                    .shopId(terminalShopNodeModel.getShopId())
//                    .createTime(new Date())
//                    .nodeName("OA审批申请")
//                    .nodeLevel("1")
//                    .nodeType(terminalShopNodeModel.getNodeType())
//                    .updateName("OA审批")
//                    .updateUser(0)
//                    .dataLogId(0)
//                    .nodeSourceId(terminalShopNodeModel.getNodeSourceId())
//                    .oaId(oaId)
//                    .build();
//            terminalShopNodeDao.insert(nodeModel);


            terminalShopNodeModel.setUpdateName(userContext.getTerminalModel().getName());
            terminalShopNodeModel.setUpdateDate(new Date());
            terminalShopNodeModel.setNodeStatus("1");
            terminalShopNodeModel.setUpdateMsg("审核通过");
            terminalShopNodeDao.updateById(terminalShopNodeModel);
            // 更新相关记录为通过状态
            TerminalLocationModifyModel modifyModel = new TerminalLocationModifyModel();
            modifyModel.setId(terminalShopNodeModel.getNodeSourceId());
            modifyModel.setStatus(1);
            terminalLocationModifyDao.updateById(modifyModel);
            // 获取变更记录
            TerminalLocationModifyModel locationModifyModel = locationModifyService.getById(terminalShopNodeModel.getNodeSourceId());
            if(locationModifyModel==null){
                throw new BusinessException("未找到变更记录={}", terminalShopNodeModel.getNodeSourceId());
            }
            // 更新t_terminal_shop的地址信息
            TerminalShopModel terminalShopModel = new TerminalShopModel();
            terminalShopModel.setId(terminalShopNodeModel.getTerminalShopId());
            terminalShopModel.setHeadImg(locationModifyModel.getNewHeadImg());
            terminalShopModel.setProvince(locationModifyModel.getNewProvince());
            terminalShopModel.setCity(locationModifyModel.getNewCity());
            terminalShopModel.setDistrict(locationModifyModel.getNewDistrict());
            terminalShopModel.setAddress(locationModifyModel.getNewAddress());
            terminalShopModel.setLongitude(locationModifyModel.getNewLongitude());
            terminalShopModel.setLatitude(locationModifyModel.getNewLatitude());
            terminalShopModel.setAddressStatus(1);
            terminalShopService.updateById(terminalShopModel);
            // 更新t_member_shop的地址信息
            ShopModel shopModel = new ShopModel();
            shopModel.setId(locationModifyModel.getMemberShopId());
            shopModel.setProvince(locationModifyModel.getNewProvince());
            shopModel.setCity(locationModifyModel.getNewCity());
            shopModel.setDistrict(locationModifyModel.getNewDistrict());
            shopModel.setAddress(locationModifyModel.getNewAddress());
            shopModel.setLongitude(locationModifyModel.getNewLongitude());
            shopModel.setLatitude(locationModifyModel.getNewLatitude());
            shopDao.update(shopModel);
            rtnVal = true;
        }
        if("1".equals(isBack)){
            // 设置状态为驳回
            terminalLocationModifyModel.setStatus(2);
            locationModifyService.updateById(terminalLocationModifyModel);
        }

        return rtnVal;
    }

    private boolean approvalReceiptInfo(TerminalShopNodeReq terminalShopNodeReq, TerminalShopNodeModel terminalShopNodeModel, boolean rtnVal) {
        String isBack = terminalShopNodeReq.getIsBack(); // 0 通过   1驳回  2 提交OA

        Long deltailId = terminalShopNodeModel.getOrderId();
        TerminalScanDetailModel terminalScanDetailModel = terminalScanDetailPlusDao.selectById(deltailId);
        Integer monthTotalQty = terminalScanDetailCommonDao.getThisMonthDeliveryTotalQty(terminalShopNodeModel.getShopId(), terminalScanDetailModel.getGoodsCode());

        //组装更新对象
        TerminalShopNodeModel updateNodeModel = TerminalShopNodeModel.builder()
                .id(terminalShopNodeReq.getId())
                .isBack("2".equals(isBack) ? "0" : isBack)
                .updateMsg(terminalShopNodeReq.getUpdateMsg())
                .updateDate(new Date())
                .nodeStatus("1")
                .approvalUser(userContext.getTerminalModel().getId())
                .updateName(userContext.getTerminalModel().getName())
                .updatePhone(userContext.getTerminalModel().getPhone())
                .approvalQty(monthTotalQty)
                .build();

        if ("2".equals(terminalShopNodeReq.getIsBack())) {//OA审批
            TerminalShopNodeModel nodeModel = TerminalShopNodeModel.builder()
                    .terminalShopId(terminalShopNodeReq.getTerminalShopId())
                    .scheduleShopId(terminalShopNodeReq.getScheduleShopId())
                    .orderNo(terminalShopNodeModel.getOrderNo())
                    .companyId(terminalShopNodeReq.getCompanyId())
                    .shopId(terminalShopNodeModel.getShopId())
                    .orderId(Long.valueOf(deltailId))
                    .createTime(new Date())
                    .nodeName("OA审批申请")
                    .nodeLevel("1")
                    .nodeType(terminalShopNodeModel.getNodeType())
                    .updateName("OA审批")
                    .updateUser(0)
                    .dataLogId(0)
                    .currentQty(monthTotalQty)
                    .thresholdVal(terminalShopNodeModel.getThresholdVal())
                    .build();

            //组装数据
            JSONObject formValues = getReceiptOaParam(terminalShopNodeModel, terminalScanDetailModel, monthTotalQty);

            Map<String, Object> paramBody = new HashMap<String, Object>();

            paramBody.put("docSubject", userContext.getTerminalModel().getName() + "申请一条收货单(" + formValues.getString("orderNo") + ")");
            JSONObject personJson = new JSONObject();
            String phone = userContext.getTerminalModel().getPhone();
            /*if("18312120000".equals(phone)){
                phone = "19859010627";
            }*/
            personJson.put("LoginName", phone);
            paramBody.put("docCreator", personJson.toJSONString());
            paramBody.put("fdTemplateId", "187787752db223af965f6ac416d9eb2f");
            paramBody.put("formValues", formValues.toJSONString());

            HttpHeaders headers = oaUtils.getOaToken();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<Map<String, Object>>(paramBody, headers);
            RequestLog requestLog = new RequestLog();
            long ernterSystemTime = System.currentTimeMillis();
            requestLog.setReqName("申请收货OA审批:" + formValues.getString("orderNo"));
            requestLog.setReqType(34);
            requestLog.setReqUrlPath(sendOaOrderUrl);
            requestLog.setCreateDate(new Date());
            requestLog.setReqJson(JSON.toJSONString(paramBody));

            //请求参数打印
            log.info("OA收货审核的实体参数:{}", JSON.toJSONString(paramBody));
            try {
                RestTemplate template = new RestTemplate();
                log.info("OA请求的地址:{}", sendOaOrderUrl);
                ResponseEntity<Map> exchange = template.exchange(sendOaOrderUrl, HttpMethod.POST, entity, Map.class);
                requestLog.setResJson(exchange.getBody().toString());
                requestLog.setResCode(exchange.getBody().get("code").toString());
                requestLog.setResMsg(exchange.getBody().get("msg").toString());
                log.info("OA收货审核返回的数据---{}---{}", formValues.getString("orderNo"), JSON.toJSONString(exchange));
                if ("200".equals(exchange.getBody().get("code").toString())) {
                    String data = exchange.getBody().get("data").toString();
                    String msg = exchange.getBody().get("msg").toString();
                    if (StringUtils.isBlank(data)) {//失败
                        throw new BusinessException("400", "OA收货审核失败:" + msg);
                    }
                    nodeModel.setOaId(data);
                } else {
                    String msg = exchange.getBody().get("msg").toString();
                    throw new BusinessException("400", msg);
                }
            } catch (Exception e) {
                e.printStackTrace();
                requestLog.setResCode("-1");
                requestLog.setResMsg("申请OA收货审核失败:" + e.getMessage());
                log.error("申请OA收货审核失败原因:{}", e.getMessage());
                throw new BusinessException("400", "申请OA收货审核失败原因:" + e.getMessage());
            } finally {
                log.info("RequestLog===>", JSONObject.toJSONString(requestLog));
                long outSystemTime = System.currentTimeMillis();
                String diffTime = DateUtils.longToHMS(outSystemTime - ernterSystemTime);
                requestLog.setReqTime(diffTime);
                requestLogService.insertLog(requestLog);
            }
            String currentYm = DateUtils.convert2String(new Date(), "yyyy-MM");
            nodeModel.setMonthYear(currentYm);
            terminalShopNodeDao.insert(nodeModel);
        } else { // 应该客户经理审批
            Integer orderStatus = "0".equals(isBack) ? 1 : 8;
            if (orderStatus == 1) {// 通过时
                agreeHandleScore(terminalShopNodeModel);
            } else {
                rejectHandleScore(terminalShopNodeModel);
            }
        }
        terminalShopNodeDao.updateById(updateNodeModel);
        if (!"0".equals(isBack)) {
            rtnVal = true;
        }
        return rtnVal;
    }


    @Override
    public Map<String, String> getShopScheduleInfo(Integer terminalShopId) {
        return terminalShopDao.getShopScheduleInfoById(terminalShopId);
    }

    /**
     * @author: Sunjianbu
     * @Date: 2024/03/05 15:53
     * @Description: 同步申请超级终端OA审批的情况
     */
    @Override
    public void syncSuperTerminalOAApproval() {
        // 查询OA审核中的超级终端申请
        LambdaQueryWrapper<TerminalShopNodeModel> qw = Wrappers.lambdaQuery();
        qw.eq(TerminalShopNodeModel::getNodeStatus, 0);
        qw.eq(TerminalShopNodeModel::getNodeType, TerminalShopNodeEnum.APPLY_SUPER_TERMINAL.getType());
        qw.eq(TerminalShopNodeModel::getNodeLevel, 1);
        qw.isNotNull(TerminalShopNodeModel::getOaId);
        List<TerminalShopNodeModel> shopNodeModelList = terminalShopNodeDao.selectList(qw);

        log.info("共有" + shopNodeModelList.size() + "条数据需要处理");


//        HttpHeaders headers = new HttpHeaders();
//        String accountID = oaAccountID; // 认证用户名
//        String accountPassword = oaAccountPassword; // 认证密码
//        String token = addAuth(headers, accountID + ":" + accountPassword);

        HttpHeaders headers = oaUtils.getOaToken();


        for (int i = 0; i < shopNodeModelList.size(); i++) {
            TerminalShopNodeModel terminalShopNodeModel = shopNodeModelList.get(i);
            // 获取节点表内的protocol_ids，并转化为数组
            String protocolIds = terminalShopNodeModel.getProtocolIds();
            String[] protocolIdArr = protocolIds.split(",");

            log.info("共有" + shopNodeModelList.size() + "条数据需要，当前处理到" + i + "条，id=" + terminalShopNodeModel.getId());
            String oaId = terminalShopNodeModel.getOaId();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", oaId);
            log.info("查询OA--->{}的流程信息地址:::{}---->参数:{}", terminalShopNodeModel.getOaId(), sendOaOrderApprovalUrl, JSON.toJSONString(jsonObject));
            HttpEntity<Map<String, Object>> entity = new HttpEntity<Map<String, Object>>(jsonObject, headers);
            RestTemplate template = new RestTemplate();
            ResponseEntity<Map> exchange = template.exchange(sendOaOrderApprovalUrl, HttpMethod.POST, entity, Map.class);
            Boolean hasAbandon = false;
            List<Map<String, Object>> list = (List<Map<String, Object>>) exchange.getBody().get("data");
            for (Map<String, Object> flowJson : list) {
                if(Objects.nonNull(flowJson.get("actionKey"))) {
                    String actionkey = flowJson.get("actionKey").toString();
                    if (actionkey.endsWith(CommonConstant.FLOW_TYPE_MAP.get("废弃"))) {
                        hasAbandon = true;
                        break;
                    }
                }
            }
            log.info("查询OA--->{}的流程信息有{}条,返回信息:{}", terminalShopNodeModel.getOaId(), list.size(), exchange.getBody().toString());

            Map<String, Object> flowJson = list.get(list.size() - 1);
            log.info("最后一条审批节点:{}", JSONObject.toJSONString(flowJson));
            if(Objects.nonNull(flowJson.get("actionKey"))) {
                String actionkey = flowJson.get("actionKey").toString();
                if (CommonConstant.FLOW_TYPE_MAP.get("流程结束").equals(actionkey) && !hasAbandon) {
                    handleApprovalSuccess(terminalShopNodeModel, flowJson, protocolIdArr, list);
                } else if (hasAbandon || CommonConstant.FLOW_TYPE_MAP.get("审批人驳回").equals(actionkey)) {
                    handleApprovalFail(terminalShopNodeModel, flowJson, protocolIdArr);
                }
            }else {
                if (list.size() > 1) {
                    RequestLog requestLog = new RequestLog();
                    long ernterSystemTime = System.currentTimeMillis();
                    requestLog.setReqName("定时拉取OA审批流::单号=" + terminalShopNodeModel.getOrderNo() + ";oaId=" + oaId);
                    requestLog.setReqType(6);
                    requestLog.setReqUrlPath(sendOaOrderApprovalUrl);
                    requestLog.setCreateDate(new Date());
                    requestLog.setResCode("0");
                    requestLog.setReqJson(JSONObject.toJSONString(entity));
                    requestLog.setResJson(exchange.getBody().toString());
                    long outSystemTime = System.currentTimeMillis();
                    String diffTime = DateUtils.longToHMS(outSystemTime - ernterSystemTime);
                    requestLog.setReqTime(diffTime);
                    requestLogService.insertLog(requestLog);
                }
            }
        }
    }

    @Override
    public void syncHighEndWineTerminalOAApproval() {
        // 查询OA审核中的高端酒终端申请
        LambdaQueryWrapper<TerminalShopNodeModel> qw = Wrappers.lambdaQuery();
        qw.eq(TerminalShopNodeModel::getNodeStatus, 0);
//        qw.eq(TerminalShopNodeModel::getNodeType, TerminalShopNodeEnum.UPDATE_PROTOCOL.getType());
        qw.in(TerminalShopNodeModel::getNodeType, TerminalShopNodeEnum.UPDATE_PROTOCOL.getType(), TerminalShopNodeEnum.SUPPLY_PROTOCOL.getType());
        qw.eq(TerminalShopNodeModel::getNodeLevel, 1);
        qw.isNotNull(TerminalShopNodeModel::getOaId);
        List<TerminalShopNodeModel> shopNodeModelList = terminalShopNodeDao.selectList(qw);

        log.info("共有" + shopNodeModelList.size() + "条数据需要处理");


//        HttpHeaders headers = new HttpHeaders();
//        String accountID = oaAccountID; // 认证用户名
//        String accountPassword = oaAccountPassword; // 认证密码
//        String token = addAuth(headers, accountID + ":" + accountPassword);

        HttpHeaders headers = oaUtils.getOaToken();


        for (int i = 0; i < shopNodeModelList.size(); i++) {
            TerminalShopNodeModel terminalShopNodeModel = shopNodeModelList.get(i);
            // 获取节点表内的protocol_ids，并转化为数组
            String protocolIds = terminalShopNodeModel.getProtocolIds();
            String[] protocolIdArr = protocolIds.split(",");

            log.info("共有" + shopNodeModelList.size() + "条数据需要，当前处理到" + i + "条，id=" + terminalShopNodeModel.getId());
            String oaId = terminalShopNodeModel.getOaId();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", oaId);
            log.info("查询OA--->{}的流程信息地址:::{}---->参数:{}", terminalShopNodeModel.getOaId(), sendOaOrderApprovalUrl, JSON.toJSONString(jsonObject));
            HttpEntity<Map<String, Object>> entity = new HttpEntity<Map<String, Object>>(jsonObject, headers);
            RestTemplate template = new RestTemplate();
            ResponseEntity<Map> exchange = template.exchange(sendOaOrderApprovalUrl, HttpMethod.POST, entity, Map.class);
            Boolean hasAbandon = false;
            List<Map<String, Object>> list = (List<Map<String, Object>>) exchange.getBody().get("data");
            for (Map<String, Object> flowJson : list) {
                if(Objects.nonNull(flowJson.get("actionKey"))) {
                    String actionkey = flowJson.get("actionKey").toString();
                    if (actionkey.endsWith(CommonConstant.FLOW_TYPE_MAP.get("废弃"))) {
                        hasAbandon = true;
                        break;
                    }
                }
            }
            log.info("查询OA--->{}的流程信息有{}条,返回信息:{}", terminalShopNodeModel.getOaId(), list.size(), exchange.getBody().toString());

            Map<String, Object> flowJson = list.get(list.size() - 1);
            log.info("最后一条审批节点:{}", JSONObject.toJSONString(flowJson));
            if(Objects.nonNull(flowJson.get("actionKey"))) {
                String actionkey = flowJson.get("actionKey").toString();
                if (CommonConstant.FLOW_TYPE_MAP.get("流程结束").equals(actionkey) && !hasAbandon) {
                    handleHighEndWineApprovalSuccess(terminalShopNodeModel, flowJson, protocolIdArr, list, terminalShopNodeModel.getNodeType(), oaId);

                } else if (hasAbandon || CommonConstant.FLOW_TYPE_MAP.get("审批人驳回").equals(actionkey)) {
                    handleHighEndWineApprovalFail(terminalShopNodeModel, flowJson, protocolIdArr, terminalShopNodeModel.getNodeType(), oaId);
                }
            }else {
                if (list.size() > 1) {
                    RequestLog requestLog = new RequestLog();
                    long ernterSystemTime = System.currentTimeMillis();
                    requestLog.setReqName("定时拉取OA审批流::单号=" + terminalShopNodeModel.getOrderNo() + ";oaId=" + oaId);
                    requestLog.setReqType(6);
                    requestLog.setReqUrlPath(sendOaOrderApprovalUrl);
                    requestLog.setCreateDate(new Date());
                    requestLog.setResCode("0");
                    requestLog.setReqJson(JSONObject.toJSONString(entity));
                    requestLog.setResJson(exchange.getBody().toString());
                    long outSystemTime = System.currentTimeMillis();
                    String diffTime = DateUtils.longToHMS(outSystemTime - ernterSystemTime);
                    requestLog.setReqTime(diffTime);
                    requestLogService.insertLog(requestLog);
                }
            }
        }
    }

    @Override
    public void syncSuperTerminal() {
        // 查询超级终端的终端等级
        List<TerminalShopLevelModel> levelList = terminalShopLevelDao.selectList(new LambdaQueryWrapper<TerminalShopLevelModel>()
                .eq(TerminalShopLevelModel::getIsDelete, DeleteFlagEnum.NOT_DELETE.getKey())
                .eq(TerminalShopLevelModel::getType, TerminalLevelTypeEnum.SUPER.getKey()));
        // 查询所有审批成功的超级终端
        LambdaQueryWrapper<TerminalProductProtocolRelationModel> qw = Wrappers.lambdaQuery();
        qw.eq(TerminalProductProtocolRelationModel::getPolicyType,PolicyTypeEnum.CONVENTIONAL_POLICY.getKey());
        qw.eq(TerminalProductProtocolRelationModel::getCheckStatus, ProtocolCheckStatusEnum.SUCCESS.getKey());
        if(Objects.nonNull(levelList) && levelList.size() > 0){
            List<Long> levelIds = levelList.stream().map(TerminalShopLevelModel::getId).collect(Collectors.toList());
            qw.in(TerminalProductProtocolRelationModel::getLevelCode, levelIds);
        }
        qw.eq(TerminalProductProtocolRelationModel::getIsDelete, DeleteFlagEnum.NOT_DELETE.getKey());
        List<TerminalProductProtocolRelationModel> relationList = terminalProductProtocolRelationDao.selectList(qw);
        // 判断是否有超级终端，若有超级终端则同步给中台
        if(Objects.nonNull(relationList) && relationList.size() > 0){
            // 遍历协议列表取出去重后的terminalShopId
            List<Long> terminalShopIds = relationList.stream().map(TerminalProductProtocolRelationModel::getTerminalShopId).distinct().collect(Collectors.toList());
            for (Long terminalShopId : terminalShopIds) {
                Integer terminalId = terminalShopId.intValue();
                // 同步终端和协议信息到中台
                TerminalShopInfoScheduleModel scheduleModel = terminalShopInfoScheduleDao.selectOne(new QueryWrapper<TerminalShopInfoScheduleModel>().eq("terminal_shop_id", terminalId));
                TerminalShopContractResp terminalShopContractResp = terminalShopContractDao.selectTerminalShopContractResp(terminalId);
                int insertOrUpdate = 1;
                terminalShopService.sendZtStateDataNewProtocol(scheduleModel, terminalShopContractResp, insertOrUpdate);
            }
        }

    }
    // 处理审批成功的情况
    @Override
    @Transactional
    public void handleApprovalSuccess(TerminalShopNodeModel terminalShopNodeModel, Map<String, Object> flowJson, String[] protocolIdArr, List<Map<String, Object>> list) {
        if (list.size() > 2) {
            flowJson = list.get(list.size() - 2);
        }
        Object handlerName = flowJson.get("handlerName");
        String auditNote = flowJson.get("auditNote").toString();
        String actionDate = flowJson.get("actionDate").toString() + ":00";
        Date date = DateUtils.convert2Date(actionDate, "yyyy-MM-dd HH:mm:ss");
        terminalShopNodeModel.setUpdateName(handlerName != null ? handlerName.toString() : "");
        terminalShopNodeModel.setUpdateDate(date);
        terminalShopNodeModel.setNodeStatus("1");
        terminalShopNodeModel.setUpdateMsg(auditNote);
        log.info("OA审批申请超级终端成功，影响的node表数据：", JSON.toJSONString(terminalShopNodeModel));
        terminalShopNodeDao.updateById(terminalShopNodeModel);

        Integer terminalId = terminalShopNodeModel.getTerminalShopId();
        // 更新旧协议状态
        Integer companyId = terminalShopNodeModel.getCompanyId();
        // 查询当前季度开始结束时间
        String currentQuarterStartDate = DateUtils.getCurrentQuarterStartDate().toString();
        String currentQuarterEndDate = DateUtils.getCurrentQuarterEndDate().toString();

//        terminalProductProtocolV2Service.updatePackageQuantityFailureTime(terminalId, companyId);
//        terminalProductProtocolV2Service.updateDisplayFailureTime(terminalId, companyId);
//
//        // 遍历当前节点的协议id数组protocolIdArr，更新协议状态
//        for (String protocolId : protocolIdArr) {
//            TerminalProductProtocolRelationModel relationModel = new TerminalProductProtocolRelationModel();
//            relationModel.setId(Long.parseLong(protocolId));
//            relationModel.setCheckStatus(ProtocolCheckStatusEnum.SUCCESS.getKey());
//            relationModel.setUpdateTime(LocalDateTime.now());
//            log.info("OA审批申请超级终端成功，影响的protocol表数据：", JSON.toJSONString(relationModel));
//            terminalProductProtocolRelationDao.updateById(relationModel);
//        }
//        terminalProductProtocolV2Service.sendZTUpdatePackageQuantityProtocolIsDelete(terminalId, companyId);
//        terminalProductProtocolV2Service.sendZTUpdateDisplayProtocolIsDelete(terminalId, companyId);


        // 查询当前生效的陈列协议
        TerminalProductProtocolRelationModel currQuarterEffectiveDisplayProtocol = terminalProductProtocolV2Service.getProtocolTerminalShopIdAndTypeAndDate(ProtocolTypeEnum.DISPLAY.getKey(),terminalId.longValue(), currentQuarterStartDate, currentQuarterEndDate);
        TerminalProductProtocolRelationModel newProtocol = null;
        // 获取陈列协议类型是否发生变化
        Boolean isChangeDisplayProtocolType = null;
        if(currQuarterEffectiveDisplayProtocol != null){
            String oldProtocolCode = currQuarterEffectiveDisplayProtocol.getProtocolCode();
            for (String protocolId : protocolIdArr) {
                TerminalProductProtocolRelationModel relationModel = terminalProductProtocolV2Service.getById(Long.parseLong(protocolId));
                if(relationModel.getProtocolType() == ProtocolTypeEnum.DISPLAY.getKey()){
                    newProtocol = relationModel;
                    break;
                }
            }
            if(Objects.nonNull(newProtocol)) {
                //获取等级是否发生变化
                Boolean isChangeTerminalLevelCode = terminalProductProtocolV2Service.compareProtocolLevelCode(oldProtocolCode, newProtocol.getProtocolCode());
                // 获取陈列协议类型是否发生变化
                isChangeDisplayProtocolType = terminalProductProtocolV2Service.compareDisplayProtocolType(oldProtocolCode, newProtocol.getProtocolCode());

                //1、等级变化并且协议类型不变,直接删除，因陈列类型不变，生效时间不会变化
                //if (isChangeTerminalLevelCode && !isChangeDisplayProtocolType) {
                //    terminalProductProtocolV2Service.updateProtocolIsDelete(currQuarterEffectiveDisplayProtocol);
                //}
                // 2、等级无变化仅陈列类别变化 || 等级变化+类别变化
                if (isChangeDisplayProtocolType) {
                    handleProtocol(currQuarterEffectiveDisplayProtocol,newProtocol);
                }
            }
        }

        // 查询当前生效的包量协议
        TerminalProductProtocolRelationModel currQuarterEffectivePackageProtocol = terminalProductProtocolV2Service.getProtocolTerminalShopIdAndTypeAndDate(ProtocolTypeEnum.PACKAGE_QUANTITY.getKey(),terminalId.longValue(), currentQuarterStartDate, currentQuarterEndDate);
        if(currQuarterEffectivePackageProtocol != null){
            handleProtocol(currQuarterEffectivePackageProtocol,newProtocol);
        }

        // 遍历当前节点的协议id数组protocolIdArr，更新协议状态
        for (String protocolId : protocolIdArr) {
            TerminalProductProtocolRelationModel relationModel = new TerminalProductProtocolRelationModel();
            relationModel.setId(Long.parseLong(protocolId));
            relationModel.setCheckStatus(ProtocolCheckStatusEnum.SUCCESS.getKey());
            relationModel.setUpdateTime(LocalDateTime.now());
            log.info("OA审批申请超级终端成功，影响的protocol表数据：", JSON.toJSONString(relationModel));
            terminalProductProtocolRelationDao.updateById(relationModel);
        }

        // 等更新之后再调用清洗逻辑
        if(currQuarterEffectiveDisplayProtocol != null && Objects.nonNull(newProtocol) && isChangeDisplayProtocolType){
            terminalSkuCheckService.calculateDisplaySurface(newProtocol.getTerminalShopId().intValue(), newProtocol.getId(), "陈列协议陈列类型修改", newProtocol.getEffectiveTime());
        }

        // 同步终端和协议信息到中台
        TerminalShopInfoScheduleModel scheduleModel = terminalShopInfoScheduleDao.selectOne(new QueryWrapper<TerminalShopInfoScheduleModel>().eq("terminal_shop_id", terminalId));
        TerminalShopContractResp terminalShopContractResp = terminalShopContractDao.selectTerminalShopContractResp(terminalId);
        int insertOrUpdate = 1;
        terminalShopService.sendZtStateDataNewProtocol(scheduleModel, terminalShopContractResp, insertOrUpdate);
    }

    @Override
    @Transactional
    public void handleHighEndWineApprovalSuccess(TerminalShopNodeModel terminalShopNodeModel, Map<String, Object> flowJson, String[] protocolIdArr, List<Map<String, Object>> list, Integer nodeType, String oaId) {
        if (list.size() > 2) {
            flowJson = list.get(list.size() - 2);
        }
        Object handlerName = flowJson.get("handlerName");
        String auditNote = flowJson.get("auditNote").toString();
        String actionDate = flowJson.get("actionDate").toString() + ":00";
        Date date = DateUtils.convert2Date(actionDate, "yyyy-MM-dd HH:mm:ss");
        terminalShopNodeModel.setUpdateName(handlerName != null ? handlerName.toString() : "");
        terminalShopNodeModel.setUpdateDate(date);
        terminalShopNodeModel.setNodeStatus("1");
        terminalShopNodeModel.setUpdateMsg(auditNote);
        log.info("OA审批申请高端酒终端成功，影响的node表数据：", JSON.toJSONString(terminalShopNodeModel));
        terminalShopNodeDao.updateById(terminalShopNodeModel);

        // 高端酒终端审批成功，更新协议状态
        Integer terminalId = terminalShopNodeModel.getTerminalShopId();
        Integer companyId = terminalShopNodeModel.getCompanyId();
        ProtocoTimeParamResp currentPeriodTime = DateUtils.getCurrentPeriodTime();
        String currentPeriodTimeStartDate = currentPeriodTime.getStartDate();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDateTime currentPeriodEndTime = LocalDate.parse(currentPeriodTime.getEndDate(), formatter).atTime(23, 59, 59);
        //查询生效的协议
//                    TerminalProductProtocolRelationModel curPeriodEffectiveProtocol = terminalProductProtocolV2CommonService.getHighEndWineProtocolTerminalShopIdAndTypeAndDate(ProtocolTypeEnum.HIGH_END_WINE_PACKAGE_QUANTITY.getKey(),terminalId.longValue(), currentPeriodTimeStartDate, currentPeriodTimeEndDate);
        TerminalProductProtocolRelationModel newProtocol = null;

        // 遍历当前节点的协议id数组protocolIdArr，更新协议状态
        for (String protocolId : protocolIdArr) {
            // 在这里区分补充协议还是协议变更
            if(nodeType.equals(TerminalShopNodeEnum.SUPPLY_PROTOCOL.getType())){
                //查询生效的协议
                TerminalProductProtocolRelationModel effectiveProtocol = terminalProductProtocolV2Service.getOldProtocol(terminalId, companyId, ProtocolTypeEnum.HIGH_END_WINE_PACKAGE_QUANTITY.getKey(), ProtocolCheckStatusEnum.OA_AUDIT.getKey());
                if(effectiveProtocol == null){
                    throw new BusinessException("没有OA审核中的协议！");
                }
//                handleProtocolHighEndWine(effectiveProtocol);

//                TerminalShopNodeModel terminalShopNodeModel1 = terminalShopNodeDao.selectOne(new QueryWrapper<TerminalShopNodeModel>().eq("terminal_shop_id", terminalId)
//                        .eq("oa_id", oaId));
//
//                terminalShopNodeModel1.setNodeLevel("0");
//                terminalShopNodeModel1.setNodeStatus("1");
//                terminalShopNodeModel1.setIsBack("0");
//                terminalShopNodeModel1.setUpdateDate(new DateTime());

                TerminalProductProtocolRelationModel relationModel = new TerminalProductProtocolRelationModel();
                relationModel.setId(Long.parseLong(protocolId));
                relationModel.setCheckStatus(ProtocolCheckStatusEnum.SUCCESS.getKey());
                relationModel.setUpdateTime(LocalDateTime.now());
                log.info("OA审批申请高端酒终端成功，影响的protocol表数据：", JSON.toJSONString(relationModel));
                terminalProductProtocolRelationDao.updateById(relationModel);
            } else if (nodeType.equals(TerminalShopNodeEnum.UPDATE_PROTOCOL.getType())) {
                //查询生效的协议
                TerminalProductProtocolRelationModel effectiveProtocol = terminalProductProtocolV2Service.getOldProtocol(terminalId, companyId, ProtocolTypeEnum.HIGH_END_WINE_PACKAGE_QUANTITY.getKey(), ProtocolCheckStatusEnum.SUCCESS.getKey());
                if(effectiveProtocol == null){
                    throw new BusinessException("没有OA审核成功的协议！");
                }
                TerminalProductProtocolRelationChangeDetailModel terminalProductProtocolRelationChangeDetailModel = terminalProductProtocolRelationChangeDetailCommonDao.selectOne(new QueryWrapper<TerminalProductProtocolRelationChangeDetailModel>().eq("id", protocolId)
                        .eq("is_delete", DeleteFlagEnum.NOT_DELETE.getKey()));
                //查询待生效的协议
                TerminalProductProtocolRelationModel newProtocolRelationModel = terminalProductProtocolRelationDao.selectOne(new QueryWrapper<TerminalProductProtocolRelationModel>().eq("id", terminalProductProtocolRelationChangeDetailModel.getNewId()));
                handleProtocolHighEndWine(effectiveProtocol, newProtocolRelationModel.getEffectiveTime());


                TerminalProductProtocolRelationChangeModel terminalProductProtocolRelationChangeModel = terminalProductProtocolRelationChangeCommonDao.selectOne(new QueryWrapper<TerminalProductProtocolRelationChangeModel>().eq("id", terminalProductProtocolRelationChangeDetailModel.getChangeId())
                        .eq("is_delete", DeleteFlagEnum.NOT_DELETE.getKey()));
                terminalProductProtocolRelationChangeModel.setCheckStatus(ProtocolCheckStatusEnum.SUCCESS.getKey());
                terminalProductProtocolRelationChangeModel.setUpdateTime(LocalDateTime.now());
                terminalProductProtocolRelationChangeCommonDao.updateById(terminalProductProtocolRelationChangeModel);
//                TerminalShopNodeModel terminalShopNodeModel1 = terminalShopNodeDao.selectOne(new QueryWrapper<TerminalShopNodeModel>().eq("terminal_shop_id", terminalId)
//                        .eq("oa_id", oaId));
//
//                terminalShopNodeModel1.setNodeLevel("0");
//                terminalShopNodeModel1.setNodeStatus("1");
//                terminalShopNodeModel1.setIsBack("0");
//                terminalShopNodeModel1.setUpdateDate(new DateTime());

                TerminalProductProtocolRelationModel relationModel = new TerminalProductProtocolRelationModel();
                relationModel.setId(terminalProductProtocolRelationChangeDetailModel.getNewId());
                relationModel.setCheckStatus(ProtocolCheckStatusEnum.SUCCESS.getKey());
                relationModel.setUpdateTime(LocalDateTime.now());
                log.info("OA审批申请高端酒终端成功，影响的protocol表数据：", JSON.toJSONString(relationModel));
                terminalProductProtocolRelationDao.updateById(relationModel);
            }
        }
        // 同步终端和协议信息到中台
        TerminalShopInfoScheduleModel scheduleModel = terminalShopInfoScheduleDao.selectOne(new QueryWrapper<TerminalShopInfoScheduleModel>().eq("terminal_shop_id", terminalId));
        TerminalShopContractResp terminalShopContractResp = terminalShopContractDao.selectTerminalShopContractResp(terminalId);
        int insertOrUpdate = 1;
        terminalShopService.sendZtStateDataNewProtocol(scheduleModel, terminalShopContractResp, insertOrUpdate);
    }

    public void handleProtocol(TerminalProductProtocolRelationModel currQuarterEffectivePackageProtocol,TerminalProductProtocolRelationModel newProtocol){
        //是否需要更新失效时间
        Boolean isUpdateProtocolFailtureTimeFlag = terminalProductProtocolV2Service.isUpdateProtocolFailtureTime(currQuarterEffectivePackageProtocol,newProtocol.getEffectiveTime());
        if (isUpdateProtocolFailtureTimeFlag) {
            // 获取新协议的生效时间上个季度末最后一天
            Date protocolEffectiveDate = newProtocol.getEffectiveTime();
            Instant instant = protocolEffectiveDate.toInstant();
            LocalDateTime protocolEffectiveTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
            // 计算新协议生效时间的上一个季度末最后一天日期
            LocalDateTime lastDayOfPreviousQuarter = DateUtils.getLastDayOfPreviousQuarter(protocolEffectiveTime);
//            LocalDateTime lastDayOfPreviousQuarter = DateUtils.getLastDayOfPreviousQuarter();
            terminalProductProtocolV2Service.updateProtocolFailtureTime(currQuarterEffectivePackageProtocol, lastDayOfPreviousQuarter);
        } else {
            //否则，删除当前生效协议
            terminalProductProtocolV2Service.updateProtocolIsDelete(currQuarterEffectivePackageProtocol);
        }
    }

    // 高端酒处理协议
    public void handleProtocolHighEndWine(TerminalProductProtocolRelationModel currQuarterEffectivePackageProtocol, Date newProtocolEffectiveTime){
        ProtocoTimeParamResp currentPeriodTime = DateUtils.getCurrentPeriodTime();
        String currentPeriodTimeStartDate = currentPeriodTime.getStartDate();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//        LocalDateTime currentPeriodEndTime = LocalDate.parse(currentPeriodTime.getEndDate(), formatter).atTime(23, 59, 59);
        Boolean includeCurrentPeriodRangeProtocolFlag = terminalProductProtocolV2CommonService.includeNextPeriodProtocol(currQuarterEffectivePackageProtocol, newProtocolEffectiveTime);
        if (includeCurrentPeriodRangeProtocolFlag) {
            terminalProductProtocolV2CommonService.updateProtocolIsDelete(currQuarterEffectivePackageProtocol);
        } else {
            LocalDateTime currentPeriodEndTime = terminalProductProtocolV2CommonService.covertProtocolFailtureTime(newProtocolEffectiveTime);
            terminalProductProtocolV2CommonService.updateProtocolFailtureTime(currQuarterEffectivePackageProtocol, currentPeriodEndTime);
        }

    }


    // 处理审批失败的情况
    @Override
    @Transactional
    public void handleApprovalFail(TerminalShopNodeModel terminalShopNodeModel, Map<String, Object> flowJson, String[] protocolIdArr) {
        String handlerName = flowJson.get("handlerName").toString();
        String auditNote = flowJson.get("auditNote").toString();
        String actionDate = flowJson.get("actionDate").toString() + ":00";
        Date date = DateUtils.convert2Date(actionDate, "yyyy-MM-dd HH:mm:ss");
        terminalShopNodeModel.setUpdateName(handlerName);
        terminalShopNodeModel.setUpdateDate(date);
        terminalShopNodeModel.setNodeStatus("1");
        terminalShopNodeModel.setUpdateMsg(auditNote);
        terminalShopNodeModel.setIsBack("1");
        terminalShopNodeDao.updateById(terminalShopNodeModel);
        // 遍历当前节点的协议id数组protocolIdArr，更新协议状态
        for (String protocolId : protocolIdArr) {
            TerminalProductProtocolRelationModel relationModel = new TerminalProductProtocolRelationModel();
            relationModel.setId(Long.parseLong(protocolId));
            relationModel.setCheckStatus(ProtocolCheckStatusEnum.OA_FAIL.getKey());
            relationModel.setUpdateTime(LocalDateTime.now());
            terminalProductProtocolRelationDao.updateById(relationModel);
        }
    }

    @Override
    @Transactional
    public void handleHighEndWineApprovalFail(TerminalShopNodeModel terminalShopNodeModel, Map<String, Object> flowJson, String[] protocolIdArr, Integer nodeType, String oaId) {
        String handlerName = flowJson.get("handlerName").toString();
        String auditNote = flowJson.get("auditNote").toString();
        String actionDate = flowJson.get("actionDate").toString() + ":00";
        Date date = DateUtils.convert2Date(actionDate, "yyyy-MM-dd HH:mm:ss");
        terminalShopNodeModel.setUpdateName(handlerName);
        terminalShopNodeModel.setUpdateDate(date);
        terminalShopNodeModel.setNodeStatus("1");
        terminalShopNodeModel.setUpdateMsg(auditNote);
        terminalShopNodeModel.setIsBack("1");
        terminalShopNodeDao.updateById(terminalShopNodeModel);
        if(nodeType.equals(TerminalShopNodeEnum.SUPPLY_PROTOCOL.getType())){
            // 遍历当前节点的协议id数组protocolIdArr，更新协议状态
            for (String protocolId : protocolIdArr) {
//                TerminalProductProtocolRelationChangeModel terminalProductProtocolRelationChangeModel = terminalProductProtocolRelationChangeCommonDao.selectOne(new QueryWrapper<TerminalProductProtocolRelationChangeModel>()
//                        .eq("check_status", ProtocolCheckStatusEnum.OA_AUDIT.getKey())
//                        .eq("is_delete", DeleteFlagEnum.NOT_DELETE.getKey())
//                        .eq("terminal_shop_id", terminalShopNodeModel.getTerminalShopId()));
//                terminalProductProtocolRelationChangeModel.setCheckStatus(ProtocolCheckStatusEnum.FAIL.getKey());
//                terminalProductProtocolRelationChangeModel.setUpdateTime(LocalDateTime.now());
//                terminalProductProtocolRelationChangeCommonDao.updateById(terminalProductProtocolRelationChangeModel);
//                TerminalShopNodeModel terminalShopNodeModel1 = terminalShopNodeDao.selectOne(new QueryWrapper<TerminalShopNodeModel>().eq("terminal_shop_id", terminalShopNodeModel.getTerminalShopId())
//                        .eq("oa_id", oaId));
//                terminalShopNodeModel1.setNodeLevel("0");
//                terminalShopNodeModel1.setNodeStatus("1");
//                terminalShopNodeModel1.setIsBack("1");
//                terminalShopNodeModel1.setUpdateDate(new DateTime());

                TerminalProductProtocolRelationModel relationModel = new TerminalProductProtocolRelationModel();
                relationModel.setId(Long.parseLong(protocolId));
                relationModel.setCheckStatus(ProtocolCheckStatusEnum.OA_FAIL.getKey());
                relationModel.setUpdateTime(LocalDateTime.now());
                log.info("OA审批申请高端酒终端拒绝，影响的protocol表数据：", JSON.toJSONString(relationModel));
                terminalProductProtocolRelationDao.updateById(relationModel);
            }
        } else if (nodeType.equals(TerminalShopNodeEnum.UPDATE_PROTOCOL.getType())) {
            // 遍历当前节点的协议id数组protocolIdArr，更新协议状态
            for (String protocolId : protocolIdArr) {
                // 改change_detail审核状态
                TerminalProductProtocolRelationChangeDetailModel terminalProductProtocolRelationChangeDetailModel = terminalProductProtocolRelationChangeDetailCommonDao.selectOne(new QueryWrapper<TerminalProductProtocolRelationChangeDetailModel>().eq("id", protocolId)
                        .eq("is_delete", DeleteFlagEnum.NOT_DELETE.getKey()));
                TerminalProductProtocolRelationChangeModel terminalProductProtocolRelationChangeModel = terminalProductProtocolRelationChangeCommonDao.selectOne(new QueryWrapper<TerminalProductProtocolRelationChangeModel>().eq("id", terminalProductProtocolRelationChangeDetailModel.getChangeId())
                        .eq("is_delete", DeleteFlagEnum.NOT_DELETE.getKey()));
                terminalProductProtocolRelationChangeModel.setCheckStatus(ProtocolCheckStatusEnum.FAIL.getKey());
                terminalProductProtocolRelationChangeModel.setUpdateTime(LocalDateTime.now());
                terminalProductProtocolRelationChangeCommonDao.updateById(terminalProductProtocolRelationChangeModel);
//                TerminalShopNodeModel terminalShopNodeModel1 = terminalShopNodeDao.selectOne(new QueryWrapper<TerminalShopNodeModel>().eq("terminal_shop_id", terminalShopNodeModel.getTerminalShopId())
//                        .eq("oa_id", oaId));
//                terminalShopNodeModel1.setNodeLevel("0");
//                terminalShopNodeModel1.setNodeStatus("1");
//                terminalShopNodeModel1.setIsBack("1");
//                terminalShopNodeModel1.setUpdateDate(new DateTime());

                TerminalProductProtocolRelationModel relationModel = new TerminalProductProtocolRelationModel();
                relationModel.setId(terminalProductProtocolRelationChangeDetailModel.getNewId());
                relationModel.setCheckStatus(ProtocolCheckStatusEnum.OA_FAIL.getKey());
                relationModel.setUpdateTime(LocalDateTime.now());
                log.info("OA审批申请高端酒终端拒绝，影响的protocol表数据：", JSON.toJSONString(relationModel));
                terminalProductProtocolRelationDao.updateById(relationModel);
            }
        }

    }

    public void sendZtStateDataNewProtocol(TerminalShopInfoScheduleModel scheduleModel, TerminalShopContractResp terminalShopContractResp, int insertOrUpdate) {
        RequestLog requestLog = new RequestLog();
        long ernterSystemTime = System.currentTimeMillis();
        try {
            //封装请求参数
            JSONObject reqParam = getReqParamNewProtocol(insertOrUpdate, scheduleModel, terminalShopContractResp, "1", null, null);

            String token = ztUtils.getXwToken();
            //请求参数打印
            log.info("我是请求同步终端店数据的实体参数:{}", JSON.toJSONString(reqParam));
            log.info("我是请求同步终端店数据的token参数:{}", token);

            requestLog.setReqName("预备终端预激活信息:" + scheduleModel.getShopName());
            requestLog.setReqType(7);
            requestLog.setReqUrlPath(sendTerminalDataUrl);
            requestLog.setCreateDate(new Date());
            requestLog.setReqJson(reqParam.toJSONString());
            requestLog.setReqKey(scheduleModel.getShopName());
            String rtnJson = HttpUtil.createPost(sendTerminalDataUrl).header("token", token).header("Content-Type", "application/json").body(reqParam.toJSONString()).execute().body();
            requestLog.setResJson(rtnJson);
            scheduleModel.setStatus(3);
            if (com.intelliquor.cloud.shop.job.util.StringUtils.hasNull(rtnJson)) {
                JSONObject resp = JSONObject.parseObject(rtnJson);
                log.info("[返回参数]同步中台的采集终端---{}", JSON.toJSONString(resp));
                if (resp != null && resp.getJSONObject("resp_data") != null) {
                    JSONObject result = resp.getJSONObject("resp_data").getJSONObject("result");
                    if (null != result && result.get("success").equals("True")) {
                        scheduleModel.setStatus(1);
                        requestLog.setResCode("0");
                    } else {
                        requestLog.setResCode("-1");
                        requestLog.setResMsg(resp.toJSONString());
                        throw new BusinessException("400", "返回的参数:" + resp.toJSONString());
                    }
                } else {
                    requestLog.setResCode("-1");
                    requestLog.setResMsg(resp.toJSONString());
                    throw new BusinessException("400", "返回的参数:" + resp.toJSONString());
                }
            } else {
                requestLog.setResCode("-1");
                requestLog.setResMsg("没有返回值");
                throw new BusinessException("400", "没有返回值");
            }
        } catch (Exception e) {
            e.printStackTrace();
            requestLog.setResCode("-1");
            requestLog.setResMsg(e.getMessage());
            throw new BusinessException("400", "同步中台的采集终端数据失败:" + e.getMessage());
        } finally {
            long outSystemTime = System.currentTimeMillis();
            String diffTime = DateUtils.longToHMS(outSystemTime - ernterSystemTime);
            requestLog.setReqTime(diffTime);
            requestLogService.insertLog(requestLog);
        }
    }

    /**
     * @author: Sunjianbu
     * @Date: 2024/02/22
     * @Description: 封装请求推送中台的终端信息(无协议)
     */
    private JSONObject getReqParamNewProtocol(int insertOrUpdate, TerminalShopInfoScheduleModel scheduleModel, TerminalShopContractResp terminalShopContractResp,
                                              String status, List<TerminalProductProtocolRelationModel> terminalProductProtocolRelationModelList, TerminalProductProtocolRelationModel mainProtocolModel) {
        // 25年协议不用同步协议相关信息
         /**
        if (Objects.isNull(terminalProductProtocolRelationModelList)) {
            // 和中台确认，只同步最新生效的协议信息（最多两条）
            //QueryWrapper<TerminalProductProtocolRelationModel> queryWrapper = new QueryWrapper<TerminalProductProtocolRelationModel>()
            //        .eq("terminal_shop_id", scheduleModel.getTerminalShopId())
            //        .eq("check_status", ProtocolCheckStatusEnum.SUCCESS.getKey())
            //        .eq("is_delete", DeleteFlagEnum.NOT_DELETE.getKey());
            //queryWrapper.orderBy(true, true, "create_time");
            //terminalProductProtocolRelationModelList = terminalProductProtocolRelationDao.selectList(queryWrapper);
            terminalProductProtocolRelationModelList = new ArrayList<>();
            TerminalProductProtocolRelationModel displayProtocolProtocolRelationModel = terminalProductProtocolV2Service.getDisplayProtocolByCheckStatus(scheduleModel.getTerminalShopId(), scheduleModel.getCompanyId(), ProtocolCheckStatusEnum.SUCCESS.getKey());
            if (Objects.nonNull(displayProtocolProtocolRelationModel)) {
                terminalProductProtocolRelationModelList.add(displayProtocolProtocolRelationModel);
            }
            TerminalProductProtocolRelationModel packageQuantityProtocolProtocolRelationModel = terminalProductProtocolV2Service.getPackageQuantityByCheckStatus(scheduleModel.getTerminalShopId(), scheduleModel.getCompanyId(), ProtocolCheckStatusEnum.SUCCESS.getKey());
            if (Objects.nonNull(packageQuantityProtocolProtocolRelationModel)) {
                terminalProductProtocolRelationModelList.add(packageQuantityProtocolProtocolRelationModel);
            }
            TerminalProductProtocolRelationModel highEndProtocolDetail = terminalProductProtocolV2Service.getOldProtocol(scheduleModel.getTerminalShopId(), scheduleModel.getCompanyId(), ProtocolTypeEnum.HIGH_END_WINE_PACKAGE_QUANTITY.getKey(), ProtocolCheckStatusEnum.SUCCESS.getKey());
            if (Objects.nonNull(highEndProtocolDetail)) {
                terminalProductProtocolRelationModelList.add(highEndProtocolDetail);
            }
        }
        */
        if (Objects.nonNull(mainProtocolModel)) {
            if (terminalProductProtocolRelationModelList.isEmpty() || Objects.isNull(terminalProductProtocolRelationModelList)) {
                terminalProductProtocolRelationModelList = new ArrayList<>();
            }
            terminalProductProtocolRelationModelList.add(mainProtocolModel);
        }
        JSONObject rtnJson = new JSONObject();
        JSONObject json = new JSONObject();
        // storecode	String	Y	终端主编码
        json.put("storecode", scheduleModel.getMainCode());
        //上级经销商/分销商信息
        if (Objects.nonNull(scheduleModel.getDistributorId()) && scheduleModel.getDistributorId() != 0) {
            TCloudDealerInfoModel cloudDealerInfoModel = cloudDealerInfoDao.selectById(scheduleModel.getDistributorId());
            json.put("parent_channelcode", cloudDealerInfoModel.getDealerCode());
            json.put("parent_channelname", cloudDealerInfoModel.getDealerName());
        } else if (Objects.nonNull(scheduleModel.getCopartnerId()) && scheduleModel.getCopartnerId() != 0) {
            TCloudDealerInfoModel cloudDealerInfoModel = cloudDealerInfoDao.selectById(scheduleModel.getCopartnerId());
            json.put("parent_channelcode", cloudDealerInfoModel.getDealerCode());
            json.put("parent_channelname", cloudDealerInfoModel.getDealerName());
        } else {
            json.put("parent_channelcode", terminalShopContractResp.getDealerCode());
            json.put("parent_channelname", terminalShopContractResp.getDealerName());
        }
        // 朱鹏说:如果跟换过经销商，副编码会重新生成新的   推送中台时 使用新的编码
        //deputy_code	String	Y	终端副编码
//        LambdaQueryWrapper<TerminalShopModifyRecordPlus> lqw = Wrappers.lambdaQuery();
//        lqw.eq(TerminalShopModifyRecordPlus::getTerminalShopId, scheduleModel.getTerminalShopId());
//        lqw.eq(TerminalShopModifyRecordPlus::getMemberShopId, scheduleModel.getMemberShopId());
//        lqw.orderByDesc(TerminalShopModifyRecordPlus::getId);
//        List<TerminalShopModifyRecordPlus> terminalShopModifyRecordList = terminalShopModifyRecordMapper.selectList(lqw);
//        if (CollectionUtils.isNotEmpty(terminalShopModifyRecordList)) {
//            json.put("deputy_code", terminalShopModifyRecordList.get(0).getNewDeputyCode());
//        } else {
//            json.put("deputy_code", scheduleModel.getDeputyCode());
//        }
        json.put("deputy_code", scheduleModel.getDeputyCode());
        //storename	String	Y	终端名称
        json.put("storename", scheduleModel.getShopName());
        //  contracttype	String	Y	合同类型	合同类型(0:主品合同,1:酱酒合同 2：常规渠道经销合同 3：国台酱酒经销合同 4：专卖店经销合同 5：数智体验中心经销合同 6：团购特约经销合同 7：电商平台经销合同)
        json.put("contracttype", terminalShopContractResp.getContractType() + "");
        // contractcode	String	Y	合同编码
        json.put("contractcode", terminalShopContractResp.getContractCode());
        // contactname	String	Y	终端负责人姓名	存在校验：需在中台配置完成
        json.put("contactname", scheduleModel.getLeaderName());
        // contactphone	String	Y	终端负责人电话
        json.put("contactphone", scheduleModel.getLeaderPhone());
        //tag	Number	Y	终端标签	0:终端关注
        json.put("tag", scheduleModel.getTag() + "");
        //headimg	String	Y	门头照
        json.put("headimg", scheduleModel.getHeadImg());
        //remark String	N	备注
        json.put("remark", com.intelliquor.cloud.shop.job.util.StringUtils.hasNull(scheduleModel.getRemark()) ? scheduleModel.getRemark() : "");

        //province	String	Y	省
        json.put("province", scheduleModel.getProvince());
        //city	String	Y	市
        json.put("city", scheduleModel.getCity());
        //district	String	Y	区
        json.put("district", scheduleModel.getDistrict());
        //address	String	Y	详细地址
        json.put("address", scheduleModel.getAddress());
        //longitude	String	Y	经度
        json.put("longitude", scheduleModel.getLongitude() + "");
        //latitude	String	Y	纬度
        json.put("latitude", scheduleModel.getLatitude() + "");
        // is_image	String	Y	是否形象店	0- 否 1-是
        json.put("is_image", scheduleModel.getIsImage() + "");
        // storecontactname	String	N	店员姓名
        json.put("storecontactname", "");
        // storecontactphone	String	N	店员电话
        json.put("storecontactphone", "");
        //0- 否，无营业执照 1-是,有营业执照
        json.put("is_business_licence", scheduleModel.getWhetherLicense() + "");
        //license_code	String	N	营业执照编号
        json.put("license_code", com.intelliquor.cloud.shop.job.util.StringUtils.hasNull(scheduleModel.getLicenseCode()) ? scheduleModel.getLicenseCode() : "");

        //license_img	String	N	营业执照照片
        json.put("license_img", com.intelliquor.cloud.shop.job.util.StringUtils.hasNull(scheduleModel.getLicenseCode()) ? scheduleModel.getLicenseImg() : "");

        // payment_method	String	N	收款方式
        json.put("payment_method", CodeConstant.RECEIVING_PAYMENT_TYPE_MAP.get(scheduleModel.getReceivingPaymentType()));
        //payee_name	String	N	收款人姓名
        json.put("payee_name", scheduleModel.getReceivingPaymentName());
        //payee_account	String	N	收款人账号
        json.put("payee_account", com.intelliquor.cloud.shop.job.util.StringUtils.hasNull(scheduleModel.getReceivingPaymentAccount()) ? scheduleModel.getReceivingPaymentAccount() : "");
        //deposit_bank	String	N	开户行
        json.put("deposit_bank", com.intelliquor.cloud.shop.job.util.StringUtils.hasNull(scheduleModel.getReceivingPaymentBank()) ? scheduleModel.getReceivingPaymentBank() : "");

        //store_type	String	Y	终端类型 	1-烟酒店、2-专卖店、3-餐饮店、4-商超 5-渠道终端 6-企业终端 7-餐饮终端 8-连锁终端 9-团购终端 10-渠道终端会员 11-连锁终端会员 12-非会员虚拟终端 14-超级终端
        int shopType = scheduleModel.getShopType().intValue();
        String storeType = terminalShopCommonService.generateTerminalTypeByTerminalType(shopType);
        json.put("store_type", storeType);
        // 新终端类型 0:渠道终端 1:餐饮终端 2:团购终端 3:企业终端 4:连锁终端 5会员终端
        json.put("shop_type", shopType + "");
        //status	String	Y	激活状态 0- 未激活 1-已激活
        json.put("status", status);
        //option	String	Y	操作类型	1：新增；2：编辑；3：预采集
        json.put("option", (insertOrUpdate + 1) + "");
        //manager	String	Y	客户经理id
        TerminalAccountManagerModel accountManagerModel = setMangerInfo(scheduleModel.getCreateUser());
        if (Objects.nonNull(accountManagerModel)) {
            json.put("manager", accountManagerModel.getGtId() + "");
        }
        // 终端采集日期
        json.put("acquisition_date", DateUtils.convert2StringYYYYMMddHHmmss(scheduleModel.getCreateTime()));
        JSONArray gt_store_agreement = new JSONArray();
        // 25年协议不用同步协议相关信息
         /**
        if(Objects.nonNull(terminalProductProtocolRelationModelList) && terminalProductProtocolRelationModelList.size() > 0) {
            // 查询所有协议配置信息并将其生成map对象，其中protocol_code作为key,protocol_type作为value
            List<TerminalProductProtocolConfigModel> terminalProductProtocolConfigList = terminalProductProtocolConfigService.selectTerminalProductProtocolConfigList(new TerminalProductProtocolConfigModel());
            Map<String, Integer> protocolTypeMap = terminalProductProtocolConfigList.stream().collect(Collectors.toMap(TerminalProductProtocolConfigModel::getProtocolCode, TerminalProductProtocolConfigModel::getProtocolType));

            // 查询所有协议配置信息并将其生成map对象，其中code作为key,code_name作为value
            List<NodeVo> list =  codeBaseService.selectNodeList("PRODUCT_TYPE");
            Map<String, String> productTypeMap = list.stream().collect(Collectors.toMap(NodeVo::getValue, NodeVo::getLabel));

            for (TerminalProductProtocolRelationModel terminalProductProtocolRelationModel : terminalProductProtocolRelationModelList) {
                JSONObject protocolJson = new JSONObject();
                //type	String	N	协议类型 主协议、附加协议
                protocolJson.put("type", "0");
                //protocol_property	String	N 协议名称	陈列包量协议、陈列协议  0, "陈列包量协议",1, "陈列协议"
                protocolJson.put("protocol_property", ProtocolTypeEnum.getValue(protocolTypeMap.get(terminalProductProtocolRelationModel.getProtocolCode())));
                //productname	String	N	协议产品名称   国标、酱酒  1:国台国标 2:国台酱酒 3:高端酒
                protocolJson.put("productname", productTypeMap.get(terminalProductProtocolRelationModel.getProtocolProductCode()));

                // number	String	N	 数量
                // 若为高端酒则传number
                if (terminalProductProtocolRelationModel.getProtocolType().equals(ProtocolTypeEnum.HIGH_END_WINE_PACKAGE_QUANTITY.getKey())) {
                    TerminalProductProtocolConfigModel configModel = terminalProductProtocolConfigService.getProductProtocolConfigByProtocolCode(terminalProductProtocolRelationModel.getProtocolCode());
                    // 根据code获取config
                    protocolJson.put("number", configModel.getYearScanInNum().toString());
                }

                //storelevel  Number	N	终端等级
                // TerminalShopLevelModel terminalShopLevelModel = terminalShopLevelDao.selectById(terminalProtocolModel.getLevelCode());
                protocolJson.put("storelevel", terminalProductProtocolRelationModel.getLevelCode());
                //display_image	String	N	陈列协议照片
                protocolJson.put("display_image", terminalProductProtocolRelationModel.getProtocolImage());
                //createtime	String	N	创建时间
                String stringYYYYMMdd = DateUtils.convert2StringYYYYMMddHHmmss(new Date());
                if (Objects.nonNull(terminalProductProtocolRelationModel.getCreateTime())) {
                    LocalDateTime createTime = terminalProductProtocolRelationModel.getCreateTime();
                    DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    String dateStr = createTime.format(fmt);
                    stringYYYYMMdd = dateStr;
                    protocolJson.put("createtime", dateStr);
                } else {
                    protocolJson.put("createtime", stringYYYYMMdd);
                }
                //updatetime	String	N	修改时间
                if (Objects.nonNull(terminalProductProtocolRelationModel.getUpdateTime())) {
                    LocalDateTime updateTime = terminalProductProtocolRelationModel.getUpdateTime();
                    DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    String dateStr = updateTime.format(fmt);
                    protocolJson.put("updatetime", dateStr);
                } else {
                    protocolJson.put("updatetime", stringYYYYMMdd);
                }
                //添加到集合
                gt_store_agreement.add(protocolJson);
            }
        }
        */
        rtnJson.put("gt_store", json);
        rtnJson.put("gt_store_agreement", gt_store_agreement);
        return rtnJson;
    }

}
