<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.intelliquor.cloud.shop.job.dao.TerminalShopDao">

    <sql id="selectSql">
        id,
        shop_name,
        leader_name,
        leader_phone,
        tag,
        head_img,
        remark,
        province,
        city,
        district,
        address,
        longitude,
        latitude,
        type,
        is_image,
        keeper_name,
        keeper_phone,
        shop_area,
        license_img,
        license_code,
        status,
        is_delete,
        create_time,
        create_user,
        update_time,
        company_id,
        member_shop_id
    </sql>










    <select id="selectShopIdByCreateUser" resultType="java.lang.Integer">
        select
        ts.id
        from
        t_terminal_shop ts
        left join
        t_terminal_account_manager tam
        on ts.create_user = tam.id
        where
        ts.create_user = #{createUser}
        or
        tam.parent_id = #{createUser}
    </select>

    <select id="checkTerminalGtCode" parameterType="String" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM t_cloud_dealer_gt_terminal_apply
        where terminal_code=#{code} or deputy_code=#{code}
    </select>



    <select id="checkTerminalCode" parameterType="String" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM t_terminal_shop
        where main_code=#{code} or deputy_code=#{code}
    </select>





    <select id="selectDisplayTerminal" resultType="com.intelliquor.cloud.shop.job.model.resp.TerminalShopDisplayResp">
        select
        tts.id,
        tts.shop_name,
        tts.main_code,
        tts.deputy_code,
        tts.company_id,
        cdi.dealer_code,
        cdi.dealer_name,
        ttsc.contract_type,
        ttsc.contract_code
        from
        t_terminal_shop tts
        left join t_terminal_shop_contract ttsc on ttsc.terminal_shop_id = tts.id
        left join
        t_cloud_dealer_info cdi on ttsc.dealer_code = cdi.dealer_code
        where tts.status = 1 and tts.is_delete=0 and tts.company_id = #{companyId}
        and ttsc.contract_type in (2,3)
        <if test="shopIdList != null and shopIdList.size > 0">
            and tts.id not in
            <foreach collection="shopIdList" separator="," item="id" close=")" open="(">
                #{id}
            </foreach>
        </if>


    </select>





    <select id="selectAuditDataByShopId" resultType="com.intelliquor.cloud.shop.job.model.resp.GtOpenTerminalShopAuditResp">
        select
        ts.id,
        ts.terminal_shop_id,
        ts.shop_name,
        cdi.dealer_name,
        cdi.dealer_code,
        tsc.contract_code,
        tsc.contract_type,
        ts.province,
        ts.city,
        ts.district,
        ts.contact_name,
        ts.contact_level,
        ts.contact_phone,
        ts.address,
        ts.license_code,
        ts.shop_type,
        ts.food_business_license,
        ts.receiving_payment_type,
        ts.receiving_payment_name,
        ts.receiving_payment_account,
        ts.receiving_payment_bank,
        ts.receiving_warehouse_province,
        ts.receiving_warehouse_city,
        ts.receiving_warehouse_district,
        ts.receiving_warehouse_address,
        ts.whether_proprietary_trading,
        ts.is_image,
        ts.deputy_code,
        ts.main_code,
        tsl.level_name as level_code,
        tsc.package_quantity_image,
        tsc.display_image,
        ts.head_img,
        ts.image_head_picture,
        ts.receiving_payment_account_picture,
        ts.license_img,
        tsc.package_quantity_name,
        tsc.package_quantity_display_surface,
        tsc.package_quantity_display_cost,
        tsc.package_quantity_replenish_stock_quantity,
        tsc.display_name,
        tsc.display_display_surface,
        tsc.display_display_cost,
        tsc.display_replenish_stock_quantity,
        ts.tag,
        ts.leader_name,
        ts.leader_phone,
        ts.remark,
        ts.whether_license,
        ts.status,
        ts.create_time,
        ts.company_id,
        ts.member_shop_id,
        ts.is_high_member,
        cdii.dealer_name as distributorName,
        CONCAT(ts.province, ts.city, ts.district) AS area_name,
        tdcr.affiliate_name,
        tdcr.region_name,
        if(am.parent_id=0,am.name ,(select ams.name from t_terminal_account_manager ams WHERE am.parent_id = ams.id) ) as accountManagerName
        from
        t_terminal_shop_info_schedule ts
        left join
        t_terminal_shop_contract tsc
        on ts.terminal_shop_id = tsc.terminal_shop_id
        left join
        t_cloud_dealer_info cdi
        on tsc.dealer_code = cdi.dealer_code
        left join
        t_cloud_dealer_info cdii
        on ts.distributor_id = cdii.id
        left join
        t_terminal_shop_level tsl
        on ts.level_code = tsl.id
        left join t_dealer_contract_rel tdcr
        on tsc.contract_code = tdcr.contract_code
        LEFT JOIN t_terminal_account_manager am ON ts.create_user = am.id
        where
        ts.id = #{shopId}
    </select>

    <select id="selectAuditData" resultType="com.intelliquor.cloud.shop.job.model.resp.GtOpenTerminalShopAuditResp">
        select
        ts.id,
        ts.shop_name,
        cdi.dealer_name,
        cdi.dealer_code,
        tsc.contract_code,
        tsc.contract_type,
        ts.province,
        ts.city,
        ts.district,
        ts.contact_name,
        ts.contact_level,
        ts.contact_phone,
        ts.address,
        ts.license_code,
        ts.shop_type,
        ts.food_business_license,
        ts.receiving_payment_type,
        ts.receiving_payment_name,
        ts.receiving_payment_account,
        ts.receiving_payment_bank,
        ts.receiving_warehouse_province,
        ts.receiving_warehouse_city,
        ts.receiving_warehouse_district,
        ts.receiving_warehouse_address,
        ts.whether_proprietary_trading,
        ts.is_image,
        ts.deputy_code,
        ts.main_code,
        tsl.level_name as level_code,
        tsc.package_quantity_image,
        tsc.display_image,
        ts.head_img,
        ts.image_head_picture,
        ts.receiving_payment_account_picture,
        ts.license_img,
        tsc.package_quantity_name,
        tsc.package_quantity_display_surface,
        tsc.package_quantity_display_cost,
        tsc.package_quantity_replenish_stock_quantity,
        tsc.display_name,
        tsc.display_display_surface,
        tsc.display_display_cost,
        tsc.display_replenish_stock_quantity,
        ts.tag,
        ts.leader_name,
        ts.leader_phone,
        ts.remark,
        ts.whether_license,
        ts.status,
        ts.create_time,
        ts.company_id,
        ts.member_shop_id,
        ts.is_high_member,
        cdii.dealer_name as distributorName,
        CONCAT(ts.province, ts.city, ts.district) AS area_name,
        tdcr.affiliate_name,
        tdcr.region_name,
        if(am.parent_id=0,am.name ,(select ams.name from t_terminal_account_manager ams WHERE am.parent_id = ams.id) ) as accountManagerName
        from
        t_terminal_shop ts
        left join
        t_terminal_shop_contract tsc
        on ts.id = tsc.terminal_shop_id
        left join
        t_cloud_dealer_info cdi
        on tsc.dealer_code = cdi.dealer_code
        left join
        t_cloud_dealer_info cdii
        on ts.distributor_id = cdii.id
        left join
        t_terminal_shop_level tsl
        on ts.level_code = tsl.id
        left join t_dealer_contract_rel tdcr
        on tsc.contract_code = tdcr.contract_code
        LEFT JOIN t_terminal_account_manager am ON ts.create_user = am.id
        where
        ts.id = #{shopId}
    </select>


    <select id="selectSimilarTerminal" resultType="com.intelliquor.cloud.shop.job.model.resp.SimilarTerminalResp">
        select
            tsis.id as shopId,
            ts.shop_name as shopName,
            ts.leader_name as leaderName,
            ts.leader_phone as leaderPhone
        from
            t_terminal_shop ts
            left join t_terminal_shop_contract tsc on ts.id = tsc.terminal_shop_id
            left join t_cloud_dealer_info cdi on tsc.dealer_code = cdi.dealer_code
            LEFT JOIN t_terminal_shop_info_schedule tsis ON tsis.terminal_shop_id = ts.id
        where
            ts.id != #{req.shopId}
            and(ts.shop_name = #{req.shopName} and ts.city = #{req.city} )
            or (tsc.contract_code = #{req.contractCode} and cdi.dealer_name = #{req.dealerName}  and (ts.leader_name = #{req.leaderName} or ts.leader_phone = #{req.leaderPhone} or ts.address = #{req.address}))
            or (ts.shop_name like concat('%', #{req.shopName}, '%') and tsc.contract_code = #{req.contractCode} and ts.province = #{req.province} and ts.city = #{req.city})
    </select>

    <select id="getShopScheduleInfoById" resultType="java.util.Map">
        select
        tdcr.affiliate_name,
        tdcr.region_name,
        if(am.parent_id=0,am.name ,(select ams.name from t_terminal_account_manager ams WHERE am.parent_id = ams.id) ) as accountManagerName
        from  t_terminal_shop ts
        left join  t_terminal_shop_contract tsc
        on ts.id = tsc.terminal_shop_id
        left join t_dealer_contract_rel tdcr
        on tsc.contract_code = tdcr.contract_code
        LEFT JOIN t_terminal_account_manager am ON ts.create_user = am.id
        where  ts.id = #{id}
    </select>
    <select id="getTerminalIdListByDisplay" resultType="com.intelliquor.cloud.shop.job.model.TerminalShopModel">
        select * from t_terminal_shop tt where tt.status =1 and tt.is_prepare =0 and tt.is_delete = 0 and tt.shop_type in(0,1,2,3,4,9)
        and not exists ( select 1 from t_display_result t where t.month_year = #{monthYear} and t.source_flag != 0  and t.terminal_shop_id = tt.id)
        ORDER BY id  limit 5000
    </select>

    <select id="getTerminalShopNumByContractCodeAndShopType" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.job.model.resp.FindMapResp">
        SELECT
        COUNT(DISTINCT ts.id) as count,
        ts.shop_type as shopType
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND ts.shop_type != 5
        AND tsc.contract_code = #{contractCode}
        <if test="startTime != null and startTime != ''">
            AND ts.create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND ts.create_time &lt;= #{endTime}
        </if>
        <if test="shopType != null and shopType != ''">
            AND ts.shop_type = #{shopType}
        </if>
        GROUP BY ts.shop_type
    </select>

    <select id="getTerminalShopCountExistVisit" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.job.model.resp.FindMapResp">
        SELECT
            COUNT(DISTINCT datas.tsId) as count,
            datas.shop_type as shopType
        FROM (
                 SELECT
                     DISTINCT ts.id as tsId,
                    ts.shop_type,
                              (SELECT COUNT(visit.id) FROM t_terminal_visit_record visit WHERE visit.shop_id = ts.member_shop_id AND visit.create_time &gt;= #{startTime} AND visit.create_time &lt;= #{endTime}) as visitCount
                 FROM
                     t_terminal_shop ts
                         LEFT JOIN
                     t_terminal_shop_contract tsc
                     ON
                         ts.id = tsc.terminal_shop_id
                 WHERE
                     ts.`status` = 1
                   AND ts.is_prepare = 0
                   AND ts.is_delete = 0
                   AND ts.shop_type != 5
                    <if test="shopType != null and shopType != ''">
                        AND ts.shop_type = #{shopType}
                    </if>
                   AND tsc.contract_code = #{contractCode}
                   AND ts.create_time &lt;= #{endTime}
                 HAVING visitCount > 0
             ) as datas
            GROUP BY datas.shop_type
    </select>

    <select id="getTerminalShopCountNotExistVisit" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.job.model.resp.FindMapResp">
        SELECT
            COUNT(DISTINCT datas.tsId) as count,
            datas.shop_type as shopType
        FROM (
                 SELECT
                     DISTINCT ts.id as tsId,
                    ts.shop_type,
                              (SELECT COUNT(visit.id) FROM t_terminal_visit_record visit WHERE visit.shop_id = ts.member_shop_id AND visit.create_time &gt;= #{startTime} AND visit.create_time &lt;= #{endTime}) as visitCount
                 FROM
                     t_terminal_shop ts
                         LEFT JOIN
                     t_terminal_shop_contract tsc
                     ON
                         ts.id = tsc.terminal_shop_id
                 WHERE
                     ts.`status` = 1
                   AND ts.is_prepare = 0
                   AND ts.is_delete = 0
                   AND ts.shop_type != 5
                    <if test="shopType != null and shopType != ''">
                        AND ts.shop_type = #{shopType}
                    </if>
                   AND tsc.contract_code = #{contractCode}
                   AND ts.create_time &lt;= #{endTime}
                 HAVING visitCount = 0
             ) as datas
        GROUP BY datas.shop_type
    </select>

    <select id="getTerminalShopNumByContractCodeAndShopLevel" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.job.model.resp.FindMapResp">
        SELECT
        COUNT(DISTINCT ts.id) as count,
        tsl.id as shopType
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        LEFT JOIN
        (SELECT MAX(tp2.create_time), tp2.* FROM t_terminal_protocol tp2 WHERE tp2.delete_status = 0 and tp2.protocol_type = 0 GROUP BY tp2.terminal_shop_id) as tp
        ON
        ts.id = tp.terminal_shop_id
        LEFT JOIN
        t_terminal_shop_level tsl
        ON
        tp.level_code = tsl.id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND tsc.contract_code = #{contractCode}
        <if test="startTime != null and startTime != ''">
            AND ts.create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND ts.create_time &lt;= #{endTime}
        </if>
        <if test="shopLevel != null and shopLevel != ''">
            AND tsl.id = #{shopLevel}
        </if>
        GROUP BY tsl.id
    </select>

    <select id="getTerminalShopCountNotExistSkuCheckPass" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.job.model.resp.FindMapResp">
        SELECT
        COUNT(DISTINCT datas.tsId) as count,
        datas.tslId as shopType
        FROM (
        SELECT
        DISTINCT ts.id as tsId,
        tsl.id as tslId,
        (SELECT COUNT(sku.id) FROM t_terminal_sku_check sku WHERE sku.terminal_shop_id = ts.id AND sku.create_time &gt;= #{startTime} AND sku.create_time &lt;= #{endTime} AND (sku.check_status = 0 OR sku.check_status = 1)) as skuCount
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        LEFT JOIN
        (SELECT MAX(tp2.create_time), tp2.* FROM t_terminal_protocol tp2 WHERE tp2.delete_status = 0 and tp2.protocol_type = 0 GROUP BY tp2.terminal_shop_id) as tp
        ON
        ts.id = tp.terminal_shop_id
        LEFT JOIN
        t_terminal_shop_level tsl
        ON
        tp.level_code = tsl.id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND tsc.contract_code = #{contractCode}
        <if test="endTime != null and endTime != ''">
            AND ts.create_time &lt;= #{endTime}
        </if>
        <if test="shopLevel != null and shopLevel != ''">
            AND tsl.id = #{shopLevel}
        </if>
        HAVING skuCount = 0
        ) as datas
        GROUP BY datas.tslId
    </select>

    <select id="getTerminalShopCountExistSkuCheckAuditIn" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.job.model.resp.FindMapResp">
        SELECT
        COUNT(DISTINCT datas.tsId) as count,
        datas.tslId as shopType
        FROM (
        SELECT
        DISTINCT ts.id as tsId,
        tsl.id as tslId,
        (SELECT COUNT(sku.id) FROM t_terminal_sku_check sku WHERE sku.terminal_shop_id = ts.id AND sku.create_time &gt;= #{startTime} AND sku.create_time &lt;= #{endTime} AND (sku.check_status = 0 OR sku.check_status = 3)) as skuCount,
        (SELECT COUNT(sku.id) FROM t_terminal_sku_check sku WHERE sku.terminal_shop_id = ts.id AND sku.create_time &gt;= #{startTime} AND sku.create_time &lt;= #{endTime} AND sku.check_status = 1) as skuPassCount
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        LEFT JOIN
        (SELECT MAX(tp2.create_time), tp2.* FROM t_terminal_protocol tp2 WHERE tp2.delete_status = 0 and tp2.protocol_type = 0 GROUP BY tp2.terminal_shop_id) as tp
        ON
        ts.id = tp.terminal_shop_id
        LEFT JOIN
        t_terminal_shop_level tsl
        ON
        tp.level_code = tsl.id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND tsc.contract_code = #{contractCode}
        <if test="endTime != null and endTime != ''">
            AND ts.create_time &lt;= #{endTime}
        </if>
        <if test="shopLevel != null and shopLevel != ''">
            AND tsl.id = #{shopLevel}
        </if>
        HAVING skuCount > 0 AND skuPassCount = 0
        ) as datas
        GROUP BY datas.tslId
    </select>

    <select id="getTerminalSkuCheckDisplayNumTotal" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.job.model.resp.FindMapResp">
        SELECT
        IFNULL(SUM(tpp.display_surface), 0) as count,
        tsl.id as shopType
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        LEFT JOIN
        t_terminal_protocol tp
        ON
        ts.id = tp.terminal_shop_id
        LEFT JOIN
        t_terminal_shop_level tsl
        ON
        tp.level_code = tsl.id
        LEFT JOIN
        t_terminal_product_protocol tpp
        ON
        tp.product_protocol_config_id = tpp.id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND tp.delete_status = 0
        AND tp.protocol_type = 0
        AND tp.check_status=1
        AND tp.effective_time &lt;= #{fifteenTime}
        AND tsc.contract_code = #{contractCode}
        <if test="endTime != null and endTime != ''">
            AND ts.create_time &lt;= #{endTime}
        </if>
        <if test="shopLevel != null and shopLevel != ''">
            AND tsl.id = #{shopLevel}
        </if>
        GROUP BY tsl.id
    </select>

    <!--查询创建时间超过30天的数据-->
    <select id="selectTerminalDisableTask" resultType="com.intelliquor.cloud.shop.job.model.TerminalShopModel">
        select ts.*
        from t_terminal_shop ts
                 left join t_member_shop ms ON ms.id = ts.member_shop_id
        where ts.status = 1
          and ts.is_delete = 0
          and ts.merge_type = 0
          and ms.status = 0
          and ts.create_time &lt;= DATE_SUB(NOW(), INTERVAL 30 DAY) limit 5000
    </select>
    <select id="getTerminalIdListByDisplayNew"
            resultType="com.intelliquor.cloud.shop.job.model.TerminalShopModel">
        select tt.* from t_terminal_shop tt  where tt.status =1 and tt.is_prepare =0 and tt.is_delete = 0 and tt.shop_type in(0,1,2,3,4,9)
        and tt.id in (SELECT DISTINCT dt.terminal_shop_id FROM t_display_result	dt	WHERE 	dt.data_type = #{dataType}  and dt.month_year = #{monthYear} and dt.source_flag=1 and dt.qualify_flag=0)
        ORDER BY tt.id  limit 5000
    </select>


    <select id="findVisitShopListStatistics" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.job.model.resp.VisitStatisticsResp">
        SELECT
            COUNT(DISTINCT datas.tsId) as storeNums,
            SUM(CASE WHEN datas.createTime &lt;= #{endTime} THEN 1 ELSE 0 END) as currentStoreNums,
            SUM(CASE WHEN datas.createTime &lt;= #{endTime} AND datas.createTime &gt;= #{startTime} THEN 1 ELSE 0 END) as currentAddNums,
            datas.shopType as shopType,
            SUM(datas.visitCount) as currentVisitNums,
            SUM(CASE WHEN datas.visitCount > 0 THEN 1 ELSE 0 END) as currentVisitStores,
            SUM(CASE WHEN datas.visitCount = 0 THEN 1 ELSE 0 END) as currentNotVisitStores
        FROM (
                 SELECT
                     ts.id as tsId,
                     ts.create_time as createTime,
                     ts.shop_type as shopType,
                     (SELECT COUNT(visit.id) FROM t_terminal_visit_record visit WHERE visit.shop_id = ts.member_shop_id AND visit.create_time &lt;= #{endTime} AND visit.create_time &gt;= #{startTime}) as visitCount
                 FROM
                     t_terminal_shop ts
                         LEFT JOIN
                     t_terminal_shop_contract tsc
                     ON
                         ts.id = tsc.terminal_shop_id
                 WHERE
                     ts.`status` = 1
                   AND ts.is_prepare = 0
                   AND ts.is_delete = 0
                   AND ts.shop_type NOT IN(5, 6, 7, 8)
                   AND tsc.contract_code = #{contractCode}
             ) datas GROUP BY datas.shopType
    </select>

    <select id="findSkuCheckShopListStatistics" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.job.model.resp.SkuCheckStatisticsResp">
        SELECT
            datas.shopType as agreementType,
            COUNT(DISTINCT datas.tsId) as storeNums,
            IFNULL(SUM(CASE WHEN datas.createTime &lt;= #{endTime} THEN 1 ELSE 0 END), 0) as currentStoreNums,
            IFNULL(SUM(CASE WHEN datas.createTime &lt;= #{endTime} AND datas.createTime &gt;= #{startTime} THEN 1 ELSE 0 END), 0) as currentAddNums,
            IFNULL(SUM(CASE WHEN datas.skuPassCount = 0 AND datas.skuCount = 0 THEN 1 ELSE 0 END), 0) as auditFailStores,
            IFNULL(SUM(CASE WHEN datas.skuPassCount = 0 AND datas.skuCount > 0 THEN 1 ELSE 0 END), 0) as auditInStores,
            IFNULL(SUM(datas.skuTotal),0) as auditDisplayNums
        FROM
            (
                SELECT
                    ts.id as tsId,
                    ts.create_time as createTime,
                    tsl.id as shopType,
                    (SELECT COUNT(sku.id) FROM t_terminal_sku_check sku WHERE sku.terminal_shop_id = ts.id AND sku.create_time &gt;= #{startTime} AND sku.create_time &lt;= #{endTime} AND ((sku.check_status = 0 AND sku.check_need = 1) OR sku.check_status = 3)) as skuCount,
                    (SELECT COUNT(sku.id) FROM t_terminal_sku_check sku WHERE sku.terminal_shop_id = ts.id AND sku.create_time &gt;= #{startTime} AND sku.create_time &lt;= #{endTime} AND (sku.check_status = 1 OR sku.check_status = 4)) as skuPassCount,
                    (SELECT MAX(sku.sku_total) FROM t_terminal_sku_check sku WHERE sku.terminal_shop_id = ts.id AND sku.create_time &gt;= #{startTime} AND sku.create_time &lt;= #{endTime} AND (sku.check_status = 1 OR sku.check_status = 4)) as skuTotal
                FROM
                    t_terminal_shop ts
                        LEFT JOIN
                    t_terminal_shop_contract tsc
                    ON
                        ts.id = tsc.terminal_shop_id
                        LEFT JOIN
                    t_terminal_protocol tp
                    ON
                        ts.id = tp.terminal_shop_id
                        LEFT JOIN
                    t_terminal_shop_level tsl
                    ON
                        tp.level_code = tsl.id
                WHERE
                    ts.`status` = 1
                  AND ts.is_prepare = 0
                  AND ts.is_delete = 0
                  AND tp.delete_status = 0
                  AND tp.protocol_type = 0
                  AND tp.check_status=1
                  AND tsc.contract_code = #{contractCode}
            ) datas GROUP BY datas.shopType
    </select>
</mapper>
