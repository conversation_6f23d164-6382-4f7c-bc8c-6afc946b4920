<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.intelliquor.cloud.shop.terminal.dao.TerminalShopContractDao">

    <sql id="selectSql">
        id,
        contract_type,
        dealer_code,
        contract_code,
        display_image,
        package_quantity_image,
        member_shop_id,
        terminal_shop_id,
        create_time,
        update_time
    </sql>

    <delete id="deleteByShopId">
        delete
        t_terminal_shop_contract,
        t_terminal_shop_contract_product
        from
        t_terminal_shop_contract
        left join
        t_terminal_shop_contract_product
        on t_terminal_shop_contract.id = t_terminal_shop_contract_product.contract_id
        where
        t_terminal_shop_contract.terminal_shop_id = #{shopId}
    </delete>

    <select id="selectTShopIdListByDealerCode" resultType="java.lang.Integer">
        select
        distinct
        tsc.terminal_shop_id
        from
        t_terminal_shop_contract tsc
        left join
        t_terminal_shop ts
        on tsc.terminal_shop_id = ts.id
        where
        tsc.dealer_code in
        <foreach collection="dealerModelList" separator="," open="(" close=")" item="dealerModel">
            #{dealerModel.dealerCode}
        </foreach>
        and
        ts.is_delete = 0
        and
        ts.is_prepare=0
        group by tsc.id
    </select>

    <select id="selectPartnerShopIdListByDealerCode" resultType="java.lang.Integer">
        select
        DISTINCT terminal_shop_id
        from
        t_terminal_shop_contract
        where
        contract_code in(
        select
        contract_code
        from
        t_dealer_contract_rel
        where dealer_code in
        <foreach collection="dealerModelList" separator="," open="(" close=")" item="dealerModel">
            #{dealerModel.dealerCode}
        </foreach>
        )
    </select>


    <select id="selectContractCodeByDealerCode" resultType="com.intelliquor.cloud.shop.terminal.model.TerminalShopContractModel">
        select
            contract_type,
            dealer_code,
            contract_code
        from
        t_terminal_shop_contract
        where
        dealer_code = #{dealerCode} and contract_type = #{contractType}
        group by contract_code
    </select>

    <select id="selectTerminalShopContractResp" resultType="com.intelliquor.cloud.shop.common.model.resp.TerminalShopContractResp">
        select
        tsc.id,
        tsc.contract_type,
        tsc.dealer_code,
        cdi.dealer_name,
        cdi.id as dealerId,
        tsc.contract_code,
        tsc.contract_code_name,
        tsc.display_image,
        tsc.package_quantity_image,
        tsc.member_shop_id,
        tsc.terminal_shop_id,
        tsc.create_time,
        tsc.update_time,
        tsc.package_quantity_name,
        tsc.package_quantity_display_surface,
        tsc.package_quantity_display_cost,
        tsc.package_quantity_replenish_stock_quantity,
        tsc.display_name,
        tsc.display_display_surface,
        tsc.display_display_cost,
        tsc.display_replenish_stock_quantity,
        tsc.display_surface,
        tsc.month_scan_in_num,
        tsc.year_scan_in_num,
        tsc.display_amount,
        tsc.package_amount
        from
        t_terminal_shop_contract tsc
        left join
        t_cloud_dealer_info cdi
        on
        tsc.dealer_code = cdi.dealer_code
        where
        tsc.terminal_shop_id = #{tShopId}
       /* group by tsc.id*/
    </select>

    <select id="selectTerminalShopContract25Resp" resultType="com.intelliquor.cloud.shop.common.model.resp.TerminalShopContract25Resp">
        select
            tsc.id,
            tsc.contract_type,
            tsc.dealer_code,
            cdi.dealer_name,
            cdi.id as dealerId,
            tsc.contract_code,
            tsc.contract_code_name,
            tsc.display_image,
            tsc.package_quantity_image,
            tsc.member_shop_id,
            tsc.terminal_shop_id,
            tsc.create_time,
            tsc.update_time,
            tsc.package_quantity_name,
            tsc.package_quantity_display_surface,
            tsc.package_quantity_display_cost,
            tsc.package_quantity_replenish_stock_quantity,
            tsc.display_name,
            tsc.display_display_surface,
            tsc.display_display_cost,
            tsc.display_replenish_stock_quantity,
            tsc.display_surface,
            tsc.month_scan_in_num,
            tsc.year_scan_in_num,
            tsc.display_amount,
            tsc.package_amount
        from
            t_terminal_shop_contract tsc
                left join
            t_cloud_dealer_info cdi
            on
                tsc.dealer_code = cdi.dealer_code
        where
            tsc.terminal_shop_id = #{tShopId}
        /* group by tsc.id*/
    </select>

    <update id="updateContract" parameterType="com.intelliquor.cloud.shop.terminal.model.TerminalShopContractModel">
        UPDATE t_terminal_shop_contract
        <set>
            <if test="contractType != null ">
                contract_type = #{contractType},
            </if>
            <if test="dealerCode != null and dealerCode!='' ">
                dealer_code = #{dealerCode},
            </if>
            <if test="contractCode != null and contractCode!='' ">
                contract_code = #{contractCode},
            </if>
            <if test="contractCodeName != null and contractCodeName!='' ">
                contract_code_name = #{contractCodeName},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        WHERE terminal_shop_id = #{terminalShopId} and member_shop_id = #{memberShopId}
    </update>


    <update id="updateBatchContract">
        UPDATE t_terminal_shop_contract
        <set>
            <if test="model.contractType != null ">
                contract_type = #{model.contractType},
            </if>
            <if test="model.dealerCode != null and model.dealerCode!='' ">
                dealer_code = #{model.dealerCode},
            </if>
            <if test="model.contractCode != null and model.contractCode!='' ">
                contract_code = #{model.contractCode},
            </if>
            <if test="model.contractCodeName != null and model.contractCodeName!='' ">
                contract_code_name = #{model.contractCodeName},
            </if>
            <if test="model.updateTime != null">
                update_time = #{model.updateTime},
            </if>
        </set>
        WHERE terminal_shop_id in
        <foreach collection="terminalShopIds" item="item"  separator="," open="(" close=")" >
            #{item}
        </foreach>
    </update>

    <insert id="insertContract" parameterType="com.intelliquor.cloud.shop.terminal.model.TerminalShopContractModel">
        insert into
        t_terminal_shop_contract(
        contract_type,
        dealer_code,
        contract_code,
        contract_code_name,
        member_shop_id,
        terminal_shop_id,
        create_time,
        update_time
        )values(
        #{contractType},
        #{dealerCode},
        #{contractCode},
        #{contractCodeName},
        #{memberShopId},
        #{terminalShopId},
        now(),
        now()
        )
    </insert>

    <select id="selectContractExistListByContractType" resultType="com.intelliquor.cloud.shop.common.model.ContractTypeCategoryDetailModel">
        SELECT
            tctcd2.*
        FROM
            t_terminal_shop_contract tsc
                join
            t_contract_type_category_detail tctcd
            ON tsc.contract_type  = tctcd.contract_type
                join
            t_contract_type_category_detail tctcd2
            ON tctcd.cat_id  = tctcd2.cat_id
        WHERE
            tctcd.is_delete = 0
        <if test="contractType != null ">
            and tsc.contract_type = #{contractType}
        </if>
        group by tctcd2.contract_type
    </select>

    <select id="selectContractsByLicenseCode" resultType="com.intelliquor.cloud.shop.terminal.model.TerminalShopModel">
        SELECT
        ms.*
        FROM
        t_terminal_shop_contract tsc2
        JOIN
        t_terminal_shop ms
        ON
        ms.id = tsc2.terminal_shop_id
        WHERE
        ms.status = 1
        AND
        ms.is_delete = 0
        AND
        ms.shop_type NOT IN (2, 4)
        <if test="licenseCode != null ">
            and ms.license_code = #{licenseCode}
        </if>
        <if test="contractTypes != null">
        and tsc2.contract_type in
            <foreach collection="contractTypes" item="item"  separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
