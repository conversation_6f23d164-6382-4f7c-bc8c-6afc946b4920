<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.intelliquor.cloud.shop.terminal.dao.TerminalShopDao">

    <sql id="selectSql">
        id,
        shop_name,
        leader_name,
        leader_phone,
        tag,
        head_img,
        remark,
        province,
        city,
        district,
        address,
        longitude,
        latitude,
        type,
        is_image,
        keeper_name,
        keeper_phone,
        shop_area,
        license_img,
        license_code,
        status,
        is_delete,
        create_time,
        create_user,
        update_time,
        company_id,
        member_shop_id
    </sql>

    <update id="updateIsDeleteById">
        update
        t_terminal_shop
        set is_delete = #{isDelete}
        where
        id = #{id}
    </update>

    <select id="selectTerminalShopListByIdList" resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalShopResp">
        select ts.id,
        ts.shop_name,
        ts.leader_name,
        ts.leader_phone,
        ts.tag,
        ts.head_img,
        ts.remark,
        ts.province,
        ts.city,
        ts.district,
        ts.address,
        ts.longitude,
        ts.latitude,
        ts.type,
        ts.is_image,
        ts.keeper_name,
        ts.keeper_phone,
        ts.shop_area,
        ts.license_img,
        ts.license_code,
        tsis.status,
        ts.is_delete,
        ts.create_time,
        ts.create_user,
        ts.update_time,
        ts.company_id,
        ts.member_shop_id,
        ts.level_code,
        ts.level_code                        as levelName,
        tam.name                             as managerOrStaffName,
        ts.shop_type                         as shopType,
        (case tam.type
        when 0 then '客户经理'
        when 1 then '经销商人员'
        when 2 then '终端人员'
        when 3 then '终端采集人员' end) as managerOrStaffType,
        ms.status as terminalStatus
        from t_terminal_shop ts
        left join
        t_member_shop ms
        on
        ts.member_shop_id = ms.id
        left join
        t_terminal_shop_level_config tslc
        on
        ms.level_code = tslc.level_code
        left join
        t_terminal_account_manager tam
        on
        ts.create_user = tam.id
        left join
        t_terminal_shop_info_schedule tsis
        on
        tsis.terminal_shop_id = ts.id
        where ts.is_delete = #{terminalShopManageReq.isDelete}
        and  tsis.status!=1
        and ts.shop_type not in (5,10,11,12,13)
        and ts.id in
        <foreach collection="idList" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>

        <if test="managerIdList != null and managerIdList.size > 0">
            and ts.create_user in
            <foreach collection="managerIdList" item="managerId" separator="," close=")" open="(">
                #{managerId}
            </foreach>
        </if>

        <if test="terminalShopManageReq.shopName != null and terminalShopManageReq.shopName != ''">
            and ts.shop_name like concat('%', #{terminalShopManageReq.shopName}, '%')
        </if>

        <if test="terminalShopManageReq.isHighMember != null ">
            and ts.is_high_member = #{terminalShopManageReq.isHighMember}
        </if>

        <if test="terminalShopManageReq.tag != null">
            and ts.tag = #{terminalShopManageReq.tag}
        </if>

        <if test="terminalShopManageReq.gatherMonth != null and terminalShopManageReq.gatherMonth != ''">
            and ts.create_time &gt;= #{terminalShopManageReq.gatherMonthStartTime}
            and ts.create_time &lt;= #{terminalShopManageReq.gatherMonthEndTime}
        </if>

        <if test="terminalShopManageReq.status != null">
            <choose>
                <when test="terminalShopManageReq.status == 98">
                    and tsis.status in (2, 4)
                </when>
                <when test="terminalShopManageReq.status == 99">
                    and tsis.status in (3, 5)
                </when>
                <otherwise>
                    and tsis.status = #{terminalShopManageReq.status}
                </otherwise>
            </choose>
        </if>

        <if test="terminalShopManageReq.shopType != null">
            and ts.shop_type = #{terminalShopManageReq.shopType}
        </if>

        <if test="terminalShopManageReq.level != null and terminalShopManageReq.level != '' and terminalShopManageReq.level != 6">
            and ms.level_code = #{terminalShopManageReq.level}
        </if>

        <if test="terminalShopManageReq.level != null and terminalShopManageReq.level != '' and terminalShopManageReq.level == 6">
            and (
            ms.level_code = 0
            or
            ms.level_code is null
            )
        </if>

        <if test="terminalShopManageReq.sort != null and terminalShopManageReq.sort == 0">
            order by ts.create_time desc
        </if>

        <if test="terminalShopManageReq.sort != null and terminalShopManageReq.sort == 1">
            order by ts.create_time asc
        </if>
    </select>

    <select id="selectHotelTerminalShopListByIdList" resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalShopResp">
        select ts.id,
        ts.shop_name,
        ts.leader_name,
        ts.leader_phone,
        ts.tag,
        ts.head_img,
        ts.remark,
        ts.province,
        ts.city,
        ts.district,
        ts.address,
        ts.longitude,
        ts.latitude,
        ts.type,
        ts.is_image,
        ts.keeper_name,
        ts.keeper_phone,
        ts.shop_area,
        ts.license_img,
        ts.license_code,
        tsis.status,
        ts.is_delete,
        ts.create_time,
        ts.create_user,
        ts.update_time,
        ts.company_id,
        ts.member_shop_id,
        ts.level_code,
        ts.level_code                        as levelName,
        tam.name                             as managerOrStaffName,
        ts.shop_type                         as shopType,
        (case tam.type
        when 0 then '客户经理'
        when 1 then '经销商人员'
        when 2 then '终端人员'
        when 3 then '终端采集人员' end) as managerOrStaffType,
        ms.status as terminalStatus
        from t_terminal_shop ts
        left join
        t_member_shop ms
        on
        ts.member_shop_id = ms.id
        left join
        t_terminal_shop_level_config tslc
        on
        ms.level_code = tslc.level_code
        left join
        t_terminal_account_manager tam
        on
        ts.create_user = tam.id
        left join
        t_terminal_shop_info_schedule tsis
        on
        tsis.terminal_shop_id = ts.id
        where ts.is_delete = #{terminalShopManageReq.isDelete}
        and ts.shop_type in (10,11,12,13)
        <if test="terminalShopManageReq.terminalStatus != null">
            and ms.status = #{terminalShopManageReq.terminalStatus}
        </if>
        and ts.id in
        <foreach collection="idList" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>

        <if test="managerIdList != null and managerIdList.size > 0">
            and ts.create_user in
            <foreach collection="managerIdList" item="managerId" separator="," close=")" open="(">
                #{managerId}
            </foreach>
        </if>

        <if test="terminalShopManageReq.shopName != null and terminalShopManageReq.shopName != ''">
            and ts.shop_name like concat('%', #{terminalShopManageReq.shopName}, '%')
        </if>

        <if test="terminalShopManageReq.isHighMember != null ">
            and ts.is_high_member = #{terminalShopManageReq.isHighMember}
        </if>

        <if test="terminalShopManageReq.tag != null">
            and ts.tag = #{terminalShopManageReq.tag}
        </if>

        <if test="terminalShopManageReq.gatherMonth != null and terminalShopManageReq.gatherMonth != ''">
            and ts.create_time &gt;= #{terminalShopManageReq.gatherMonthStartTime}
            and ts.create_time &lt;= #{terminalShopManageReq.gatherMonthEndTime}
        </if>

        <if test="terminalShopManageReq.status != null">
            <choose>
                <when test="terminalShopManageReq.status == 98">
                    and tsis.status in (2, 4)
                </when>
                <when test="terminalShopManageReq.status == 99">
                    and tsis.status in (3, 5)
                </when>
                <otherwise>
                    and tsis.status = #{terminalShopManageReq.status}
                </otherwise>
            </choose>
        </if>

        <if test="terminalShopManageReq.shopType != null">
            and ts.shop_type = #{terminalShopManageReq.shopType}
        </if>

        <if test="terminalShopManageReq.level != null and terminalShopManageReq.level != '' and terminalShopManageReq.level != 6">
            and ms.level_code = #{terminalShopManageReq.level}
        </if>

        <if test="terminalShopManageReq.level != null and terminalShopManageReq.level != '' and terminalShopManageReq.level == 6">
            and (
            ms.level_code = 0
            or
            ms.level_code is null
            )
        </if>
        <if test="terminalShopManageReq.sort != null and terminalShopManageReq.sort == 0">
            order by ts.create_time desc
        </if>

        <if test="terminalShopManageReq.sort != null and terminalShopManageReq.sort == 1">
            order by ts.create_time asc
        </if>
    </select>

    <select id="selectTerminalShopModifyListByIdList" resultType="com.intelliquor.cloud.shop.common.model.TerminalShopModifyModel">
        select
        ts.id,
        ts.shop_name,
        ts.shop_type,
        ts.address,
        ts.leader_name,
        ts.leader_phone,
        ts.main_code,
        ts.deputy_code,
        ts.status,
        ts.is_delete,
        ts.create_user,
        ts.create_time,
        ts.company_id,
        ts.member_shop_id,
        tsl.level_name,
        tsc.contract_type,
        tsc.contract_code,
        tam.name as managerOrStaffName,
        cdi.id as relationDealerId,
        ts.is_high_member,
        ts.merge_type,
        ts.merge_id
        from
        t_terminal_shop ts
        left join
        t_member_shop ms on ts.member_shop_id = ms.id
        left join
        t_terminal_shop_contract tsc on tsc.terminal_shop_id = ts.id
        left join
        t_cloud_dealer_info cdi on cdi.dealer_code = ms.dealer_code
        left join
        t_terminal_account_manager tam on ts.create_user = tam.id
        left join t_terminal_shop_level tsl on ts.level_code = tsl.id
        left join (select * from t_terminal_shop_merge where status=0) merge on ts.id=merge.from_id
        where
        ts.is_delete = 0
        and ts.status = 1
        and ts.company_id = #{req.companyId}
        and cdi.id is not null
        and ts.merge_type in (0,1) and merge.from_id is null
        <if test="req.modifyType != null">
            and ts.id not in (
            select terminal_shop_id from t_terminal_shop_modify_record where modify_type =#{req.modifyType} and status in (1,2)
            )
        </if>
        <if test="idList != null and idList.size > 0">
            and ts.id in
            <foreach collection="idList" separator="," open="(" close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="managerIdList != null and managerIdList.size > 0">
            and ts.create_user in
            <foreach collection="managerIdList" item="managerId" separator="," close=")" open="(">
                #{managerId}
            </foreach>
        </if>
        <if test="req.shopName != null and req.shopName != ''">
            and ts.shop_name like concat('%',#{req.shopName},'%')
        </if>
        <if test="req.createTimeStart != null and req.createTimeStart != ''">
            and ts.create_time &gt;= #{req.createTimeStart}
        </if>
        <if test="req.createTimeEnd != null and req.createTimeEnd != ''">
            and ts.create_time &lt;= #{req.createTimeEnd}
        </if>
        <if test="req.shopType != null">
            and ts.shop_type = #{req.shopType}
        </if>
        <if test="req.mergeType != null">
            and ts.merge_type = #{req.mergeType}
        </if>
        <if test="req.modifyStatus != null">
            <if test="req.modifyStatus ==99">
                and tsmr.status IS NULL
            </if>
            <if test="req.modifyStatus !=99">
                and tsmr.status = #{req.modifyStatus}
            </if>
        </if>
        <if test="req.province != null and req.province != '' ">
            and ts.province = #{req.province}
        </if>
        <if test="req.city != null and req.city != '' ">
            and ts.city = #{req.city}
        </if>
        <if test="req.district != null and req.district != '' ">
            and ts.district = #{req.district}
        </if>
        <if test="req.contractCode != null and req.contractCode != '' ">
            and tsc.contract_code = #{req.contractCode}
        </if>
        <if test="req.contractType != null and req.contractType != '' ">
            and tsc.contract_type = #{req.contractType}
        </if>
        order by ts.create_time desc

    </select>

    <select id="selectTerminalShopModifyListByIdListAdmin" resultType="com.intelliquor.cloud.shop.common.model.TerminalShopModifyModel">
        select
        ts.id,
        ts.shop_name,
        ts.shop_type,
        ts.address,
        ts.leader_name,
        ts.leader_phone,
        ts.main_code,
        ts.deputy_code,
        ts.status,
        ts.is_delete,
        ts.create_user,
        ts.create_time,
        ts.company_id,
        ts.member_shop_id,
        tsl.level_name,
        tsc.contract_type,
        tsc.contract_code,
        tam.name as managerOrStaffName,
        cdi.id as relationDealerId,
        ts.is_high_member,
        ts.merge_type,
        ts.merge_id
        from
        t_terminal_shop ts
        left join
        t_member_shop ms on ts.member_shop_id = ms.id
        left join
        t_terminal_shop_contract tsc on tsc.terminal_shop_id = ts.id
        left join
        t_cloud_dealer_info cdi on cdi.dealer_code = ms.dealer_code
        left join
        t_terminal_account_manager tam on ts.create_user = tam.id
        left join t_terminal_shop_level tsl on ts.level_code = tsl.id
        where
        ts.is_delete = 0
        and ts.status = 1
        and ts.company_id = #{req.companyId}
        and cdi.id is not null
        <if test="req.modifyType != null">
            and ts.id not in (
            select terminal_shop_id from t_terminal_shop_modify_record where modify_type =#{req.modifyType} and status in (1,2)
            )
        </if>
        <if test="idList != null and idList.size > 0">
            and ts.id in
            <foreach collection="idList" separator="," open="(" close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="managerIdList != null and managerIdList.size > 0">
            and ts.create_user in
            <foreach collection="managerIdList" item="managerId" separator="," close=")" open="(">
                #{managerId}
            </foreach>
        </if>
        <if test="req.shopName != null and req.shopName != ''">
            and ts.shop_name like concat('%',#{req.shopName},'%')
        </if>
        <if test="req.createTimeStart != null and req.createTimeStart != ''">
            and ts.create_time &gt;= #{req.createTimeStart}
        </if>
        <if test="req.createTimeEnd != null and req.createTimeEnd != ''">
            and ts.create_time &lt;= #{req.createTimeEnd}
        </if>
        <if test="req.shopType != null">
            and ts.shop_type = #{req.shopType}
        </if>
        <if test="req.mergeType != null">
            and ts.merge_type = #{req.mergeType}
        </if>
        <if test="req.modifyStatus != null">
            <if test="req.modifyStatus ==99">
                and tsmr.status IS NULL
            </if>
            <if test="req.modifyStatus !=99">
                and tsmr.status = #{req.modifyStatus}
            </if>
        </if>
        <if test="req.province != null and req.province != '' ">
            and ts.province = #{req.province}
        </if>
        <if test="req.city != null and req.city != '' ">
            and ts.city = #{req.city}
        </if>
        <if test="req.district != null and req.district != '' ">
            and ts.district = #{req.district}
        </if>
        <if test="req.contractCode != null and req.contractCode != '' ">
            and tsc.contract_code = #{req.contractCode}
        </if>
        <if test="req.contractType != null and req.contractType != '' ">
            and tsc.contract_type = #{req.contractType}
        </if>
        order by ts.create_time desc

    </select>

    <select id="selectTerminalShopModifyListByIdListAdmin2024" resultType="com.intelliquor.cloud.shop.common.model.TerminalShopModifyModel">
        select
        ts.id,
        ts.shop_name,
        ts.shop_type,
        ts.address,
        ts.leader_name,
        ts.leader_phone,
        ts.main_code,
        ts.deputy_code,
        ts.status,
        ts.is_delete,
        ts.create_user,
        ts.create_time,
        ts.company_id,
        ts.member_shop_id,
        tsc.contract_type,
        tsc.contract_code,
        tam.name as managerOrStaffName,
        cdi.id as relationDealerId,
        ts.is_high_member,
        ts.merge_type,
        ts.merge_id
        from
        t_terminal_shop ts
        left join
        t_member_shop ms on ts.member_shop_id = ms.id
        left join
        t_terminal_shop_contract tsc on tsc.terminal_shop_id = ts.id
        left join
        t_cloud_dealer_info cdi on cdi.dealer_code = ms.dealer_code
        left join
        t_terminal_account_manager tam on ts.create_user = tam.id
        where
        ts.is_delete = 0
        and ts.status = 1
        and ts.company_id = #{req.companyId}
        and cdi.id is not null
        <if test="req.modifyType != null">
            and ts.id not in (
            select terminal_shop_id from t_terminal_shop_modify_record where modify_type =#{req.modifyType} and status in (1,2)
            )
        </if>
        <if test="idList != null and idList.size > 0">
            and ts.id in
            <foreach collection="idList" separator="," open="(" close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="managerIdList != null and managerIdList.size > 0">
            and ts.create_user in
            <foreach collection="managerIdList" item="managerId" separator="," close=")" open="(">
                #{managerId}
            </foreach>
        </if>
        <if test="req.shopName != null and req.shopName != ''">
            and ts.shop_name like concat('%',#{req.shopName},'%')
        </if>
        <if test="req.createTimeStart != null and req.createTimeStart != ''">
            and ts.create_time &gt;= #{req.createTimeStart}
        </if>
        <if test="req.createTimeEnd != null and req.createTimeEnd != ''">
            and ts.create_time &lt;= #{req.createTimeEnd}
        </if>
        <if test="req.shopType != null">
            and ts.shop_type = #{req.shopType}
        </if>
        <if test="req.mergeType != null">
            and ts.merge_type = #{req.mergeType}
        </if>
        <if test="req.modifyStatus != null">
            <if test="req.modifyStatus ==99">
                and tsmr.status IS NULL
            </if>
            <if test="req.modifyStatus !=99">
                and tsmr.status = #{req.modifyStatus}
            </if>
        </if>
        <if test="req.province != null and req.province != '' ">
            and ts.province = #{req.province}
        </if>
        <if test="req.city != null and req.city != '' ">
            and ts.city = #{req.city}
        </if>
        <if test="req.district != null and req.district != '' ">
            and ts.district = #{req.district}
        </if>
        <if test="req.contractCode != null and req.contractCode != '' ">
            and tsc.contract_code = #{req.contractCode}
        </if>
        <if test="req.contractType != null and req.contractType != '' ">
            and tsc.contract_type = #{req.contractType}
        </if>
        order by ts.create_time desc

    </select>

    <select id="getTerminalShopPage" resultType="com.intelliquor.cloud.shop.common.model.TerminalShopModifyModel">
        select t.* from (SELECT
        ts.id,
        ts.shop_name,
        ts.shop_type,
        ts.address,
        ts.leader_name,
        ts.leader_phone,
        ts.main_code,
        ts.deputy_code,
        ts.status,
        ts.is_delete,
        ts.create_user,
        ts.create_time,
        ts.company_id,
        ts.member_shop_id,
        tsl.level_name,
        tsc.contract_type,
        tsc.contract_code,
        cdi.id as relationDealerId,
        ts.is_high_member,
        cdib.dealer_name as distributorName,
        cdib.dealer_code as distributorCode,
        cdib.id as distributorId,
        cdiba.dealer_code as dealerCode,
        cdiba.dealer_name as dealerName,
        (select count(1) from t_terminal_parent_update_record tpur where tpur.shop_id = ts.id) as parentUpdateCount
        FROM
        t_terminal_shop ts
        LEFT JOIN t_member_shop ms ON ts.member_shop_id = ms.id
        LEFT JOIN t_terminal_shop_contract tsc ON tsc.terminal_shop_id = ts.id
        LEFT JOIN t_cloud_dealer_info cdi ON cdi.dealer_code = ms.dealer_code
        LEFT JOIN t_cloud_dealer_relation cdr ON cdr.dealer_id = cdi.id
        LEFT JOIN t_cloud_dealer_info cdib ON cdib.id = cdr.parent_id
        LEFT JOIN t_cloud_dealer_relation cdra ON cdra.dealer_id = cdib.id
        LEFT JOIN t_cloud_dealer_info cdiba ON cdiba.id = cdra.parent_id
        LEFT JOIN t_terminal_shop_level tsl ON ts.level_code = tsl.id
        WHERE
        ts.is_delete = 0
        AND ts.STATUS = 1
        AND ts.shop_type not in (5,6,7,8)
        AND ts.company_id = #{req.companyId}
        AND cdi.id IS NOT NULL
        AND ts.distributor_id is not null
        <if test="req.modifyType != null">
          and ts.id not in (
            select
            terminal_shop_id from t_terminal_shop_modify_record where modify_type =#{req.modifyType} and
            status in (1,2)
            )
        </if>
            <if test="idList != null and idList.size > 0">
                and ts.id in
                <foreach collection="idList" separator="," open="(" close=")" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="managerIdList != null and managerIdList.size > 0">
                and ts.create_user in
                <foreach collection="managerIdList" item="managerId" separator="," close=")" open="(">
                    #{managerId}
                </foreach>
            </if>
            <if test="req.shopName != null and req.shopName != ''">
                and ts.shop_name like concat('%',#{req.shopName},'%')
            </if>
            <if test="req.createTimeStart != null and req.createTimeStart != ''">
                and ts.create_time &gt;= #{req.createTimeStart}
            </if>
            <if test="req.createTimeEnd != null and req.createTimeEnd != ''">
                and ts.create_time &lt;= #{req.createTimeEnd}
            </if>
            <if test="req.shopType != null">
                and ts.shop_type = #{req.shopType}
            </if>
            <if test="req.province != null and req.province != '' ">
                and ts.province = #{req.province}
            </if>
            <if test="req.city != null and req.city != '' ">
                and ts.city = #{req.city}
            </if>
            <if test="req.district != null and req.district != '' ">
                and ts.district = #{req.district}
            </if>
            <if test="req.contractCode != null and req.contractCode != '' ">
                and tsc.contract_code = #{req.contractCode}
            </if>
            <if test="req.contractType != null and req.contractType != '' ">
                and tsc.contract_type = #{req.contractType}
            </if>
            <if test="req.contractType != null and req.contractType != '' ">
                and tsc.contract_type = #{req.contractType}
            </if>
            <if test="req.distributorId != null and req.distributorId != '' ">
                and ts.distributor_id = #{req.distributorId}
            </if>
            <if test="req.dealerCode != null and req.dealerCode != '' ">
                and cdiba.dealer_code = #{req.dealerCode}
            </if>
            <if test="req.distributorId != null and req.distributorId != '' ">
                and cdib.id = #{req.distributorId}
            </if>
        UNION
        (
            SELECT
                ts.id,
                ts.shop_name,
                ts.shop_type,
                ts.address,
                ts.leader_name,
                ts.leader_phone,
                ts.main_code,
                ts.deputy_code,
                ts.status,
                ts.is_delete,
                ts.create_user,
                ts.create_time,
                ts.company_id,
                ts.member_shop_id,
                tsl.level_name,
                tsc.contract_type,
                tsc.contract_code,
                cdi.id as relationDealerId,
                ts.is_high_member,
                null as distributorName,
                null as distributorCode,
                null as distributorId,
                cdib.dealer_code as dealerCode,
                cdib.dealer_name as dealerName,
                (select count(1) from t_terminal_parent_update_record tpur where tpur.shop_id = ts.id) as parentUpdateCount
            FROM
                t_terminal_shop ts
            LEFT JOIN t_member_shop ms ON ts.member_shop_id = ms.id
            LEFT JOIN t_terminal_shop_contract tsc ON tsc.terminal_shop_id = ts.id
            LEFT JOIN t_cloud_dealer_info cdi ON cdi.dealer_code = ms.dealer_code
            LEFT JOIN t_cloud_dealer_relation cdr ON cdr.dealer_id = cdi.id
            LEFT JOIN t_cloud_dealer_info cdib ON cdib.id = cdr.parent_id
            LEFT JOIN t_terminal_shop_level tsl ON ts.level_code = tsl.id
            WHERE
                ts.is_delete = 0
                AND ts.STATUS = 1
                AND ts.company_id = #{req.companyId}
                AND cdi.id IS NOT NULL
                <if test="req.modifyType != null">
                    and ts.id not in (
                    select terminal_shop_id from t_terminal_shop_modify_record where modify_type =#{req.modifyType} and
                    status in (1,2)
                    )
                </if>
                <if test="idList != null and idList.size > 0">
                    and ts.id in
                    <foreach collection="idList" separator="," open="(" close=")" item="id">
                        #{id}
                    </foreach>
                </if>
                <if test="managerIdList != null and managerIdList.size > 0">
                    and ts.create_user in
                    <foreach collection="managerIdList" item="managerId" separator="," close=")" open="(">
                        #{managerId}
                    </foreach>
                </if>
                <if test="req.shopName != null and req.shopName != ''">
                    and ts.shop_name like concat('%',#{req.shopName},'%')
                </if>
                <if test="req.createTimeStart != null and req.createTimeStart != ''">
                    and ts.create_time &gt;= #{req.createTimeStart}
                </if>
                <if test="req.createTimeEnd != null and req.createTimeEnd != ''">
                    and ts.create_time &lt;= #{req.createTimeEnd}
                </if>
                <if test="req.shopType != null">
                    and ts.shop_type = #{req.shopType}
                </if>
                <if test="req.province != null and req.province != '' ">
                    and ts.province = #{req.province}
                </if>
                <if test="req.city != null and req.city != '' ">
                    and ts.city = #{req.city}
                </if>
                <if test="req.district != null and req.district != '' ">
                    and ts.district = #{req.district}
                </if>
                <if test="req.contractCode != null and req.contractCode != '' ">
                    and tsc.contract_code = #{req.contractCode}
                </if>
                <if test="req.contractType != null and req.contractType != '' ">
                    and tsc.contract_type = #{req.contractType}
                </if>
                <if test="req.contractType != null and req.contractType != '' ">
                    and tsc.contract_type = #{req.contractType}
                </if>
                <if test="req.distributorId != null and req.distributorId != '' ">
                    and ts.distributor_id = #{req.distributorId}
                </if>
                <if test="req.dealerCode != null and req.dealerCode != '' ">
                    and cdib.dealer_code = #{req.dealerCode}
                </if>
                <if test="req.distributorId != null and req.distributorId != '' ">
                    and ts.distributor_id = #{req.distributorId}
                </if>
             )
        ) t order by t.create_time desc
    </select>


    <select id="getTerminalShopPage2" resultType="com.intelliquor.cloud.shop.common.model.TerminalShopModifyModel">
        select t.* from (
        SELECT
        ts.id,
        ts.shop_name,
        ts.shop_type,
        ts.address,
        ts.leader_name,
        ts.leader_phone,
        ts.main_code,
        ts.deputy_code,
        ts.status,
        ts.is_delete,
        ts.create_user,
        ts.create_time,
        ts.company_id,
        ts.member_shop_id,
        tsl.level_name,
        tsc.contract_type,
        tsc.contract_code,
        cdi.id as relationDealerId,
        ts.is_high_member,
        null as distributorName,
        null as distributorCode,
        null as distributorId,
        cdib.dealer_code as dealerCode,
        cdib.dealer_name as dealerName,
        (select count(1) from t_terminal_parent_update_record tpur where tpur.shop_id = ts.id) as parentUpdateCount
        FROM
        t_terminal_shop ts
        LEFT JOIN t_member_shop ms ON ts.member_shop_id = ms.id
        LEFT JOIN t_terminal_shop_contract tsc ON tsc.terminal_shop_id = ts.id
        LEFT JOIN t_cloud_dealer_info cdi ON cdi.dealer_code = ms.dealer_code
        LEFT JOIN t_cloud_dealer_relation cdr ON cdr.dealer_id = cdi.id
        LEFT JOIN t_cloud_dealer_info cdib ON cdib.id = cdr.parent_id
        LEFT JOIN t_terminal_shop_level tsl ON ts.level_code = tsl.id
        WHERE
        ts.is_delete = 0
        AND ts.STATUS = 1
        AND ts.company_id = #{req.companyId}
        AND cdi.id IS NOT NULL
        AND (ts.distributor_id = 0 OR ts.distributor_id IS NULL)
        <if test="idList != null and idList.size > 0">
            and ts.id in
            <foreach collection="idList" separator="," open="(" close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="managerIdList != null and managerIdList.size > 0">
            and ts.create_user in
            <foreach collection="managerIdList" item="managerId" separator="," close=")" open="(">
                #{managerId}
            </foreach>
        </if>
        <if test="req.shopName != null and req.shopName != ''">
            and ts.shop_name like concat('%',#{req.shopName},'%')
        </if>
        <if test="req.contractCode != null and req.contractCode != '' ">
            and tsc.contract_code = #{req.contractCode}
        </if>
        <if test="req.dealerCode != null and req.dealerCode != '' ">
            and cdib.dealer_code = #{req.dealerCode}
        </if>
        ) t order by t.create_time desc
    </select>


    <select id="selectTerminalShopModifyById" resultType="com.intelliquor.cloud.shop.common.model.TerminalShopModifyModel">
        select
        ts.id,
        ts.shop_name,
        ts.shop_type,
        ts.address,
        ts.leader_name,
        ts.leader_phone,
        ts.main_code,
        ts.deputy_code,
        ts.status,
        ts.is_delete,
        ts.create_user,
        ts.create_time,
        ts.company_id,
        ts.member_shop_id,
        c.level_name,
        tsc.contract_type,
        tsc.contract_code,
        tam.name as managerOrStaffName,
        cdi.id as relationDealerId,
        ts.is_high_member
        from
        t_terminal_shop ts
        left join
        t_member_shop ms on ts.member_shop_id = ms.id
        left join
        t_terminal_shop_contract tsc on tsc.terminal_shop_id = ts.id
        left join
        t_cloud_dealer_info cdi on cdi.dealer_code = ms.dealer_code
        left join
        t_terminal_account_manager tam on ts.create_user = tam.id
        left join
        t_terminal_shop_level_config c on ts.level_code = c.level_code
        where
        ts.id = #{id}
        and
        ts.status = 1
        and
        ts.is_delete = 0
        and
        ts.member_shop_id !=0
        and
        ts.member_shop_id is not null
        and
        cdi.id is not null
    </select>


    <select id="selectShopModifyByMemberShopId" resultType="com.intelliquor.cloud.shop.common.model.TerminalShopModifyModel">
        select
            ms.id as memberShopId,
            ms.name as shop_name,
            ms.main_code,
            ms.dealer_code as deputyCode,
            tid.contract_type,
            tid.contract_no as contract_code,
            cdi.id as relationDealerId,
            cdi.dealer_code,
            cdi.account_type,
            ms.is_member as is_high_member,
            ms.linkman,
            ms.linkphone
        from
            t_member_shop ms
        left join
            t_cloud_dealer_info_detail tid on tid.dealer_code = ms.dealer_code
        left join
            t_cloud_dealer_info cdi on cdi.dealer_code = ms.dealer_code
        where
            ms.id = #{memberShopId}
    </select>

    <select id="selectMemberType" resultType="com.intelliquor.cloud.shop.common.model.TerminalShopModifyModel">
        select
            ms.id AS memberShopId,
            ms.`name` AS shopName,
            ms.dealer_code AS deputyCode,
            ms.main_code,
            ms.is_member,
            cdi.account_type,
            cdii.phone as leaderPhone
        from t_member_shop ms
        left JOIN t_cloud_dealer_info cdi ON ms.dealer_code = cdi.dealer_code
        left JOIN  t_cloud_dealer_relation dr on cdi.id = dr.dealer_id
        left JOIN t_cloud_dealer_info cdii on cdii.id = dr.parent_id
        where ms.id = #{memberShopId}

    </select>


    <select id="selectSuperType" resultType="com.intelliquor.cloud.shop.common.model.TerminalShopModifyModel">
        select cdii.account_type,cdii.dealer_code,cdii.dealer_name as shopName
        FROM
            t_member_shop ms
        left join
            t_cloud_dealer_info cdi on cdi.dealer_code = ms.dealer_code
        left join
            t_cloud_dealer_relation dr on cdi.id = dr.dealer_id
        left join
            t_cloud_dealer_info cdii on cdii.id = dr.parent_id
        where  ms.id =  #{memberShopId}
    </select>


    <select id="selectTerimianlShopModifyByMemberShopId" resultType="com.intelliquor.cloud.shop.common.model.TerminalShopModifyModel">
        select
            ms.id as memberShopId,
            ms.name as shop_name,
            ms.main_code,
            ms.dealer_code as deputyCode,
            tsc.contract_type,
            tsc.contract_code,
            tsc.terminal_shop_id as id,
            cdi.id as relationDealerId,
            cdi.dealer_code,
            cdi.account_type,
            ms.is_member,
            ms.linkman,
            ms.linkphone
        from
            t_member_shop ms
        left join
            t_terminal_shop_contract tsc on tsc.member_shop_id = ms.id
        left join
            t_cloud_dealer_info cdi on cdi.dealer_code = ms.dealer_code
        where
            ms.id = #{memberShopId}
    </select>

    <select id="selectPartnerShopModifyByMemberShopId" resultType="com.intelliquor.cloud.shop.common.model.TerminalShopModifyModel">
        select
            ms.id as memberShopId,
            ms.name as shop_name,
            ms.main_code,
            ms.dealer_code as deputyCode,
            cdi.contract_type,
            cdi.contract_code,
            cdi.id as relationDealerId,
            cdi.dealer_code,
            cdi.account_type,
            ms.is_member,
            ms.linkman,
            ms.linkphone
        from
            t_member_shop ms
        left join
            t_cloud_dealer_info cdi on cdi.dealer_code = ms.dealer_code
        where
            ms.id = #{memberShopId}
--           and
--             ms.status = 0
--           and
--             cdi.id is not null
    </select>

    <select id="selectChildShopModifyById" resultType="com.intelliquor.cloud.shop.common.model.TerminalShopModifyModel">
        select
            ms.id as memberShopId,
            ms.name as shop_name,
            ms.main_code,
            ms.dealer_code as deputyCode,
            tsc.contract_type,
            tsc.contract_code,
            tsc.terminal_shop_id as id,
            cdi.id as relationDealerId,
            cdi.dealer_code,
            ms.is_member as is_high_member,
            ms.linkman,
            ms.linkphone
        from
            t_member_shop ms
        left join t_cloud_dealer_info cdi ON ms.dealer_code = cdi.dealer_code
        left join t_cloud_dealer_relation r on cdi.id = r.dealer_id
        left join
            t_terminal_shop_contract tsc on tsc.member_shop_id = ms.id
        where
         r.parent_id = #{relationDealerId}  and tsc.terminal_shop_id is not null and tsc.contract_code is not null
    </select>

    <select id="selectTerminalShopListById" resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalShopResp">
        select ts.id,
        ts.shop_name,
        ts.leader_name,
        ts.leader_phone,
        ts.tag,
        ts.head_img,
        ts.remark,
        ts.province,
        ts.city,
        ts.district,
        ts.address,
        ts.longitude,
        ts.latitude,
        ts.type,
        ts.is_image,
        ts.keeper_name,
        ts.keeper_phone,
        ts.shop_area,
        ts.license_img,
        ts.license_code,
        tsis.status,
        ts.is_delete,
        ts.create_time,
        ts.create_user,
        ts.update_time,
        ts.company_id,
        ts.member_shop_id,
        ts.level_code,
        ts.level_code as levelName,
        ts.shop_type  as shopType,
        ts.is_high_member,
        tam.name                             as managerOrStaffName,
        tam.phone                            as managerOrStaffPhone,
        ts.terminal_divide_id,
        IFNULL(tamm.name, tammm.name)        as accountManagerName,
        IFNULL(tamm.phone, tammm.phone)      as accountManagerPhone,
        ms.virtual_amount,
        ts.merge_type,
        ts.merge_id,
        ms.status as terminalStatus,
        ts.is_prepare
        from t_terminal_shop ts
        left join
        t_member_shop ms
        on
        ts.member_shop_id = ms.id
        left join
        t_terminal_shop_level_config tslc
        on
        ms.level_code = tslc.level_code
        left join
        t_terminal_shop_info_schedule tsis
        on
        tsis.terminal_shop_id = ts.id
        left join t_terminal_account_manager tam on ts.create_user = tam.id
        left join t_terminal_account_manager tamDivide on ts.terminal_divide_id = tamDivide.id
        left join t_terminal_account_manager tamm on tamm.id = tamDivide.parent_id
        left join t_terminal_account_manager tammm on tam.parent_id = tammm.id
        where ts.shop_type not in (5,10,11,12,13)
        <if test="listType != null">
            <if test="listType==0">
                and tsis.status !=1
            </if>
            <if test="listType==1">
                and tsis.status = 1
            </if>
        </if>
        <if test="terminalShopManageReq.terminalDivideId == null or terminalShopManageReq.terminalDivideId == ''">
            and ts.create_user = #{terminalShopManageReq.id}
        </if>
        <if test="terminalShopManageReq.terminalDivideId != null and terminalShopManageReq.terminalDivideId != ''">
            and (
            ts.create_user = #{terminalShopManageReq.id}
            or
            ts.terminal_divide_id = #{terminalShopManageReq.terminalDivideId}
            )
        </if>
        and ts.is_delete = #{terminalShopManageReq.isDelete}
        <if test="terminalShopManageReq.shopName != null and terminalShopManageReq.shopName != ''">
            and ts.shop_name like concat('%', #{terminalShopManageReq.shopName}, '%')
        </if>
        <if test="terminalShopManageReq.isHighMember != null ">
            and ts.is_high_member = #{terminalShopManageReq.isHighMember}
        </if>
        <if test="terminalShopManageReq.tag != null">
            and ts.tag = #{terminalShopManageReq.tag}
        </if>

        <if test="terminalShopManageReq.gatherMonth != null and terminalShopManageReq.gatherMonth != ''">
            and ts.create_time &gt;= #{terminalShopManageReq.gatherMonthStartTime}
            and ts.create_time &lt;= #{terminalShopManageReq.gatherMonthEndTime}
        </if>

        <if test="terminalShopManageReq.status != null">
            <choose>
                <when test="terminalShopManageReq.status == 98">
                    and tsis.status in (2, 4)
                </when>
                <when test="terminalShopManageReq.status == 99">
                    and tsis.status in (3, 5)
                </when>
                <!--
                <otherwise>
                    and tsis.status = #{terminalShopManageReq.status}
                </otherwise>-->
            </choose>
        </if>

        <if test="terminalShopManageReq.shopType != null">
            and ts.shop_type = #{terminalShopManageReq.shopType}
        </if>

        <if test="terminalShopManageReq.level != null and terminalShopManageReq.level != '' and terminalShopManageReq.level != 6">
            and ms.level_code = #{terminalShopManageReq.level}
        </if>

        <if test="terminalShopManageReq.level != null and terminalShopManageReq.level != '' and terminalShopManageReq.level == 6">
            and (
            ms.level_code = 0
            or
            ms.level_code is null
            )
        </if>

        <if test="terminalShopManageReq.terminalStatus != null">
            and ms.status = #{terminalShopManageReq.terminalStatus} and ts.merge_type &lt; 2
        </if>

        <if test="terminalShopManageReq.searchText != null and terminalShopManageReq.searchText != ''">
            and (ts.shop_name like concat('%', #{terminalShopManageReq.searchText}, '%')
            or ts.leader_name like concat('%', #{terminalShopManageReq.searchText}, '%')
            or ts.leader_phone like concat('%', #{terminalShopManageReq.searchText}, '%')
            )
        </if>

        <if test="terminalShopManageReq.sort != null and terminalShopManageReq.sort == 0">
            order by ts.create_time desc
        </if>

        <if test="terminalShopManageReq.sort != null and terminalShopManageReq.sort == 1">
            order by ts.create_time asc
        </if>
    </select>

    <select id="selectHotelTerminalShopListById" resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalShopResp">
        select ts.id,
        ts.shop_name,
        ts.leader_name,
        ts.leader_phone,
        ts.tag,
        ts.head_img,
        ts.remark,
        ts.province,
        ts.city,
        ts.district,
        ts.address,
        ts.longitude,
        ts.latitude,
        ts.type,
        ts.is_image,
        ts.keeper_name,
        ts.keeper_phone,
        ts.shop_area,
        ts.license_img,
        ts.license_code,
        tsis.status,
        ts.is_delete,
        ts.create_time,
        ts.create_user,
        ts.update_time,
        ts.company_id,
        ts.member_shop_id,
        ts.level_code,
        ts.level_code as levelName,
        ts.shop_type  as shopType,
        ts.is_high_member,
        tam.name                             as managerOrStaffName,
        tam.phone                            as managerOrStaffPhone,
        ts.terminal_divide_id,
        IFNULL(tamm.name, tammm.name)        as accountManagerName,
        IFNULL(tamm.phone, tammm.phone)      as accountManagerPhone,
        ms.virtual_amount,
        ts.merge_type,
        ts.merge_id,
        ms.status as terminalStatus,
        ts.is_prepare
        from t_terminal_shop ts
        left join
        t_member_shop ms
        on
        ts.member_shop_id = ms.id
        left join
        t_terminal_shop_level_config tslc
        on
        ms.level_code = tslc.level_code
        left join
        t_terminal_shop_info_schedule tsis
        on
        tsis.terminal_shop_id = ts.id
        left join t_terminal_account_manager tam on ts.create_user = tam.id
        left join t_terminal_account_manager tamDivide on ts.terminal_divide_id = tamDivide.id
        left join t_terminal_account_manager tamm on tamm.id = tamDivide.parent_id
        left join t_terminal_account_manager tammm on tam.parent_id = tammm.id
        where ts.shop_type in (10,11,12,13)
        <if test="terminalShopManageReq.terminalDivideId == null or terminalShopManageReq.terminalDivideId == ''">
            and ts.create_user = #{terminalShopManageReq.id}
        </if>
        <if test="terminalShopManageReq.terminalDivideId != null and terminalShopManageReq.terminalDivideId != ''">
            and (
            ts.create_user = #{terminalShopManageReq.id}
            or
            ts.terminal_divide_id = #{terminalShopManageReq.terminalDivideId}
            )
        </if>
        and ts.is_delete = #{terminalShopManageReq.isDelete}
        <if test="terminalShopManageReq.shopName != null and terminalShopManageReq.shopName != ''">
            and ts.shop_name like concat('%', #{terminalShopManageReq.shopName}, '%')
        </if>
        <if test="terminalShopManageReq.isHighMember != null ">
            and ts.is_high_member = #{terminalShopManageReq.isHighMember}
        </if>
        <if test="terminalShopManageReq.tag != null">
            and ts.tag = #{terminalShopManageReq.tag}
        </if>

        <if test="terminalShopManageReq.gatherMonth != null and terminalShopManageReq.gatherMonth != ''">
            and ts.create_time &gt;= #{terminalShopManageReq.gatherMonthStartTime}
            and ts.create_time &lt;= #{terminalShopManageReq.gatherMonthEndTime}
        </if>

        <if test="terminalShopManageReq.status != null">
            <choose>
                <when test="terminalShopManageReq.status == 98">
                    and tsis.status in (2, 4)
                </when>
                <when test="terminalShopManageReq.status == 99">
                    and tsis.status in (3, 5)
                </when>
                <otherwise>
                    and tsis.status = #{terminalShopManageReq.status}
                </otherwise>
            </choose>
        </if>

        <if test="terminalShopManageReq.shopType != null">
            and ts.shop_type = #{terminalShopManageReq.shopType}
        </if>

        <if test="terminalShopManageReq.level != null and terminalShopManageReq.level != '' and terminalShopManageReq.level != 6">
            and ms.level_code = #{terminalShopManageReq.level}
        </if>

        <if test="terminalShopManageReq.level != null and terminalShopManageReq.level != '' and terminalShopManageReq.level == 6">
            and (
            ms.level_code = 0
            or
            ms.level_code is null
            )
        </if>

        <if test="terminalShopManageReq.terminalStatus != null">
            and ms.status = #{terminalShopManageReq.terminalStatus} and ts.merge_type &lt; 2
        </if>

        <if test="terminalShopManageReq.searchText != null and terminalShopManageReq.searchText != ''">
            and (ts.shop_name like concat('%', #{terminalShopManageReq.searchText}, '%')
            or ts.leader_name like concat('%', #{terminalShopManageReq.searchText}, '%')
            or ts.leader_phone like concat('%', #{terminalShopManageReq.searchText}, '%')
            )
        </if>

        <if test="terminalShopManageReq.sort != null and terminalShopManageReq.sort == 0">
            order by ts.create_time desc
        </if>

        <if test="terminalShopManageReq.sort != null and terminalShopManageReq.sort == 1">
            order by ts.create_time asc
        </if>
    </select>

    <select id="selectTerminalShopListByAgentRole" resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalShopAgentResp">
        select DISTINCT
        ts.id,
        ts.shop_name,
        ts.leader_name,
        ts.leader_phone,
        ts.tag,
        ts.head_img,
        ts.remark,
        ts.province,
        ts.city,
        ts.district,
        ts.address,
        ts.longitude,
        ts.latitude,
        ts.type,
        ts.is_image,
        ts.keeper_name,
        ts.keeper_phone,
        ts.shop_area,
        ts.license_img,
        ts.license_code,
<!--        tsis.status,-->
        --         由于原来逻辑中终端审核状态是从t_terminal_shop_info_schedule表中获取status为1的为激活，现在修改为t_terminal_shop表中的status+is_prepare为正式终端判断。所以判断满足条件的为1，不满足条件的为0，可以不用修改小程序并提交审核
        if((ts.status = 1 and ts.is_prepare = 0), 1, 0) as status,
        ts.is_delete,
        ts.create_time,
        ts.create_user,
        ts.update_time,
        ts.company_id,
        ts.member_shop_id,
        ts.level_code,
        ts.level_code as levelName,
        ts.shop_type  as shopType,
        ts.is_high_member,
        tam.name as managerOrStaffName
        from
        t_terminal_task_receive tttr
        left join t_terminal_task_yearly ttty on
        tttr.tp_id = ttty.tp_id and tttr.status =0
        left join t_terminal_task_yearly_detail tttyd on
        tttyd.ty_id = ttty.id
        left join t_member_shop ms on
        ms.id = tttyd.ms_id
        left join t_terminal_shop ts on
        ts.member_shop_id = ms.id and ts.status = 1
        left join
        t_terminal_account_manager tam on ts.create_user = tam.id
<!--    与智赢沟通，终端审核状态原来从t_terminal_shop_info_schedule表中获取，现在修改为t_terminal_shop表中的status+is_prepare为正式终端判断-->
<!--        left join t_terminal_shop_info_schedule tsis on-->
<!--        tsis.terminal_shop_id = ts.id and tsis.is_delete = 0-->
        where tttr.broker_id = #{terminalShopManageAgentReq.id} and tttr.delete_flag = 0
        and  ttty.delete_flag = 0 and  tttyd.delete_flag = 0
        and ms.status = 0
        and ts.is_delete = 0 and ts.shop_type != 5
        <if test="terminalShopManageAgentReq.shopName != null and terminalShopManageAgentReq.shopName != ''">
            and ts.shop_name like concat('%', #{terminalShopManageAgentReq.shopName}, '%')
        </if>

        <if test="terminalShopManageAgentReq.tag != null">
            and ts.tag = #{terminalShopManageAgentReq.tag}
        </if>

        <if test="terminalShopManageAgentReq.gatherMonth != null and terminalShopManageAgentReq.gatherMonth != ''">
            and ts.create_time &gt;= #{terminalShopManageAgentReq.gatherMonthStartTime}
            and ts.create_time &lt;= #{terminalShopManageAgentReq.gatherMonthEndTime}
        </if>

        <if test="terminalShopManageAgentReq.shopType != null">
            and ts.shop_type = #{terminalShopManageAgentReq.shopType}
        </if>

        <if test="terminalShopManageAgentReq.sort != null and terminalShopManageAgentReq.sort == 0">
            order by ts.create_time desc
        </if>

        <if test="terminalShopManageAgentReq.sort != null and terminalShopManageAgentReq.sort == 1">
            order by ts.create_time asc
        </if>
    </select>

    <select id="selectTerminalShopListByDealerId" resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalShopResp">
        select
        ts.id,
        ts.shop_name,
        ts.leader_name,
        ts.leader_phone,
        ts.tag,
        ts.head_img,
        ts.remark,
        ts.province,
        ts.city,
        ts.district,
        ts.address,
        ts.longitude,
        ts.latitude,
        ts.type,
        ts.is_image,
        ts.keeper_name,
        ts.keeper_phone,
        ts.shop_area,
        ts.license_img,
        ts.license_code,
        ts.status,
        ts.is_delete,
        ts.create_time,
        ts.create_user,
        ts.update_time,
        ts.company_id,
        ts.member_shop_id,
        ts.level_code,
        ts.level_code as levelName,
        ts.is_high_member,
        tam.name                             as managerOrStaffName,
        tam.phone                            as managerOrStaffPhone,
        ts.terminal_divide_id,
        IFNULL(tamm.name, tammm.name)        as accountManagerName,
        IFNULL(tamm.phone, tammm.phone)      as accountManagerPhone,
        ms.virtual_amount,
        ts.merge_type,
        ts.merge_id,
        ms.status as terminalStatus,
        ts.is_prepare
        from
        t_cloud_dealer_info cdi
        LEFT JOIN t_cloud_dealer_relation cdr ON cdr.parent_id = cdi.id
        LEFT JOIN t_cloud_dealer_info cdi2 ON cdr.dealer_id = cdi2.id
        LEFT JOIN t_member_shop ms ON cdi2.dealer_code = ms.dealer_code
        RIGHT JOIN t_terminal_shop ts ON ts.member_shop_id = ms.id
        left join t_terminal_account_manager tam on ts.create_user = tam.id
        left join t_terminal_account_manager tamDivide on ts.terminal_divide_id = tamDivide.id
        left join t_terminal_account_manager tamm on tamm.id = tamDivide.parent_id
        left join t_terminal_account_manager tammm on tam.parent_id = tammm.id
        left join t_terminal_shop_info_schedule tsis on ts.id = tsis.terminal_shop_id
        WHERE
        cdi.id = 35778
        and ts.shop_type not in (10,11,12,13)
        <if test="listType != null">
            <if test="listType==0">
                and ((tsis.status = 0)
                or  (ts.is_prepare= 1))
            </if>
            <if test="listType==1">
                and tsis.status = 1
            </if>
        </if>
        <if test="terminalShopManageReq.terminalDivideId != null and terminalShopManageReq.terminalDivideId != ''">
            and (
            ts.create_user = #{terminalShopManageReq.id}
            or
            ts.terminal_divide_id = #{terminalShopManageReq.terminalDivideId}
            )
        </if>
        and
        ts.is_delete = #{terminalShopManageReq.isDelete}
        <if test="terminalShopManageReq.shopName != null and terminalShopManageReq.shopName != ''">
            and ts.shop_name like concat('%',#{terminalShopManageReq.shopName},'%')
        </if>
        <if test="terminalShopManageReq.isHighMember != null ">
            and ts.is_high_member = #{terminalShopManageReq.isHighMember}
        </if>

        <if test="terminalShopManageReq.tag != null">
            and ts.tag = #{terminalShopManageReq.tag}
        </if>

        <if test="terminalShopManageReq.gatherMonth != null and terminalShopManageReq.gatherMonth != ''">
            and ts.create_time &gt;= #{terminalShopManageReq.gatherMonthStartTime}
            and ts.create_time &lt;= #{terminalShopManageReq.gatherMonthEndTime}
        </if>

        <if test="terminalShopManageReq.status != null">
            and tsis.status = #{terminalShopManageReq.status}
        </if>

        <if test="terminalShopManageReq.level != null and terminalShopManageReq.level != '' and terminalShopManageReq.level != 6">
            and ms.level_code = #{terminalShopManageReq.level}
        </if>

        <if test="terminalShopManageReq.level != null and terminalShopManageReq.level != '' and terminalShopManageReq.level == 6">
            and (
            ms.level_code = 0
            or
            ms.level_code is null
            )
        </if>

        <if test="terminalShopManageReq.terminalStatus != null">
            and ms.status = #{terminalShopManageReq.terminalStatus} and ts.merge_type &lt; 2
        </if>

        <if test="terminalShopManageReq.searchText != null and terminalShopManageReq.searchText != ''">
            and (ts.shop_name like concat('%', #{terminalShopManageReq.searchText}, '%')
            or ts.leader_name like concat('%', #{terminalShopManageReq.searchText}, '%')
            or ts.leader_phone like concat('%', #{terminalShopManageReq.searchText}, '%')
            )
        </if>

        <if test="terminalShopManageReq.sort != null and terminalShopManageReq.sort == 0">
            order by ts.create_time desc
        </if>

        <if test="terminalShopManageReq.sort != null and terminalShopManageReq.sort == 1">
            order by ts.create_time asc
        </if>
    </select>

    <select id="selectTerminalShopListByDealerId2" resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalShopResp">
        select
        ts.id,
        ts.shop_name,
        ts.leader_name,
        ts.leader_phone,
        ts.tag,
        ts.head_img,
        ts.remark,
        ts.province,
        ts.city,
        ts.district,
        ts.address,
        ts.longitude,
        ts.latitude,
        ts.type,
        ts.is_image,
        ts.keeper_name,
        ts.keeper_phone,
        ts.shop_area,
        ts.license_img,
        ts.license_code,
        ts.status,
        ts.is_delete,
        ts.create_time,
        ts.create_user,
        ts.update_time,
        ts.company_id,
        ts.member_shop_id,
        ts.level_code,
        ts.level_code as levelName,
        ts.is_high_member,
        tam.name                             as managerOrStaffName,
        tam.phone                            as managerOrStaffPhone,
        ts.terminal_divide_id,
        IFNULL(tamm.name, tammm.name)        as accountManagerName,
        IFNULL(tamm.phone, tammm.phone)      as accountManagerPhone,
        ms.virtual_amount,
        ts.merge_type,
        ts.merge_id,
        ms.status as terminalStatus,
        ts.is_prepare
        from
        t_cloud_dealer_info cdi
        LEFT JOIN t_cloud_dealer_relation cdr ON cdr.parent_id = cdi.id
        LEFT JOIN t_cloud_dealer_info cdi2 ON cdr.dealer_id = cdi2.id
        LEFT JOIN t_member_shop ms ON cdi2.dealer_code = ms.dealer_code
        RIGHT JOIN t_terminal_shop ts ON ts.member_shop_id = ms.id
        left join t_terminal_account_manager tam on ts.create_user = tam.id
        left join t_terminal_account_manager tamDivide on ts.terminal_divide_id = tamDivide.id
        left join t_terminal_account_manager tamm on tamm.id = tamDivide.parent_id
        left join t_terminal_account_manager tammm on tam.parent_id = tammm.id
        left join t_terminal_shop_info_schedule tsis on ts.id = tsis.terminal_shop_id
        WHERE
        cdi.id = 34865
        and ts.shop_type not in (10,11,12,13)
        <if test="listType != null">
            <if test="listType==0">
                and ts.status = 0
            </if>
            <if test="listType==1">
                and ts.status = 1
            </if>
        </if>
        <if test="terminalShopManageReq.terminalDivideId != null and terminalShopManageReq.terminalDivideId != ''">
            and (
            ts.create_user = #{terminalShopManageReq.id}
            or
            ts.terminal_divide_id = #{terminalShopManageReq.terminalDivideId}
            )
        </if>
        and
        ts.is_delete = #{terminalShopManageReq.isDelete}
        <if test="terminalShopManageReq.shopName != null and terminalShopManageReq.shopName != ''">
            and ts.shop_name like concat('%',#{terminalShopManageReq.shopName},'%')
        </if>
        <if test="terminalShopManageReq.isHighMember != null ">
            and ts.is_high_member = #{terminalShopManageReq.isHighMember}
        </if>

        <if test="terminalShopManageReq.tag != null">
            and ts.tag = #{terminalShopManageReq.tag}
        </if>

        <if test="terminalShopManageReq.gatherMonth != null and terminalShopManageReq.gatherMonth != ''">
            and ts.create_time &gt;= #{terminalShopManageReq.gatherMonthStartTime}
            and ts.create_time &lt;= #{terminalShopManageReq.gatherMonthEndTime}
        </if>

        <if test="terminalShopManageReq.status != null">
            and tsis.status = #{terminalShopManageReq.status}
        </if>

        <if test="terminalShopManageReq.level != null and terminalShopManageReq.level != '' and terminalShopManageReq.level != 6">
            and ms.level_code = #{terminalShopManageReq.level}
        </if>

        <if test="terminalShopManageReq.level != null and terminalShopManageReq.level != '' and terminalShopManageReq.level == 6">
            and (
            ms.level_code = 0
            or
            ms.level_code is null
            )
        </if>

        <if test="terminalShopManageReq.terminalStatus != null">
            and ms.status = #{terminalShopManageReq.terminalStatus} and ts.merge_type &lt; 2
        </if>

        <if test="terminalShopManageReq.searchText != null and terminalShopManageReq.searchText != ''">
            and (ts.shop_name like concat('%', #{terminalShopManageReq.searchText}, '%')
            or ts.leader_name like concat('%', #{terminalShopManageReq.searchText}, '%')
            or ts.leader_phone like concat('%', #{terminalShopManageReq.searchText}, '%')
            )
        </if>

        <if test="terminalShopManageReq.sort != null and terminalShopManageReq.sort == 0">
            order by ts.create_time desc
        </if>

        <if test="terminalShopManageReq.sort != null and terminalShopManageReq.sort == 1">
            order by ts.create_time asc
        </if>
    </select>

    <select id="selectTerminalShopById" resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalShopResp">
        select
        ts.id,
        ts.shop_name,
        ts.leader_name,
        ts.leader_phone,
        ts.tag,
        ts.head_img,
        ts.remark,
        ts.province,
        ts.city,
        ts.district,
        ts.address,
        ts.longitude,
        ts.latitude,
        ts.type,
        ts.is_image,
        ts.image_head_picture,
        ts.keeper_name,
        ts.keeper_phone,
        ts.shop_area,
        ts.whether_license,
        ts.license_img,
        ts.license_code,
        ts.company_person,
        ts.company_name,
        ts.license_message,
        tsis.status,
        ts.is_delete,
        ts.create_time,
        ts.create_user,
        ts.update_time,
        ts.company_id,
        ts.member_shop_id,
        ts.shop_type,
        ts.enterprise_name,
        ts.food_business_license,
        ts.whether_proprietary_trading,
        ts.receiving_warehouse_province,
        ts.receiving_warehouse_city,
        ts.receiving_warehouse_district,
        ts.receiving_warehouse_address,
        ts.level_code,
        ts.contact_name,
        ts.contact_level,
        ts.contact_phone,
        ts.receiving_payment_type,
        ts.receiving_payment_name,
        ts.receiving_payment_account,
        ts.receiving_payment_bank,
        ts.receiving_payment_account_picture,
        ts.audit_user_id,
        ts.audit_result,
        ts.shop_agreement,
        ts.gender,
        ts.birthday,
        ts.age,
        ts.work_unit,
        ts.position,
        ts.personal_preference,
        ts.dealer_contact_type,
        ts.first_buy_goods_name,
        ts.first_buy_goods_number,
        ts.first_score,
        ts.registration_form_picture,
        ts.network_point_establish_reason,
        ts.status as activeStatus,
        ts.distributor_id,
        ts.is_prepare,
        ts.is_high_member,
        ts.copartner_id,
        ts.main_code,
        ts.deputy_code,
        ts.address_status,
        ts.protocol_update_status,
        tslc.level_short_name level_name,
        ts.is_branch,
        ts.merge_type,
        ts.merge_id,
        ts.license_result,
        ts.is_license_success,
        ts.food_business_license_code,
        ts.food_license_code
        from
        t_terminal_shop ts
        left join
        t_member_shop ms
        on
        ts.member_shop_id = ms.id
        left join
        t_terminal_shop_level_config tslc
        on
        ms.level_code = tslc.level_code
        left join
        t_terminal_shop_info_schedule tsis
        on
        tsis.terminal_shop_id = ts.id
        where
        ts.id = #{id}
        and
        ts.is_delete = 0
        and
        tsis.is_delete=0
    </select>

    <select id="selectShopIdByCreateUser" resultType="java.lang.Integer">
        select
        ts.id
        from
        t_terminal_shop ts
        left join
        t_terminal_account_manager tam
        on ts.create_user = tam.id
        where
        ts.create_user = #{createUser}
            union
        select
            ts.id
        from
            t_terminal_shop ts
                left join
            t_terminal_account_manager tam
            on ts.create_user = tam.id
        where
        tam.parent_id = #{createUser}
    </select>

    <select id="checkTerminalGtCode" parameterType="String" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM t_cloud_dealer_gt_terminal_apply
        where terminal_code=#{code} or deputy_code=#{code}
    </select>

    <select id="getBelongToById"  resultType="java.lang.String">
        SELECT
        belong_to
        FROM
        `t_terminal_account_manager`
        WHERE
        id=#{id}
    </select>

    <select id="checkTerminalCode" parameterType="String" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM t_terminal_shop
        where main_code=#{code} or deputy_code=#{code}
    </select>

    <select id="restoreTerminalShopById" resultType="java.lang.Integer">
        select
        count(id)
        from
        t_terminal_shop
        where
        shop_name = #{shopName}
        and
        id != #{id}
    </select>

    <select id="selectTerminalGatherNumberByShopId" resultType="java.lang.Integer">
        select
        count(id)
        from
        t_terminal_shop
        where
        id in
        <foreach collection="shopIdList" separator="," item="id" close=")" open="(">
            #{id}
        </foreach>

        <if test="dataCountReq.startTime != null">
            and create_time &gt;= #{dataCountReq.startTime}
        </if>
        <if test="dataCountReq.endTime != null">
            and create_time &lt;= #{dataCountReq.endTime}
        </if>
    </select>

    <select id="selectTerminalActivateNumberByShopId" resultType="java.lang.Integer">
        select
        count(id)
        from
        t_terminal_shop
        where
        id in
        <foreach collection="shopIdList" separator="," item="id" close=")" open="(">
            #{id}
        </foreach>
        and
        status = 1
        <if test="dataCountReq.startTime != null">
            and create_time &gt;= #{dataCountReq.startTime}
        </if>
        <if test="dataCountReq.endTime != null">
            and create_time &lt;= #{dataCountReq.endTime}
        </if>
    </select>

    <select id="selectTerminalScanReceivingNumberByShopId" resultType="java.lang.Integer">
        select
        count(distinct tsd.shop_id)
        from
        t_terminal_scan_detail tsd
        where
        tsd.shop_id is not null
        <if test="dataCountReq.type == 0">
            and
            tsd.shop_id in
            <foreach collection="memberShopIdList" separator="," item="memberShopId" close=")" open="(">
                #{memberShopId}
            </foreach>
        </if>

        <if test="dataCountReq.type != 0">
            and tsd.create_user_id = #{dataCountReq.accountManagerId}
        </if>

        <if test="dataCountReq.startTime != null">
            and tsd.create_time &gt;= #{dataCountReq.startTime}
        </if>
        <if test="dataCountReq.endTime != null">
            and tsd.create_time &lt;= #{dataCountReq.endTime}
        </if>
    </select>

    <select id="selectDealerOutBalanceByDealerId" resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalOutProductResp">
        select
        goods_name as outProductName,
        sum(cdobd.quantity) as outProductCount,
        tscpc.box_bottle_ratio as boxBottleRatio
        from
        t_cloud_dealer_out_balance cdob
        left join
        t_cloud_dealer_out_balance_detail cdobd
        on
        cdob.id = cdobd.balance_id
        left join
        t_terminal_shop_contract_product_config tscpc
        on
        cdobd.goods_code = tscpc.agreement_code
        where
        cdob.from_dealer_code in
        <foreach collection="selectDealerList" item="dealer" open="(" close=")" separator=",">
            #{dealer.dealerCode}
        </foreach>
        and
        cdobd.goods_code in
        <foreach collection="selectProductList" item="product" open="(" close=")" separator=",">
            #{product.agreementCode}
        </foreach>
        <if test="dataCountReq.startTime != null">
            and cdobd.create_time &gt;= #{dataCountReq.startTime}
        </if>
        <if test="dataCountReq.endTime != null">
            and cdobd.create_time &lt;= #{dataCountReq.endTime}
        </if>
        group by cdobd.goods_code
    </select>

    <select id="selectMemberShopIdList" resultType="java.lang.Integer">
        select
        member_shop_id
        from
        t_terminal_shop
        where id in
        <foreach collection="terminalShopIdList" separator="," item="terminalShopId" close=")" open="(">
            #{terminalShopId}
        </foreach>
        and
        member_shop_id != 0
    </select>

    <select id="selectDisplayTerminal" resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalShopDisplayResp">
        select
        tts.id,
        tts.shop_name,
        tts.main_code,
        tts.deputy_code,
        tts.company_id,
        cdi.dealer_code,
        cdi.dealer_name,
        ttsc.contract_type,
        ttsc.contract_code
        from
        t_terminal_shop tts
        left join t_terminal_shop_contract ttsc on ttsc.terminal_shop_id = tts.id
        left join
        t_cloud_dealer_info cdi on ttsc.dealer_code = cdi.dealer_code
        where tts.status = 1 and tts.is_delete=0 and tts.company_id = #{companyId}
        and ttsc.contract_type in (2,3)
        <if test="shopIdList != null and shopIdList.size > 0">
            and tts.id not in
            <foreach collection="shopIdList" separator="," item="id" close=")" open="(">
                #{id}
            </foreach>
        </if>


    </select>
    <select id="selectTerminalDivideList" resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalDivideResp">
        select
        tts.id,
        tts.shop_name,
        tts.head_img,
        ttsc.contract_type,
        ms.level_code as shopLevel,
        tts.leader_name,
        tts.leader_phone,
        tts.create_time,
        tam.name as gatherName,
        tam.phone as gatherPhone,
        tts.address as shopAddress,
        tamDivide.name as accountExecutiveName,
        tamDivide.phone as accountExecutivePhone,
        ms.is_member,
        tamm.name as accountManagerName,
        tamm.phone as accountManagerPhone
        from
        t_terminal_shop tts
        left join t_terminal_shop_contract ttsc on ttsc.terminal_shop_id = tts.id
        left join t_member_shop ms  on tts.member_shop_id = ms.id
        left join t_terminal_account_manager tam on tts.create_user = tam.id
        left join t_terminal_account_manager tamDivide on tts.terminal_divide_id = tamDivide.id
        left join t_terminal_account_manager tamm on tamm.id = tamDivide.parent_id
        left join (select * from t_terminal_shop_merge where status=0) merge on tts.id=merge.from_id
        where
        tts.status = 1
        and
        tts.is_delete = 0
        and tts.id in
        <foreach collection="shopIdList" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>

        <if test="terminalDivideReq.shopName != null and terminalDivideReq.shopName != ''">
            and tts.shop_name like concat('%',#{terminalDivideReq.shopName},'%')
        </if>
        <if test="terminalDivideReq.divideType != null and terminalDivideReq.divideType == 0">
            and tts.terminal_divide_id = 0
        </if>
        <if test="terminalDivideReq.divideType != null and terminalDivideReq.divideType == 1">
            and tts.terminal_divide_id != 0
        </if>
--         and ms.is_member in(0,2)
        and tts.merge_type in (0,1) and merge.from_id is null
        order by tts.id desc
    </select>

    <update id="bindTerminalShopAndAccountExecutive">
        update
        t_terminal_shop
        set terminal_divide_id = #{terminalDivideId}
        where
        id = #{shopId}
    </update>
    <update id="bindTerminalShopScheduleAndAccountExecutive">
        update
            t_terminal_shop_info_schedule
        set terminal_divide_id = #{terminalDivideId}
        where
            terminal_shop_id = #{shopId}
    </update>
    <!-- 中台审批列表的数据接口 -->
    <select id="selectAuditDataList" resultType="com.intelliquor.cloud.shop.terminal.model.resp.GtOpenTerminalShopAuditListResp">
        select
        ts.id,
        ts.shop_name,
        ts.deputy_code,
        ts.main_code,
        ts.tag,
        ts.leader_name,
        ts.leader_phone,
        ts.province,
        ts.city,
        ts.district,
        ts.province+ts.city+ts.district area_name,
        ts.license_code,
        ts.create_time,
        ts.shop_type,
        ts.contact_name,
        ts.contact_level,
        ts.contact_phone,
        ts.receiving_payment_type,
        ts.receiving_payment_name,
        ts.receiving_payment_account,
        ts.receiving_payment_bank,
        ts.status,
        cdi.dealer_name,
        tsc.contract_type,
        tsc.contract_code_name,
        ts.is_high_member,
        cdii.dealer_name as distributorName,
        tdcr.affiliate_name,
        tdcr.region_name,
        if(am.parent_id=0,am.name ,(select ams.name from t_terminal_account_manager ams WHERE am.parent_id = ams.id) ) as accountManagerName,
        (select count(1) from t_terminal_shop_node sn where sn.node_level='1' and sn.protocol_id=0 and sn.terminal_shop_id = ts.terminal_shop_id and sn.schedule_shop_id= ts.id and sn.node_status='1' and sn.is_back='0') node_type
        from  t_terminal_shop_info_schedule ts
        left join  t_terminal_shop_contract tsc
        on ts.terminal_shop_id = tsc.terminal_shop_id
        left join t_dealer_contract_rel tdcr
        on tsc.contract_code = tdcr.contract_code
        LEFT JOIN t_terminal_account_manager am ON ts.create_user = am.id
        left join t_cloud_dealer_info cdi
        on tsc.dealer_code = cdi.dealer_code
        left join t_cloud_dealer_info cdii on
        ts.distributor_id = cdii.id
        where  ts.status in (1,4,5)
        <if test="status != null">
            and ts.status = #{status}
        </if>
        <if test="isHighMember != null and isHighMember != ''">
            and ts.is_high_member = #{isHighMember}
        </if>
        <if test="levelCode != null and levelCode != ''">
            and ts.level_code = #{levelCode}
        </if>
        <if test="shopType != null">
            and ts.shop_type = #{shopType}
        </if>
        <if test="shopName != null and shopName != ''">
            and ts.shop_name like concat('%',#{shopName},'%')
        </if>
        <if test="contactName != null and contactName != ''">
            and ts.contact_name like concat('%',#{contactName},'%')
        </if>
        <if test="contactPhone != null and contactPhone != ''">
            and ts.contact_phone like concat('%',#{contactPhone},'%')
        </if>
        <if test="province != null and province != ''">
            and ts.province = #{province}
        </if>
        <if test="city != null and city != ''">
            and ts.city = #{city}
        </if>
        <if test="district != null and district != ''">
            and ts.district = #{district}
        </if>
        <if test="dealerName != null and dealerName != ''">
            and cdi.dealer_name like concat('%',#{dealerName},'%')
        </if>
        order by ts.id
    </select>

    <select id="selectProtocolAuditDataList" resultType="com.intelliquor.cloud.shop.terminal.model.resp.GtOpenTerminalProtocolAuditListResp">
        select
        ts.id,
        ts.id terminal_shop_id,
        ts.shop_name,
        ts.deputy_code,
        ts.main_code,
        ts.leader_name,
        ts.leader_phone,
        ts.province,
        ts.city,
        ts.district,
        ts.province+ts.city+ts.district area_name,
        ts.shop_type,
        ts.contact_name,
        ts.contact_level,
        ts.contact_phone,
        tp.check_status status,
        tp.id   protocol_id,
        tp.protocol_property,
        tp.product_type,
        tsl.level_name,
        ifnull((select change_type from t_terminal_protocol_change pc where pc.new_id = tp.id),0) node_type
        from  t_terminal_shop ts
        left join t_terminal_protocol tp
        on ts.id = tp.terminal_shop_id
        left join t_terminal_shop_level tsl
        on tp.level_code = tsl.id
        where ts.status = 1 and tp.check_status in (1,4,5)
        <if test="status != null">
            and tp.check_status = #{status}
        </if>
        <if test="shopType != null">
            and ts.shop_type = #{shopType}
        </if>
        <if test="shopName != null and shopName != ''">
            and ts.shop_name like concat('%',#{shopName},'%')
        </if>
        <if test="contactName != null and contactName != ''">
            and ts.contact_name like concat('%',#{contactName},'%')
        </if>
        <if test="contactPhone != null and contactPhone != ''">
            and ts.contact_phone like concat('%',#{contactPhone},'%')
        </if>
        <if test="province != null and province != ''">
            and ts.province = #{province}
        </if>
        <if test="city != null and city != ''">
            and ts.city = #{city}
        </if>
        <if test="district != null and district != ''">
            and ts.district = #{district}
        </if>
        order by tp.id
    </select>

    <select id="selectAuditDataByShopId" resultType="com.intelliquor.cloud.shop.terminal.model.resp.GtOpenTerminalShopAuditResp">
        select
        ts.id,
        ts.terminal_shop_id,
        ts.shop_name,
        cdi.dealer_name,
        cdi.dealer_code,
        tsc.contract_code,
        tsc.contract_type,
        ts.province,
        ts.city,
        ts.district,
        ts.contact_name,
        ts.contact_level,
        ts.contact_phone,
        ts.address,
        ts.license_code,
        ts.shop_type,
        ts.food_business_license,
        ts.receiving_payment_type,
        ts.receiving_payment_name,
        ts.receiving_payment_account,
        ts.receiving_payment_bank,
        ts.receiving_warehouse_province,
        ts.receiving_warehouse_city,
        ts.receiving_warehouse_district,
        ts.receiving_warehouse_address,
        ts.whether_proprietary_trading,
        ts.is_image,
        ts.deputy_code,
        ts.main_code,
        tsl.level_name as level_code,
        tsc.package_quantity_image,
        tsc.display_image,
        ts.head_img,
        ts.image_head_picture,
        ts.receiving_payment_account_picture,
        ts.license_img,
        tsc.package_quantity_name,
        tsc.package_quantity_display_surface,
        tsc.package_quantity_display_cost,
        tsc.package_quantity_replenish_stock_quantity,
        tsc.display_name,
        tsc.display_display_surface,
        tsc.display_display_cost,
        tsc.display_replenish_stock_quantity,
        ts.tag,
        ts.leader_name,
        ts.leader_phone,
        ts.remark,
        ts.whether_license,
        ts.status,
        ts.create_time,
        ts.company_id,
        ts.member_shop_id,
        ts.is_high_member,
        cdii.dealer_name as distributorName,
        CONCAT(ts.province, ts.city, ts.district) AS area_name,
        tdcr.affiliate_name,
        tdcr.region_name,
        if(am.parent_id=0,am.name ,(select ams.name from t_terminal_account_manager ams WHERE am.parent_id = ams.id) ) as accountManagerName
        from
        t_terminal_shop_info_schedule ts
        left join
        t_terminal_shop_contract tsc
        on ts.terminal_shop_id = tsc.terminal_shop_id
        left join
        t_cloud_dealer_info cdi
        on tsc.dealer_code = cdi.dealer_code
        left join
        t_cloud_dealer_info cdii
        on ts.distributor_id = cdii.id
        left join
        t_terminal_shop_level tsl
        on ts.level_code = tsl.id
        left join t_dealer_contract_rel tdcr
        on tsc.contract_code = tdcr.contract_code
        LEFT JOIN t_terminal_account_manager am ON ts.create_user = am.id
        where
        ts.id = #{shopId}
    </select>

    <select id="selectAuditData" resultType="com.intelliquor.cloud.shop.terminal.model.resp.GtOpenTerminalShopAuditResp">
        select
        ts.id,
        ts.shop_name,
        cdi.dealer_name,
        cdi.dealer_code,
        tsc.contract_code,
        tsc.contract_type,
        ts.province,
        ts.city,
        ts.district,
        ts.contact_name,
        ts.contact_level,
        ts.contact_phone,
        ts.address,
        ts.license_code,
        ts.shop_type,
        ts.food_business_license,
        ts.receiving_payment_type,
        ts.receiving_payment_name,
        ts.receiving_payment_account,
        ts.receiving_payment_bank,
        ts.receiving_warehouse_province,
        ts.receiving_warehouse_city,
        ts.receiving_warehouse_district,
        ts.receiving_warehouse_address,
        ts.whether_proprietary_trading,
        ts.is_image,
        ts.deputy_code,
        ts.main_code,
        tsl.level_name as level_code,
        tsc.package_quantity_image,
        tsc.display_image,
        ts.head_img,
        ts.image_head_picture,
        ts.receiving_payment_account_picture,
        ts.license_img,
        tsc.package_quantity_name,
        tsc.package_quantity_display_surface,
        tsc.package_quantity_display_cost,
        tsc.package_quantity_replenish_stock_quantity,
        tsc.display_name,
        tsc.display_display_surface,
        tsc.display_display_cost,
        tsc.display_replenish_stock_quantity,
        ts.tag,
        ts.leader_name,
        ts.leader_phone,
        ts.remark,
        ts.whether_license,
        ts.status,
        ts.create_time,
        ts.company_id,
        ts.member_shop_id,
        ts.is_high_member,
        cdii.dealer_name as distributorName,
        CONCAT(ts.province, ts.city, ts.district) AS area_name,
        tdcr.affiliate_name,
        tdcr.region_name,
        if(am.parent_id=0,am.name ,(select ams.name from t_terminal_account_manager ams WHERE am.parent_id = ams.id) ) as accountManagerName
        from
        t_terminal_shop ts
        left join
        t_terminal_shop_contract tsc
        on ts.id = tsc.terminal_shop_id
        left join
        t_cloud_dealer_info cdi
        on tsc.dealer_code = cdi.dealer_code
        left join
        t_cloud_dealer_info cdii
        on ts.distributor_id = cdii.id
        left join
        t_terminal_shop_level tsl
        on ts.level_code = tsl.id
        left join t_dealer_contract_rel tdcr
        on tsc.contract_code = tdcr.contract_code
        LEFT JOIN t_terminal_account_manager am ON ts.create_user = am.id
        where
        ts.id = #{shopId}
    </select>

    <select id="selectTerminalActivedShopListByIdList"
            resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalShopResp">
        select ts.id,
               ts.shop_name,
               ts.leader_name,
               ts.leader_phone,
               ts.tag,
               ts.head_img,
               ts.remark,
               ts.province,
               ts.city,
               ts.district,
               ts.address,
               ts.longitude,
               ts.latitude,
               ts.type,
               ts.is_image,
               ts.keeper_name,
               ts.keeper_phone,
               ts.shop_area,
               ts.license_img,
               ts.license_code,
               ts.status,
               ts.is_delete,
               ts.create_time,
               ts.create_user,
               ts.update_time,
               ts.company_id,
               ts.member_shop_id,
               ts.level_code,
               ts.level_code                        as levelName,
               tam.name                             as managerOrStaffName,
               tam.phone                            as managerOrStaffPhone,
               ts.shop_type                         as shopType,
               IFNULL(tamm.name, tammm.name)        as accountManagerName,
               IFNULL(tamm.phone, tammm.phone)      as accountManagerPhone,
               ts.terminal_divide_id,
               (case tam.type
                    when 0 then '客户经理'
                    when 1 then '经销商人员'
                    when 2 then '终端人员'
                    when 3 then '终端采集人员' end) as managerOrStaffType,
               ms.virtual_amount,
               ts.merge_type,
               ts.merge_id,
               ms.status                            as terminalStatus,
               ts.is_prepare
        from t_terminal_shop ts
                 left join
             t_member_shop ms
             on
                 ts.member_shop_id = ms.id
                 left join
             t_terminal_shop_level_config tslc
             on
                 ms.level_code = tslc.level_code
                 left join
             t_terminal_account_manager tam
             on
                 ts.create_user = tam.id
                 left join t_terminal_account_manager tamDivide on ts.terminal_divide_id = tamDivide.id
                 left join t_terminal_account_manager tamm on tamm.id = tamDivide.parent_id
                 left join t_terminal_account_manager tammm on tam.parent_id = tammm.id
        left join t_terminal_shop_info_schedule tsis on ts.id = tsis.terminal_shop_id
        where ts.is_delete = #{terminalShopManageReq.isDelete}
          and tsis.status = 1
          and ts.shop_type not in (5,10,11,12,13)
          and ts.id in
        <foreach collection="idList" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>

        <if test="managerIdList != null and managerIdList.size > 0">
            and ts.create_user in
            <foreach collection="managerIdList" item="managerId" separator="," close=")" open="(">
                #{managerId}
            </foreach>
        </if>

        <if test="terminalShopManageReq.shopName != null and terminalShopManageReq.shopName != ''">
            and ts.shop_name like concat('%', #{terminalShopManageReq.shopName}, '%')
        </if>

        <if test="terminalShopManageReq.searchText != null and terminalShopManageReq.searchText != ''">
            and (ts.shop_name like concat('%', #{terminalShopManageReq.searchText}, '%')
                or ts.leader_name like concat('%', #{terminalShopManageReq.searchText}, '%')
                or ts.leader_phone like concat('%', #{terminalShopManageReq.searchText}, '%')
                )
        </if>

        <if test="terminalShopManageReq.tag != null">
            and ts.tag = #{terminalShopManageReq.tag}
        </if>

        <if test="terminalShopManageReq.gatherMonth != null and terminalShopManageReq.gatherMonth != ''">
            and ts.create_time &gt;= #{terminalShopManageReq.gatherMonthStartTime}
            and ts.create_time &lt;= #{terminalShopManageReq.gatherMonthEndTime}
        </if>

        <if test="terminalShopManageReq.shopType != null">
            and ts.shop_type = #{terminalShopManageReq.shopType}
        </if>

        <if test="terminalShopManageReq.isHighMember != null">
            and ts.is_high_member = #{terminalShopManageReq.isHighMember}
        </if>

        <if test="terminalShopManageReq.terminalStatus != null">
            and ms.status = #{terminalShopManageReq.terminalStatus}
            and ts.merge_type &lt; 2
        </if>

        <if test="terminalShopManageReq.sort != null and terminalShopManageReq.sort == 0">
            order by ts.create_time desc
        </if>

        <if test="terminalShopManageReq.sort != null and terminalShopManageReq.sort == 1">
            order by ts.create_time asc
        </if>
    </select>
    <select id="selectSimilarTerminal" resultType="com.intelliquor.cloud.shop.terminal.model.resp.SimilarTerminalResp">
        select
            tsis.id as shopId,
            ts.shop_name as shopName,
            ts.leader_name as leaderName,
            ts.leader_phone as leaderPhone
        from
            t_terminal_shop ts
            left join t_terminal_shop_contract tsc on ts.id = tsc.terminal_shop_id
            left join t_cloud_dealer_info cdi on tsc.dealer_code = cdi.dealer_code
            LEFT JOIN t_terminal_shop_info_schedule tsis ON tsis.terminal_shop_id = ts.id
        where
            ts.id != #{req.shopId}
            and(ts.shop_name = #{req.shopName} and ts.city = #{req.city} )
            or (tsc.contract_code = #{req.contractCode} and cdi.dealer_name = #{req.dealerName}  and (ts.leader_name = #{req.leaderName} or ts.leader_phone = #{req.leaderPhone} or ts.address = #{req.address}))
            or (ts.shop_name like concat('%', #{req.shopName}, '%') and tsc.contract_code = #{req.contractCode} and ts.province = #{req.province} and ts.city = #{req.city})
    </select>

    <select id="getShopScheduleInfoById" resultType="java.util.Map">
        select
        tdcr.affiliate_name,
        tdcr.region_name,
        if(am.parent_id=0,am.name ,(select ams.name from t_terminal_account_manager ams WHERE am.parent_id = ams.id) ) as accountManagerName
        from  t_terminal_shop ts
        left join  t_terminal_shop_contract tsc
        on ts.id = tsc.terminal_shop_id
        left join t_dealer_contract_rel tdcr
        on tsc.contract_code = tdcr.contract_code
        LEFT JOIN t_terminal_account_manager am ON ts.create_user = am.id
        where  ts.id = #{id}
    </select>




    <select id="selectPage" resultType="com.intelliquor.cloud.shop.terminal.model.resp.OpenMemberRes">
        SELECT
            ms.id,
            ms.name,
            ms.linkman,
            ms.linkphone,
            ms.address,
            ts.main_code,
            ts.deputy_code,
            tsc.contract_type,
            tsc.dealer_code,
            cdi.dealer_name,
            ts.shop_type as terminalShopType,
            case
                when ts.shop_type=10 or ts.shop_type=11 or ts.shop_type=12 or ts.shop_type=13 then true
                else false
            end as isHotelTerminal
        FROM
            t_member_shop ms
            LEFT JOIN t_terminal_shop ts ON ts.member_shop_id = ms.id
            LEFT JOIN t_terminal_shop_contract tsc on tsc.member_shop_id = ms.id
            LEFT JOIN t_cloud_dealer_info cdi on cdi.dealer_code = tsc.dealer_code
        WHERE
            ms.`status` = 0
            AND ts.merge_type in(0,1)
            AND ts.merge_id = 0
        <if test="param2.name!=null and param2.name!=''">
            AND ts.shop_name LIKE CONCAT( '%', #{param2.name}, '%' )
        </if>
       <if test="param2.customerCode!=null and param2.customerCode!=''">
           AND tsc.dealer_code = #{param2.customerCode}
       </if>
        AND ts.id IN (
        SELECT
        tableShop.id
        FROM
        (
        SELECT
        tsc.terminal_shop_id AS id
        FROM
        t_terminal_shop_contract tsc
        LEFT JOIN t_terminal_shop ts ON tsc.terminal_shop_id = ts.id
        WHERE
        tsc.dealer_code IN ( SELECT dealer_code FROM t_terminal_account_manager_dealer WHERE ( account_manager_id = #{param2.accountManagerId} ) )
        AND ts.is_delete = 0
        AND ts.STATUS = 1 UNION ALL
        SELECT
        tsc.terminal_shop_id AS id
        FROM
        t_terminal_shop_contract tsc
        LEFT JOIN t_terminal_shop ts ON tsc.terminal_shop_id = ts.id
        WHERE
        ts.deputy_code IN (
        SELECT
        c.dealer_code
        FROM
        t_terminal_account_manager_dealer a,
        t_cloud_dealer_info b,
        t_cloud_dealer_info c,
        t_cloud_dealer_relation d
        WHERE
        a.account_manager_id = #{param2.accountManagerId}
        AND a.dealer_code = b.dealer_code
        AND b.id = d.parent_id
        AND d.dealer_id = c.id
        )
        AND ts.is_delete = 0
        AND ts.STATUS = 1
        ) AS tableShop
        )
        ORDER BY
            ts.create_time DESC
        LIMIT #{param1.current},#{param1.size}
    </select>

    <select id="selectYdPage" resultType="com.intelliquor.cloud.shop.terminal.model.resp.OpenMemberRes">
        SELECT
            ms.id,
            ms.NAME,
            ms.linkman,
            ms.linkphone,
            ms.address,
            ts.main_code,
            ts.deputy_code,
            tsc.contract_type,
            tsc.dealer_code,
            cdi.dealer_name,
            ts.shop_type as terminalShopType,
            case
            when ts.shop_type=10 or ts.shop_type=11 or ts.shop_type=12 or ts.shop_type=13 then true
            else false
            end as isHotelTerminal
        FROM
            t_member_shop ms
            LEFT JOIN t_terminal_shop ts ON ts.member_shop_id = ms.id
            LEFT JOIN t_terminal_shop_contract tsc ON tsc.member_shop_id = ms.id
            LEFT JOIN t_cloud_dealer_info cdi ON cdi.dealer_code = tsc.dealer_code
        WHERE
            ms.`status` = 0
            AND ts.merge_type in(0,1)
            AND ts.merge_id = 0
        <if test="param2.name!=null and param2.name!=''">
            AND ts.shop_name LIKE CONCAT( '%', #{param2.name}, '%' )
        </if>
        <if test="param2.customerCode!=null and param2.customerCode!=''">
            AND tsc.dealer_code = #{param2.customerCode}
        </if>
            and (ts.create_user = #{param2.accountManagerId} or ts.terminal_divide_id = #{param2.accountManagerId})
	        LIMIT #{param1.current},#{param1.size}
    </select>



    <select id="countPage" resultType="java.lang.Long">
        SELECT
            count(*)
        FROM
        t_member_shop ms
        LEFT JOIN t_terminal_shop ts ON ts.member_shop_id = ms.id
        LEFT JOIN t_terminal_shop_contract tsc on tsc.member_shop_id = ms.id
        LEFT JOIN t_cloud_dealer_info cdi on cdi.dealer_code = tsc.dealer_code
        WHERE
        ms.`status` = 0
        <if test="memberReq.name!=null and memberReq.name!=''">
            AND ts.shop_name LIKE CONCAT( '%', #{memberReq.name}, '%' )
        </if>
        <if test="memberReq.customerCode!=null and memberReq.customerCode!=''">
            AND tsc.dealer_code = #{memberReq.customerCode}
        </if>
        AND ts.id IN (
        SELECT
        tableShop.id
        FROM
        (
        SELECT
        tsc.terminal_shop_id AS id
        FROM
        t_terminal_shop_contract tsc
        LEFT JOIN t_terminal_shop ts ON tsc.terminal_shop_id = ts.id
        WHERE
        tsc.dealer_code IN ( SELECT dealer_code FROM t_terminal_account_manager_dealer WHERE ( account_manager_id = #{memberReq.accountManagerId} ) )
        AND ts.is_delete = 0
        AND ts.STATUS = 1 UNION ALL
        SELECT
        tsc.terminal_shop_id AS id
        FROM
        t_terminal_shop_contract tsc
        LEFT JOIN t_terminal_shop ts ON tsc.terminal_shop_id = ts.id
        WHERE
        tsc.dealer_code IN (
        SELECT
        c.dealer_code
        FROM
        t_terminal_account_manager_dealer a,
        t_cloud_dealer_info b,
        t_cloud_dealer_info c,
        t_cloud_dealer_relation d
        WHERE
        a.account_manager_id = #{memberReq.accountManagerId}
        AND a.dealer_code = b.dealer_code
        AND b.id = d.parent_id
        AND d.dealer_id = c.id
        )
        AND ts.is_delete = 0
        AND ts.STATUS = 1
        ) AS tableShop
        )
        ORDER BY
        ts.create_time DESC
    </select>

    <select id="countYdPage" resultType="java.lang.Long">
        SELECT
            count(*)
        FROM
            t_member_shop ms
            LEFT JOIN t_terminal_shop ts ON ts.member_shop_id = ms.id
            LEFT JOIN t_terminal_shop_contract tsc ON tsc.member_shop_id = ms.id
            LEFT JOIN t_cloud_dealer_info cdi ON cdi.dealer_code = tsc.dealer_code
        WHERE
            ms.`status` = 0
        <if test="memberReq.name!=null and memberReq.name!=''">
            AND ts.shop_name LIKE CONCAT( '%', #{memberReq.name}, '%' )
        </if>
        <if test="memberReq.customerCode!=null and memberReq.customerCode!=''">
            AND tsc.dealer_code = #{memberReq.customerCode}
        </if>
            and (ts.create_user = #{memberReq.accountManagerId} or ts.terminal_divide_id = #{memberReq.accountManagerId})
    </select>

    <select id="getDistributorList" resultType="com.intelliquor.cloud.shop.terminal.model.TCloudDealerInfoModel">
        SELECT
            t1.dealer_name,
            t1.dealer_code,
            t1.provinces,
            t1.city,
            t1.district ,
            t1.id
        FROM
            t_cloud_dealer_relation t
            LEFT JOIN t_cloud_dealer_info t1 ON t1.id = t.dealer_id
            LEFT JOIN t_cloud_dealer_info t2 ON t2.id = t.parent_id
            LEFT JOIN t_cloud_dealer_info_detail t3 ON t3.dealer_id = t1.id
        WHERE
            t2.is_delete = 1
            AND t1.type = 2
            AND t1.account_type = 3
            <if test="contractNo!=null and contractNo!=''">
                AND t3.contract_no = #{contractNo}
            </if>
            AND t2.id = #{superiorId}
            order by t.create_time desc
    </select>

    <select id="getDealer" resultType="com.intelliquor.cloud.shop.terminal.model.TCloudDealerInfoModel">
        SELECT
        t1.dealer_name,
        t1.dealer_code,
        t1.provinces,
        t1.city,
        t1.district ,
        t1.id
        FROM
        t_cloud_dealer_relation t
        LEFT JOIN t_cloud_dealer_info t1 ON t1.id = t.dealer_id
        LEFT JOIN t_cloud_dealer_info_detail t3 ON t3.dealer_id = t1.id
        WHERE
        t1.is_delete = 1
        AND t1.type = 1
        AND t1.account_type = 1
        <if test="contractNo!=null and contractNo!=''">
            AND t3.contract_no = #{contractNo}
        </if>
        AND t1.id = #{superiorId}
        order by t.create_time desc
    </select>

    <update id="batchUpdateDistributorId">
        update
        t_terminal_shop
        set distributor_id = #{dealerId}
        where id in
        <foreach collection="successIds" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </update>

    <update id="batchUpdateCopartnerId">
        update
        t_terminal_shop
        set copartner_id = #{copartnerId}
        where id in
        <foreach collection="successIds" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </update>

    <select id="getPartnerList" resultType="com.intelliquor.cloud.shop.terminal.model.resp.PartnerInfoListResp">
        SELECT
            t.id,
            t.dealer_code as dealerCode,
            t.dealer_name as dealerName,
            t3.dealer_code as parentDealerCode,
            t3.dealer_name as parentDealerName
        FROM
            t_cloud_dealer_info t
            LEFT JOIN t_cloud_dealer_relation t2 ON t2.dealer_id = t.id
            LEFT JOIN t_cloud_dealer_info t3 ON t3.id= t2.parent_id
        WHERE t.is_delete = 1
        AND t.account_type = 4
        AND t3.account_type = 6
    </select>

    <update id="updateTerminalContract">
        UPDATE t_terminal_shop_contract
        SET contract_code = #{req.contractCode},
        contract_type = #{req.contractType},
        contract_code_name = #{req.contractName}
        WHERE
            terminal_shop_id in
        <foreach collection="terminalShopIds" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </update>
    <update id="updateDistributorIdById">
        update t_terminal_shop  set distributor_id = #{distributorId} where id= #{id}
    </update>

    <select id="getContractList" resultType="com.intelliquor.cloud.shop.common.model.CloudDealerGtFxsContractCommonModel">
        SELECT
        id,
        contract_type as contracttype,
        contract_code as contractCode
    FROM
        t_terminal_shop_contract
    WHERE
        dealer_code = #{dealerCode}
    GROUP BY
        contract_type,
        contract_code
    </select>

    <select id="getActivateTerminalStatistics" resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalTaskStatisticsResp">
        select
            tts.id,
            DATE_FORMAT(tts.create_time, '%Y') as year,
            MONTH (tts.create_time) as month,
            DATE_FORMAT(tts.create_time, '%Y-%m') as ym,
            tts.create_user,
            IFNULL(count(tts.id), 0) as completed_score,
            IFNULL(tttm.plan_visit_score, 0) as plan_score,
            tttm.id as tm_id
        from
            t_terminal_shop tts
--             与智赢沟通，终端审核状态从t_terminal_shop_info_schedule表中获取
--             join t_terminal_shop_node ttsn on
--             ttsn.terminal_shop_id = tts.id and ttsn.node_type =1 and ttsn.is_back =0 and ttsn.node_status =1
            join t_terminal_shop_info_schedule ttsis on
            ttsis.terminal_shop_id = tts.id and ttsis.status = 1
            left join t_terminal_task_receive tttr on
            tttr.broker_id = tts.create_user
            left join t_terminal_task_package tttp on
            tttp.id = tttr.tp_id
            left join t_terminal_task_monthly tttm on
            tttm.tp_id = tttp.id
            and tttm.`month` = MONTH (tts.create_time)
        where
            tttr.delete_flag = 0
          and tttm.delete_flag = 0
          and tts.create_user = #{brokerId}
          and DATE_FORMAT(tts.create_time, '%Y') = #{year}
        GROUP BY
            ym,
            tts.create_user
    </select>

    <select id="getActivateStatisticsByTmId" resultType="com.intelliquor.cloud.shop.terminal.model.TerminalTaskActivateStatisticsModel">
        select
            tts.create_user,
            IFNULL(count(tts.id), 0) as totalNum
        from
            t_terminal_task_monthly tttm
                join t_terminal_task_receive tttr on
                        tttr.tp_id = tttm.tp_id
                    and tttr.delete_flag = 0
                join t_terminal_shop tts on
                        tts.create_user = tttr.broker_id
                    and tts.is_delete = 0
                    and tts.create_time BETWEEN DATE(#{startTime}) and DATE(#{endTime})
--             与智赢沟通，终端审核状态从t_terminal_shop_info_schedule表中获取
--             join t_terminal_shop_node ttsn on
--             ttsn.terminal_shop_id = tts.id
--             and ttsn.node_type = 1
--             and ttsn.is_back = 0
--             and ttsn.node_status = 1
            join t_terminal_shop_info_schedule ttsis on
            ttsis.terminal_shop_id = tts.id and ttsis.status = 1
        where
            tttm.id = #{tmId}
          and tttm.delete_flag = 0
        order by
            tts.create_user desc
    </select>

    <select id="getTerminalShopNumByContractCodeAndShopType" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.channelStatistics.FindMapResp">
        SELECT
        COUNT(DISTINCT ts.id) as count,
        ts.shop_type as shopType
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND ts.shop_type NOT IN(5, 6, 7, 8)
        AND tsc.contract_code = #{contractCode}
        <if test="startTime != null and startTime != ''">
            AND ts.create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND ts.create_time &lt;= #{endTime}
        </if>
        <if test="shopType != null and shopType != ''">
            AND ts.shop_type = #{shopType}
        </if>
        GROUP BY ts.shop_type
    </select>

    <select id="getTerminalShopNumByContractCodeAndDisplayProcotol" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT
            COUNT(DISTINCT ts.id)
        FROM
            t_terminal_shop ts
                LEFT JOIN
            t_terminal_shop_contract tsc
            ON
                ts.id = tsc.terminal_shop_id
                LEFT JOIN
            t_terminal_protocol tp
            ON
                ts.id = tp.terminal_shop_id
        WHERE
            ts.`status` = 1
          AND ts.is_prepare = 0
          AND ts.is_delete = 0
          AND ts.shop_type NOT IN(5, 6, 7, 8)
          AND tp.protocol_property IN(0,1)
          AND tp.delete_status = 0
          AND tp.check_status=1
          AND tsc.contract_code = #{contractCode}
    </select>

    <select id="getTerminalShopNumByContractCodeAndDisplayProcotolAndExistSkuCheck" parameterType="java.util.Map" resultType="java.lang.Long">
        SELECT
            COUNT(DISTINCT shopId)
        FROM
            (
                SELECT
                    ts.id as shopId,
                    (SELECT COUNT(sku.id) FROM t_terminal_sku_check sku WHERE sku.terminal_shop_id = ts.id AND sku.create_time &gt;= #{startTime} AND sku.create_time &lt;= #{endTime}) as count
                FROM
                    t_terminal_shop ts
                    LEFT JOIN
                    t_terminal_shop_contract tsc
                ON
                    ts.id = tsc.terminal_shop_id
                    LEFT JOIN
                    t_terminal_protocol tp
                    ON
                    ts.id = tp.terminal_shop_id
                WHERE
                    ts.`status` = 1
                  AND ts.is_prepare = 0
                  AND ts.is_delete = 0
                  AND ts.shop_type NOT IN(5, 6, 7, 8)
                  AND tp.protocol_property IN(0,1)
                  AND tp.delete_status = 0
                  AND tp.check_status=1
                  AND tsc.contract_code = #{contractCode}
                HAVING count > 0
            ) as datas
    </select>

    <select id="getTerminalShopDisplaySurfaceTotal" parameterType="java.util.Map" resultType="java.lang.Long">
        SELECT
            IFNULL(SUM(tpp.display_surface), 0)
        FROM
            t_terminal_shop ts
                LEFT JOIN
            t_terminal_shop_contract tsc
            ON
                ts.id = tsc.terminal_shop_id
                LEFT JOIN
            t_terminal_protocol tp
            ON
                ts.id = tp.terminal_shop_id
                LEFT JOIN
            t_terminal_product_protocol tpp
            ON
                tp.product_protocol_config_id = tpp.id
        WHERE
            ts.`status` = 1
          AND ts.is_prepare = 0
          AND ts.is_delete = 0
          AND ts.shop_type NOT IN(5, 6, 7, 8)
          AND tp.protocol_property IN(0,1)
          AND tp.delete_status = 0
          AND tp.check_status=1
          AND tsc.contract_code = #{contractCode}
          AND tp.effective_time &lt;= #{fifteenTime}
    </select>

    <select id="getSumMaxSkuTotalByContractCode" parameterType="java.util.Map" resultType="java.lang.Long">
        SELECT
            IFNULL(SUM(datas.skuTotal), 0)
        FROM (
                 SELECT
                     (SELECT IFNULL(MAX(sku.sku_total),0) FROM t_terminal_sku_check sku WHERE sku.terminal_shop_id = ts.id AND (check_status = 1 OR check_status = 4) AND sku.create_time &gt;= #{startTime} AND sku.create_time &lt;= #{endTime}) as skuTotal
                 FROM
                     t_terminal_shop ts
                         LEFT JOIN
                     t_terminal_shop_contract tsc
                     ON
                         ts.id = tsc.terminal_shop_id
                         LEFT JOIN
                     t_terminal_protocol tp
                     ON
                         ts.id = tp.terminal_shop_id
                 WHERE
                     ts.`status` = 1
                   AND ts.is_prepare = 0
                   AND ts.is_delete = 0
                   AND tp.delete_status = 0
                   AND ts.shop_type NOT IN(5, 6, 7, 8)
        AND tp.protocol_property IN(0,1)
        AND tp.delete_status = 0
        AND tp.check_status=1
        AND tsc.contract_code = #{contractCode}
                 GROUP BY ts.id
             ) AS datas
    </select>

    <select id="getTerminalShopCountExistVisit" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.channelStatistics.FindMapResp">
        SELECT
            COUNT(DISTINCT datas.tsId) as count,
            datas.shop_type as shopType
        FROM (
                 SELECT
                     DISTINCT ts.id as tsId,
                     ts.shop_type,
                    (SELECT COUNT(visit.id) FROM t_terminal_visit_record visit WHERE visit.shop_id = ts.member_shop_id AND visit.create_time &gt;= #{startTime} AND visit.create_time &lt;= #{endTime}) as visitCount
                 FROM
                     t_terminal_shop ts
                         LEFT JOIN
                     t_terminal_shop_contract tsc
                     ON
                         ts.id = tsc.terminal_shop_id
                 WHERE
                     ts.`status` = 1
                   AND ts.is_prepare = 0
                   AND ts.is_delete = 0
                   AND ts.shop_type NOT IN(5, 6, 7, 8)
                   AND tsc.contract_code = #{contractCode}
                   AND ts.create_time &lt;= #{endTime}
                    <if test="shopType != null and shopType != ''">
                        AND ts.shop_type = #{shopType}
                    </if>
                 HAVING visitCount > 0
             ) as datas
        GROUP BY datas.shop_type
    </select>

    <select id="getTerminalShopCountNotExistVisit" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.channelStatistics.FindMapResp">
        SELECT
            COUNT(DISTINCT datas.tsId) as count,
            datas.shop_type as shopType
        FROM (
                 SELECT
                     DISTINCT ts.id as tsId,
                     ts.shop_type,
                     (SELECT COUNT(visit.id) FROM t_terminal_visit_record visit WHERE visit.shop_id = ts.member_shop_id AND visit.create_time &gt;= #{startTime} AND visit.create_time &lt;= #{endTime}) as visitCount
                 FROM
                     t_terminal_shop ts
                         LEFT JOIN
                     t_terminal_shop_contract tsc
                     ON
                         ts.id = tsc.terminal_shop_id
                 WHERE
                     ts.`status` = 1
                   AND ts.is_prepare = 0
                   AND ts.is_delete = 0
                   AND ts.shop_type NOT IN(5, 6, 7, 8)
                    <if test="shopType != null and shopType != ''">
                        AND ts.shop_type = #{shopType}
                    </if>
                   AND tsc.contract_code = #{contractCode}
                   AND ts.create_time &lt;= #{endTime}
                 HAVING visitCount = 0
             ) as datas
        GROUP BY datas.shop_type
    </select>

    <select id="getTerminalShopVisitListByContractCode" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.visitStatistics.VisitListResp">
        SELECT
        DISTINCT ts.id as "storeId",
        ts.member_shop_id as "memberShopId",
        ts.shop_name as "storeName",
        tsl.level_name as "storeLevel",
        tp.product_type as "yyzx",
        (SELECT COUNT(visit.id) FROM t_terminal_visit_record visit WHERE visit.shop_id = ts.member_shop_id AND visit.create_time &gt;= #{startTime} AND visit.create_time &lt;= #{endTime}) as currentVisitNums,
        (SELECT COUNT(visit.id) FROM t_terminal_visit_record visit WHERE visit.shop_id = ts.member_shop_id) as allVisitNums
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        LEFT JOIN
        t_terminal_protocol tp
        ON
        ts.id = tp.terminal_shop_id
        LEFT JOIN
        t_terminal_shop_level tsl
        ON
        tp.level_code = tsl.id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND ts.shop_type NOT IN(5, 6, 7, 8)
        AND tp.delete_status = 0
        AND tp.protocol_type = 0
        AND tp.check_status=1
        AND tsc.contract_code = #{contractCode}
        AND ts.shop_type = #{shopType}
        <if test="storeName != null and storeName != ''">
            AND ts.shop_name LIKE CONCAT('%',#{storeName},'%')
        </if>
        <choose>
            <when test="sort != null and sort != ''">
                ORDER BY currentVisitNums ${sort}
            </when>
            <otherwise>
                ORDER BY currentVisitNums DESC
            </otherwise>
        </choose>
    </select>

    <select id="getTerminalShopVisitListByContractCodeAndEndTime" parameterType="java.util.Map"  resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.visitStatistics.VisitListResp">
        SELECT
        DISTINCT ts.id as "storeId",
        ts.member_shop_id as "memberShopId",
        ts.shop_name as "storeName",
        tsl.level_name as "storeLevel",
        tp.product_type as "yyzx",
        (SELECT COUNT(visit.id) FROM t_terminal_visit_record visit WHERE visit.shop_id = ts.member_shop_id AND visit.create_time &gt;= #{startTime} AND visit.create_time &lt;= #{endTime}) as currentVisitNums,
        (SELECT COUNT(visit.id) FROM t_terminal_visit_record visit WHERE visit.shop_id = ts.member_shop_id) as allVisitNums
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        LEFT JOIN
        t_terminal_protocol tp
        ON
        ts.id = tp.terminal_shop_id
        LEFT JOIN
        t_terminal_shop_level tsl
        ON
        tp.level_code = tsl.id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND ts.shop_type NOT IN(5, 6, 7, 8)
        AND tp.delete_status = 0
        AND tp.protocol_type = 0
        AND tp.check_status=1
        AND tsc.contract_code = #{contractCode}
        AND ts.create_time &lt;= #{endTime}
        AND ts.shop_type = #{shopType}
        <if test="storeName != null and storeName != ''">
            AND ts.shop_name LIKE CONCAT('%',#{storeName},'%')
        </if>
        <choose>
            <when test="sort != null and sort != ''">
                ORDER BY currentVisitNums ${sort}
            </when>
            <otherwise>
                ORDER BY currentVisitNums DESC
            </otherwise>
        </choose>
    </select>

    <select id="getTerminalShopVisitListByContractCodeAndCurrentMonth" parameterType="java.util.Map"  resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.visitStatistics.VisitListResp">
        SELECT
        DISTINCT ts.id as "storeId",
        ts.member_shop_id as "memberShopId",
        ts.shop_name as "storeName",
        tsl.level_name as "storeLevel",
        tp.product_type as "yyzx",
        (SELECT COUNT(visit.id) FROM t_terminal_visit_record visit WHERE visit.shop_id = ts.member_shop_id AND visit.create_time &gt;= #{startTime} AND visit.create_time &lt;= #{endTime}) as currentVisitNums,
        (SELECT COUNT(visit.id) FROM t_terminal_visit_record visit WHERE visit.shop_id = ts.member_shop_id) as allVisitNums
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        LEFT JOIN
        t_terminal_protocol tp
        ON
        ts.id = tp.terminal_shop_id
        LEFT JOIN
        t_terminal_shop_level tsl
        ON
        tp.level_code = tsl.id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND ts.shop_type NOT IN(5, 6, 7, 8)
        AND tp.delete_status = 0
        AND tp.protocol_type = 0
        AND tp.check_status=1
        AND tsc.contract_code = #{contractCode}
        AND ts.create_time &gt;= #{startTime}
        AND ts.create_time &lt;= #{endTime}
        AND ts.shop_type = #{shopType}
        <if test="storeName != null and storeName != ''">
            AND ts.shop_name LIKE CONCAT('%',#{storeName},'%')
        </if>
        <choose>
            <when test="sort != null and sort != ''">
                ORDER BY currentVisitNums ${sort}
            </when>
            <otherwise>
                ORDER BY currentVisitNums DESC
            </otherwise>
        </choose>
    </select>

    <select id="getTerminalShopVisitListByContractCodeAndCurrentVisitStores" parameterType="java.util.Map"  resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.visitStatistics.VisitListResp">
        SELECT
        DISTINCT ts.id as "storeId",
        ts.member_shop_id as "memberShopId",
        ts.shop_name as "storeName",
        tsl.level_name as "storeLevel",
        tp.product_type as "yyzx",
        (SELECT COUNT(visit.id) FROM t_terminal_visit_record visit WHERE visit.shop_id = ts.member_shop_id AND visit.create_time &gt;= #{startTime} AND visit.create_time &lt;= #{endTime}) as currentVisitNums,
        (SELECT COUNT(visit.id) FROM t_terminal_visit_record visit WHERE visit.shop_id = ts.member_shop_id) as allVisitNums
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        LEFT JOIN
        t_terminal_protocol tp
        ON
        ts.id = tp.terminal_shop_id
        LEFT JOIN
        t_terminal_shop_level tsl
        ON
        tp.level_code = tsl.id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND ts.shop_type NOT IN(5, 6, 7, 8)
        AND tp.delete_status = 0
        AND tp.protocol_type = 0
        AND tp.check_status=1
        AND ts.shop_type = #{shopType}
        AND ts.create_time &lt;= #{endTime}
        AND tsc.contract_code = #{contractCode}
        <if test="storeName != null and storeName != ''">
            AND ts.shop_name LIKE CONCAT('%',#{storeName},'%')
        </if>
        HAVING currentVisitNums > 0
        <choose>
            <when test="sort != null and sort != ''">
                ORDER BY currentVisitNums ${sort}
            </when>
            <otherwise>
                ORDER BY currentVisitNums DESC
            </otherwise>
        </choose>
    </select>

    <select id="getTerminalShopVisitListByContractCodeAndCurrentNotVisitStores" parameterType="java.util.Map"  resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.visitStatistics.VisitListResp">
        SELECT
        DISTINCT ts.id as "storeId",
        ts.member_shop_id as "memberShopId",
        ts.shop_name as "storeName",
        tsl.level_name as "storeLevel",
        tp.product_type as "yyzx",
        (SELECT COUNT(visit.id) FROM t_terminal_visit_record visit WHERE visit.shop_id = ts.member_shop_id AND visit.create_time &gt;= #{startTime} AND visit.create_time &lt;= #{endTime}) as currentVisitNums,
        (SELECT COUNT(visit.id) FROM t_terminal_visit_record visit WHERE visit.shop_id = ts.member_shop_id) as allVisitNums
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        LEFT JOIN
        t_terminal_protocol tp
        ON
        ts.id = tp.terminal_shop_id
        LEFT JOIN
        t_terminal_shop_level tsl
        ON
        tp.level_code = tsl.id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND ts.shop_type NOT IN(5, 6, 7, 8)
        AND tp.delete_status = 0
        AND tp.protocol_type = 0
        AND tp.check_status=1
        AND ts.shop_type = #{shopType}
        AND ts.create_time &lt;= #{endTime}
        AND tsc.contract_code = #{contractCode}
        <if test="storeName != null and storeName != ''">
            AND ts.shop_name LIKE CONCAT('%',#{storeName},'%')
        </if>
        HAVING currentVisitNums = 0
        <choose>
            <when test="sort != null and sort != ''">
                ORDER BY currentVisitNums ${sort}
            </when>
            <otherwise>
                ORDER BY currentVisitNums DESC
            </otherwise>
        </choose>
    </select>

    <select id="getTerminalShopVisitListByContractCodeAndCurrentVisitNums" parameterType="java.util.Map"  resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.visitStatistics.VisitListResp">
        SELECT
        DISTINCT ts.id as "storeId",
        ts.member_shop_id as "memberShopId",
        ts.shop_name as "storeName",
        tsl.level_name as "storeLevel",
        tp.product_type as "yyzx",
        COUNT(DISTINCT visit.id) as currentVisitNums
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_visit_record visit
        ON
        visit.shop_id = ts.member_shop_id
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        LEFT JOIN
        t_terminal_protocol tp
        ON
        ts.id = tp.terminal_shop_id
        LEFT JOIN
        t_terminal_shop_level tsl
        ON
        tp.level_code = tsl.id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND ts.shop_type NOT IN(5, 6, 7, 8)
        AND tp.delete_status = 0
        AND tp.protocol_type = 0
        AND tp.check_status=1
        AND ts.shop_type = #{shopType}
        AND visit.create_time &gt;= #{startTime}
        AND visit.create_time &lt;= #{endTime}
        AND tsc.contract_code = #{contractCode}
        <if test="storeName != null and storeName != ''">
            AND ts.shop_name LIKE CONCAT('%',#{storeName},'%')
        </if>
        GROUP BY ts.id
        <choose>
            <when test="sort != null and sort != ''">
                ORDER BY currentVisitNums ${sort}
            </when>
            <otherwise>
                ORDER BY currentVisitNums DESC
            </otherwise>
        </choose>
    </select>

    <select id="getTerminalShopNumByContractCodeAndShopLevel" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.channelStatistics.FindMapResp">
        SELECT
        COUNT(DISTINCT ts.id) as count,
        tsl.id as shopType
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        LEFT JOIN
        t_terminal_protocol tp
        ON
        ts.id = tp.terminal_shop_id
        LEFT JOIN
        t_terminal_shop_level tsl
        ON
        tp.level_code = tsl.id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND tp.delete_status = 0
        AND tp.protocol_type = 0
        AND tp.check_status=1
        AND tsc.contract_code = #{contractCode}
        <if test="startTime != null and startTime != ''">
            AND ts.create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND ts.create_time &lt;= #{endTime}
        </if>
        <if test="shopLevel != null and shopLevel != ''">
            AND tsl.id = #{shopLevel}
        </if>
        GROUP BY tsl.id
    </select>

    <select id="getTerminalShopCountNotExistSkuCheckPass" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.channelStatistics.FindMapResp">
        SELECT
        COUNT(DISTINCT datas.tsId) as count,
        datas.tslId as shopType
        FROM (
        SELECT
        DISTINCT ts.id as tsId,
        tsl.id as tslId,
        (SELECT COUNT(sku.id) FROM t_terminal_sku_check sku WHERE sku.terminal_shop_id = ts.id AND sku.create_time &gt;= #{startTime} AND sku.create_time &lt;= #{endTime} AND (sku.check_status = 0 OR sku.check_status = 1)) as skuCount
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        LEFT JOIN
        t_terminal_protocol tp
        ON
        ts.id = tp.terminal_shop_id
        LEFT JOIN
        t_terminal_shop_level tsl
        ON
        tp.level_code = tsl.id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND tp.delete_status = 0
        AND tp.protocol_type = 0
        AND tp.check_status=1
        AND tsc.contract_code = #{contractCode}
        <if test="endTime != null and endTime != ''">
            AND ts.create_time &lt;= #{endTime}
        </if>
        <if test="shopLevel != null and shopLevel != ''">
            AND tsl.id = #{shopLevel}
        </if>
        HAVING skuCount = 0
        ) as datas
        GROUP BY datas.tslId
    </select>


    <select id="getTerminalShopCountExistSkuCheckAuditIn" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.channelStatistics.FindMapResp">
        SELECT
        COUNT(DISTINCT datas.tsId) as count,
        datas.tslId as shopType
        FROM (
        SELECT
        DISTINCT ts.id as tsId,
        tsl.id as tslId,
        (SELECT COUNT(sku.id) FROM t_terminal_sku_check sku WHERE sku.terminal_shop_id = ts.id AND sku.create_time &gt;= #{startTime} AND sku.create_time &lt;= #{endTime} AND (sku.check_status = 0 OR sku.check_status = 3)) as skuCount,
        (SELECT COUNT(sku.id) FROM t_terminal_sku_check sku WHERE sku.terminal_shop_id = ts.id AND sku.create_time &gt;= #{startTime} AND sku.create_time &lt;= #{endTime} AND sku.check_status = 1) as skuPassCount
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        LEFT JOIN
        t_terminal_protocol tp
        ON
        ts.id = tp.terminal_shop_id
        LEFT JOIN
        t_terminal_shop_level tsl
        ON
        tp.level_code = tsl.id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND tp.delete_status = 0
        AND tp.protocol_type = 0
        AND tp.check_status=1
        AND tsc.contract_code = #{contractCode}
        <if test="endTime != null and endTime != ''">
            AND ts.create_time &lt;= #{endTime}
        </if>
        <if test="shopLevel != null and shopLevel != ''">
            AND tsl.id = #{shopLevel}
        </if>
        HAVING skuCount > 0 AND skuPassCount = 0
        ) as datas
        GROUP BY datas.tslId
    </select>

    <select id="getTerminalSkuCheckDisplayNumTotal" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.channelStatistics.FindMapResp">
        SELECT
        IFNULL(SUM(tpp.display_surface), 0) as count,
        tsl.id as shopType
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        LEFT JOIN
        t_terminal_protocol tp
        ON
        ts.id = tp.terminal_shop_id
        LEFT JOIN
        t_terminal_shop_level tsl
        ON
        tp.level_code = tsl.id
        LEFT JOIN
        t_terminal_product_protocol tpp
        ON
        tp.product_protocol_config_id = tpp.id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND tp.delete_status = 0
        AND tp.protocol_type = 0
        AND tp.check_status=1
        AND tp.effective_time &lt;= #{fifteenTime}
        AND tsc.contract_code = #{contractCode}
        <if test="endTime != null and endTime != ''">
            AND ts.create_time &lt;= #{endTime}
        </if>
        <if test="shopLevel != null and shopLevel != ''">
            AND tsl.id = #{shopLevel}
        </if>
        GROUP BY tsl.id
    </select>

    <select id="getTerminalSkuCheckShopListByContractCode" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.skuCheckStatistics.SkuCheckShopListResp">
        SELECT
        DISTINCT ts.id as "storeId",
        ts.shop_name as "storeName",
        ts.shop_type as "storeType",
        tsl.level_name as "storeLevel",
        tp.product_type as "yyzx",
        (SELECT MAX(sku.sku_total) FROM t_terminal_sku_check sku WHERE sku.terminal_shop_id = ts.id AND (sku.check_status = 1 OR sku.check_status = 4) AND create_time &gt;= #{startTime} AND create_time &lt;= #{endTime}) as currentDisplayNums,
        IFNULL((SELECT ssss.count FROM (
        SELECT MIN(sss.ind),sss.terminal_shop_id,sss.count FROM (
        SELECT CONCAT(ss.num,'_',ss.skuCount) as count,ss.terminal_shop_id,(@i:=@i+1) as ind FROM (
        SELECT
        case when (skuDatas.check_status = 1 and skuDatas.skuCount > 0) or (skuDatas.check_status = 4 and skuDatas.skuCount > 0) then 1
        when (skuDatas.check_status = 3 and skuDatas.skuCount > 0) or (skuDatas.check_status = 0 and skuDatas.checkNeedCount > 0 and skuDatas.skuCount > 0) then 2
        when skuDatas.check_status = 2 and skuDatas.skuCount > 0 then 0 else 0 end as num,
        skuDatas.terminal_shop_id,skuDatas.check_status,skuDatas.skuCount
        FROM (
        SELECT sku.terminal_shop_id,sku.check_status, COUNT(sku.id) as skuCount, SUM(IF(sku.check_need = 1, 1, 0)) as checkNeedCount FROM t_terminal_sku_check sku,t_terminal_shop_contract ct
        WHERE sku.terminal_shop_id = ct.terminal_shop_id
        AND ct.contract_code = #{contractCode}
        AND sku.create_time &gt;= #{startTime} AND sku.create_time &lt;= #{endTime} GROUP BY sku.terminal_shop_id,sku.check_status
        ) skuDatas
        ) ss ,(SELECT @i:=0) AS itable ORDER BY FIELD(ss.num, 1 ,2 ,0)
        ) sss GROUP BY sss.terminal_shop_id
        ) ssss where ssss.terminal_shop_id = ts.id), '0_0') as displayStatusAndCurrentSkuCount
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        LEFT JOIN
        t_terminal_protocol tp
        ON
        ts.id = tp.terminal_shop_id
        LEFT JOIN
        t_terminal_shop_level tsl
        ON
        tp.level_code = tsl.id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND tp.delete_status = 0
        AND tp.protocol_type = 0
        AND tp.check_status=1
        AND tsc.contract_code = #{contractCode}
        AND tsl.id = #{shopLevel}
        <if test="storeName != null and storeName != ''">
            AND ts.shop_name LIKE CONCAT('%',#{storeName},'%')
        </if>
        <choose>
            <when test="sort != null and sort != ''">
                ORDER BY currentDisplayNums ${sort}
            </when>
            <otherwise>
                ORDER BY currentDisplayNums DESC
            </otherwise>
        </choose>
    </select>

    <select id="getTerminalSkuCheckShopListByContractCodeAndEndTime" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.skuCheckStatistics.SkuCheckShopListResp">
        SELECT
        DISTINCT ts.id as "storeId",
        ts.shop_name as "storeName",
        ts.shop_type as "storeType",
        tsl.level_name as "storeLevel",
        tp.product_type as "yyzx",
        (SELECT MAX(sku.sku_total) FROM t_terminal_sku_check sku WHERE sku.terminal_shop_id = ts.id AND (sku.check_status = 1 OR sku.check_status = 4) AND create_time &gt;= #{startTime} AND create_time &lt;= #{endTime}) as currentDisplayNums,
        IFNULL((SELECT ssss.count FROM (
        SELECT MIN(sss.ind),sss.terminal_shop_id,sss.count FROM (
        SELECT CONCAT(ss.num,'_',ss.skuCount) as count,ss.terminal_shop_id,(@i:=@i+1) as ind FROM (
        SELECT
        case when (skuDatas.check_status = 1 and skuDatas.skuCount > 0) or (skuDatas.check_status = 4 and skuDatas.skuCount > 0) then 1
        when (skuDatas.check_status = 3 and skuDatas.skuCount > 0) or (skuDatas.check_status = 0 and skuDatas.checkNeedCount > 0 and skuDatas.skuCount > 0) then 2
        when skuDatas.check_status = 2 and skuDatas.skuCount > 0 then 0 else 0 end as num,
        skuDatas.terminal_shop_id,skuDatas.check_status,skuDatas.skuCount
        FROM (
        SELECT sku.terminal_shop_id,sku.check_status, COUNT(sku.id) as skuCount, SUM(IF(sku.check_need = 1, 1, 0)) as checkNeedCount FROM t_terminal_sku_check sku,t_terminal_shop_contract ct
        WHERE sku.terminal_shop_id = ct.terminal_shop_id
        AND ct.contract_code = #{contractCode}
        AND sku.create_time &gt;= #{startTime} AND sku.create_time &lt;= #{endTime} GROUP BY sku.terminal_shop_id,sku.check_status
        ) skuDatas
        ) ss ,(SELECT @i:=0) AS itable ORDER BY FIELD(ss.num, 1 ,2 ,0)
        ) sss GROUP BY sss.terminal_shop_id
        ) ssss where ssss.terminal_shop_id = ts.id), '0_0') as displayStatusAndCurrentSkuCount
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        LEFT JOIN
        t_terminal_protocol tp
        ON
        ts.id = tp.terminal_shop_id
        LEFT JOIN
        t_terminal_shop_level tsl
        ON
        tp.level_code = tsl.id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND tp.delete_status = 0
        AND tp.protocol_type = 0
        AND tp.check_status=1
        AND ts.create_time &lt;= #{endTime}
        AND tsc.contract_code = #{contractCode}
        AND tsl.id = #{shopLevel}
        <if test="storeName != null and storeName != ''">
            AND ts.shop_name LIKE CONCAT('%',#{storeName},'%')
        </if>
        <choose>
            <when test="sort != null and sort != ''">
                ORDER BY currentDisplayNums ${sort}
            </when>
            <otherwise>
                ORDER BY currentDisplayNums DESC
            </otherwise>
        </choose>
    </select>

    <select id="getTerminalSkuCheckShopListByContractCodeAndCurrentMonth" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.skuCheckStatistics.SkuCheckShopListResp">
        SELECT
        DISTINCT ts.id as "storeId",
        ts.shop_name as "storeName",
        ts.shop_type as "storeType",
        tsl.level_name as "storeLevel",
        tp.product_type as "yyzx",
        (SELECT MAX(sku.sku_total) FROM t_terminal_sku_check sku WHERE sku.terminal_shop_id = ts.id AND (sku.check_status = 1 OR sku.check_status = 4) AND create_time &gt;= #{startTime} AND create_time &lt;= #{endTime}) as currentDisplayNums,
        IFNULL((SELECT ssss.count FROM (
        SELECT MIN(sss.ind),sss.terminal_shop_id,sss.count FROM (
        SELECT CONCAT(ss.num,'_',ss.skuCount) as count,ss.terminal_shop_id,(@i:=@i+1) as ind FROM (
        SELECT
        case when (skuDatas.check_status = 1 and skuDatas.skuCount > 0) or (skuDatas.check_status = 4 and skuDatas.skuCount > 0) then 1
        when (skuDatas.check_status = 3 and skuDatas.skuCount > 0) or (skuDatas.check_status = 0 and skuDatas.checkNeedCount > 0 and skuDatas.skuCount > 0) then 2
        when skuDatas.check_status = 2 and skuDatas.skuCount > 0 then 0 else 0 end as num,
        skuDatas.terminal_shop_id,skuDatas.check_status,skuDatas.skuCount
        FROM (
        SELECT sku.terminal_shop_id,sku.check_status, COUNT(sku.id) as skuCount, SUM(IF(sku.check_need = 1, 1, 0)) as checkNeedCount FROM t_terminal_sku_check sku,t_terminal_shop_contract ct
        WHERE sku.terminal_shop_id = ct.terminal_shop_id
        AND ct.contract_code = #{contractCode}
        AND sku.create_time &gt;= #{startTime} AND sku.create_time &lt;= #{endTime} GROUP BY sku.terminal_shop_id,sku.check_status
        ) skuDatas
        ) ss ,(SELECT @i:=0) AS itable ORDER BY FIELD(ss.num, 1 ,2 ,0)
        ) sss GROUP BY sss.terminal_shop_id
        ) ssss where ssss.terminal_shop_id = ts.id), '0_0') as displayStatusAndCurrentSkuCount
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        LEFT JOIN
        t_terminal_protocol tp
        ON
        ts.id = tp.terminal_shop_id
        LEFT JOIN
        t_terminal_shop_level tsl
        ON
        tp.level_code = tsl.id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND tp.delete_status = 0
        AND tp.protocol_type = 0
        AND tp.check_status=1
        AND ts.create_Time &gt;= #{startTime}
        AND ts.create_time &lt;= #{endTime}
        AND tsc.contract_code = #{contractCode}
        AND tsl.id = #{shopLevel}
        <if test="storeName != null and storeName != ''">
            AND ts.shop_name LIKE CONCAT('%',#{storeName},'%')
        </if>
        <choose>
            <when test="sort != null and sort != ''">
                ORDER BY currentDisplayNums ${sort}
            </when>
            <otherwise>
                ORDER BY currentDisplayNums DESC
            </otherwise>
        </choose>
    </select>

    <select id="getTerminalSkuCheckShopListNotExistSkuCheckPass" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.skuCheckStatistics.SkuCheckShopListResp">
        select * from (
        SELECT
        DISTINCT ts.id as "storeId",
        ts.shop_name as "storeName",
        ts.shop_type as "storeType",
        tsl.level_name as "storeLevel",
        tp.product_type as "yyzx",
        (SELECT COUNT(sku.id) FROM t_terminal_sku_check sku WHERE sku.terminal_shop_id = ts.id AND sku.create_time &gt;= #{startTime} AND sku.create_time &lt;= #{endTime}  AND ((sku.check_status = 0 AND sku.check_need = 1) OR sku.check_status = 1 OR sku.check_status = 4 OR sku.check_status = 3)) as skuCount,
        (SELECT COUNT(sku.id) FROM t_terminal_sku_check sku WHERE sku.terminal_shop_id = ts.id AND sku.create_time &gt;= #{startTime} AND sku.create_time &lt;= #{endTime}  AND sku.check_status = 2) as currentSkuCount
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        LEFT JOIN
        t_terminal_protocol tp
        ON
        ts.id = tp.terminal_shop_id
        LEFT JOIN
        t_terminal_shop_level tsl
        ON
        tp.level_code = tsl.id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND tp.delete_status = 0
        AND tp.protocol_type = 0
        AND tp.check_status=1
        AND tsc.contract_code = #{contractCode}
        <if test="endTime != null and endTime != ''">
            AND ts.create_time &lt;= #{endTime}
        </if>
        <if test="shopLevel != null and shopLevel != ''">
            AND tsl.id = #{shopLevel}
        </if>
        <if test="storeName != null and storeName != ''">
            AND ts.shop_name LIKE CONCAT('%',#{storeName},'%')
        </if>
        <choose>
            <when test="sort != null and sort != ''">
                ORDER BY ts.create_time ${sort}
            </when>
            <otherwise>
                ORDER BY ts.create_time DESC
            </otherwise>
        </choose>
                      ) datas
        where datas.skuCount = 0
    </select>

    <select id="getTerminalSkuCheckShopListExistSkuCheckAuditIn" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.skuCheckStatistics.SkuCheckShopListResp">
        select * from (
        SELECT
        DISTINCT ts.id as "storeId",
        ts.shop_name as "storeName",
        ts.shop_type as "storeType",
        tsl.level_name as "storeLevel",
        tp.product_type as "yyzx",
        (SELECT COUNT(sku.id) FROM t_terminal_sku_check sku WHERE sku.terminal_shop_id = ts.id AND sku.create_time &gt;= #{startTime} AND sku.create_time &lt;= #{endTime} AND ((sku.check_status = 0 AND sku.check_need = 1) OR sku.check_status = 3)) as currentSkuCount,
        (SELECT COUNT(sku.id) FROM t_terminal_sku_check sku WHERE sku.terminal_shop_id = ts.id AND sku.create_time &gt;= #{startTime} AND sku.create_time &lt;= #{endTime} AND (sku.check_status = 1 OR sku.check_status = 4)) as skuPassCount
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        LEFT JOIN
        t_terminal_protocol tp
        ON
        ts.id = tp.terminal_shop_id
        LEFT JOIN
        t_terminal_shop_level tsl
        ON
        tp.level_code = tsl.id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND tp.delete_status = 0
        AND tp.protocol_type = 0
        AND tp.check_status=1
        AND tsc.contract_code = #{contractCode}
        <if test="endTime != null and endTime != ''">
            AND ts.create_time &lt;= #{endTime}
        </if>
        <if test="shopLevel != null and shopLevel != ''">
            AND tsl.id = #{shopLevel}
        </if>
        <if test="storeName != null and storeName != ''">
            AND ts.shop_name LIKE CONCAT('%',#{storeName},'%')
        </if>
        <choose>
            <when test="sort != null and sort != ''">
                ORDER BY ts.create_time ${sort}
            </when>
            <otherwise>
                ORDER BY ts.create_time DESC
            </otherwise>
        </choose>
                      ) datas
        where datas.currentSkuCount > 0 AND datas.skuPassCount = 0
    </select>

    <select id="getTerminalSkuCheckShopListExistSkuCheckPassByMaxSkuTotal" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.skuCheckStatistics.SkuCheckShopListResp">
        select  * from (
        SELECT
        DISTINCT ts.id as "storeId",
        ts.shop_name as "storeName",
        ts.shop_type as "storeType",
        tsl.level_name as "storeLevel",
        tp.product_type as "yyzx",
        (SELECT MAX(sku.sku_total) FROM t_terminal_sku_check sku WHERE sku.terminal_shop_id = ts.id AND (sku.check_status = 1 OR sku.check_status = 4) AND create_time &gt;= #{startTime} AND create_time &lt;= #{endTime}) as currentDisplayNums,
        IFNULL((SELECT ssss.count FROM (
        SELECT MIN(sss.ind),sss.terminal_shop_id,sss.count FROM (
        SELECT CONCAT(ss.num,'_',ss.skuCount) as count,ss.terminal_shop_id,(@i:=@i+1) as ind FROM (
        SELECT
        case when (skuDatas.check_status = 1 and skuDatas.skuCount > 0) or (skuDatas.check_status = 4 and skuDatas.skuCount > 0) then 1
        when (skuDatas.check_status = 3 and skuDatas.skuCount > 0) or (skuDatas.check_status = 0 and skuDatas.checkNeedCount > 0 and skuDatas.skuCount > 0) then 2
        when skuDatas.check_status = 2 and skuDatas.skuCount > 0 then 0 else 0 end as num,
        skuDatas.terminal_shop_id,skuDatas.check_status,skuDatas.skuCount
        FROM (
        SELECT sku.terminal_shop_id,sku.check_status, COUNT(sku.id) as skuCount, SUM(IF(sku.check_need = 1, 1, 0)) as checkNeedCount FROM t_terminal_sku_check sku,t_terminal_shop_contract ct
        WHERE sku.terminal_shop_id = ct.terminal_shop_id
        AND ct.contract_code = #{contractCode}
        AND sku.create_time &gt;= #{startTime} AND sku.create_time &lt;= #{endTime} GROUP BY sku.terminal_shop_id,sku.check_status
        ) skuDatas
        ) ss ,(SELECT @i:=0) AS itable ORDER BY FIELD(ss.num, 1 ,2 ,0)
        ) sss GROUP BY sss.terminal_shop_id
        ) ssss where ssss.terminal_shop_id = ts.id), '0_0') as displayStatusAndCurrentSkuCount
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        LEFT JOIN
        t_terminal_protocol tp
        ON
        ts.id = tp.terminal_shop_id
        LEFT JOIN
        t_terminal_shop_level tsl
        ON
        tp.level_code = tsl.id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND tp.delete_status = 0
        AND tp.protocol_type = 0
        AND tp.check_status=1
        AND tsc.contract_code = #{contractCode}
        <if test="endTime != null and endTime != ''">
            AND ts.create_time &lt;= #{endTime}
        </if>
        <if test="shopLevel != null and shopLevel != ''">
            AND tsl.id = #{shopLevel}
        </if>
        <if test="storeName != null and storeName != ''">
            AND ts.shop_name LIKE CONCAT('%',#{storeName},'%')
        </if>
        <choose>
            <when test="sort != null and sort != ''">
                ORDER BY currentDisplayNums ${sort}
            </when>
            <otherwise>
                ORDER BY currentDisplayNums DESC
            </otherwise>
        </choose>
        ) datass where datass.displayStatusAndCurrentSkuCount like '1%'
    </select>

    <select id="getShopProtocolList" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.skuCheckStatistics.DisplayDataResp">
        SELECT
        CASE tp.protocol_type
        WHEN 0 THEN "主协议"
        WHEN 1 THEN "附加协议"
        ELSE ""
        END as "agreementType",
        tsl.level_name as "agreementLevel",
        tp.effective_time as "takeEffectDate",
        IFNULL(tpp.display_surface, 0) as "displayAsk"
        FROM
        t_terminal_protocol tp
        LEFT JOIN
        t_terminal_product_protocol tpp
        ON
        tp.product_protocol_config_id = tpp.id
        LEFT JOIN
        t_terminal_shop_level tsl
        ON
        tp.level_code = tsl.id
        WHERE
        tp.delete_status = 0
        AND tp.terminal_shop_id = #{shopId}
        AND tp.effective_time &lt;= #{fifteenTime}
        ORDER BY tp.create_time ASC
    </select>

    <select id="checkExist" resultType="com.intelliquor.cloud.shop.terminal.model.TerminalShopModel">
        select ts.*
        from t_terminal_shop ts
                 left join t_terminal_shop_contract tsc ON ts.id = tsc.terminal_shop_id
        where ts.status = 1 and ts.is_delete = 0
        and ts.license_code = #{licenseCode}
        and tsc.contract_code = #{contractCode}
    </select>
    <select id="approximate" resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalShopResp">
        select ts.*
        from t_terminal_shop ts
        left join t_terminal_shop_contract tsc ON ts.id = tsc.terminal_shop_id
        where ts.status = 1 and ts.is_delete = 0 and ts.merge_type in (0, 1)
        and ts.license_code = #{licenseCode}
        and tsc.contract_code = #{contractCode}
        and ts.leader_phone = #{leaderPhone}
    </select>

    <select id="selectTerminalExistList" resultType="com.intelliquor.cloud.shop.terminal.model.TerminalShopModel">
        select ms.*,sum(tsd.quantity) as storeTotalInNum
        from t_terminal_shop ms
                 left join t_terminal_shop_contract tsc ON ms.id = tsc.terminal_shop_id
                 left join t_terminal_scan_detail  tsd ON (tsd.shop_id = ms.member_shop_id and tsd.is_delete=0)
        where ms.status = 1
          and ms.is_delete = 0
        <if test="contractCode != null and contractCode != ''">
            and tsc.contract_code = #{contractCode}
        </if>
        <if test="licenseCode != null and licenseCode != ''">
            and ms.license_code = #{licenseCode}
        </if>
        <if test="phone != null and phone != ''">
            and ms.leader_phone = #{phone}
        </if>
        group by ms.id
    </select>

    <select id="findVisitShopListStatistics" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.visitStatistics.VisitStatisticsResp">
        SELECT
        COUNT(DISTINCT datas.tsId) as storeNums,
        IFNULL(SUM(CASE WHEN datas.createTime &lt;= #{endTime} THEN 1 ELSE 0 END), 0) as currentStoreNums,
        IFNULL(SUM(CASE WHEN datas.createTime &lt;= #{endTime} AND datas.createTime &gt;= #{startTime} THEN 1 ELSE 0 END), 0) as currentAddNums,
        datas.shopType as storeType,
        IFNULL(SUM(datas.visitCount), 0) as currentVisitNums,
        IFNULL(SUM(CASE WHEN datas.visitCount > 0 THEN 1 ELSE 0 END), 0) as currentVisitStores,
        IFNULL(SUM(CASE WHEN datas.visitCount = 0 THEN 1 ELSE 0 END), 0) as currentNotVisitStores
        FROM (
        SELECT
        ts.id as tsId,
        ts.create_time as createTime,
        ts.shop_type as shopType,
        (SELECT COUNT(visit.id) FROM t_terminal_visit_record visit WHERE visit.shop_id = ts.member_shop_id AND visit.create_time &lt;= #{endTime} AND visit.create_time &gt;= #{startTime}) as visitCount
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND ts.shop_type NOT IN(5, 6, 7, 8)
        AND tsc.contract_code = #{contractCode}
        ) datas GROUP BY datas.shopType
    </select>

    <select id="findSkuCheckShopListStatistics" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.skuCheckStatistics.SkuCheckStatisticsResp">
        SELECT
        datas.shopType as agreementType,
        COUNT(DISTINCT datas.tsId) as storeNums,
        IFNULL(SUM(CASE WHEN datas.createTime &lt;= #{endTime} THEN 1 ELSE 0 END), 0) as currentStoreNums,
        IFNULL(SUM(CASE WHEN datas.createTime &lt;= #{endTime} AND datas.createTime &gt;= #{startTime} THEN 1 ELSE 0 END), 0) as currentAddNums,
        IFNULL(SUM(CASE WHEN datas.skuPassCount = 0 AND datas.skuCount = 0 THEN 1 ELSE 0 END), 0) as auditFailStores,
        IFNULL(SUM(CASE WHEN datas.skuPassCount = 0 AND datas.skuCount > 0 THEN 1 ELSE 0 END), 0) as auditInStores,
        IFNULL(SUM(datas.skuTotal),0) as auditDisplayNums
        FROM
        (
        SELECT
        ts.id as tsId,
        ts.create_time as createTime,
        tsl.id as shopType,
        (SELECT COUNT(sku.id) FROM t_terminal_sku_check sku WHERE sku.terminal_shop_id = ts.id AND sku.create_time &gt;= #{startTime} AND sku.create_time &lt;= #{endTime} AND ((sku.check_status = 0 AND sku.check_need = 1) OR sku.check_status = 3)) as skuCount,
        (SELECT COUNT(sku.id) FROM t_terminal_sku_check sku WHERE sku.terminal_shop_id = ts.id AND sku.create_time &gt;= #{startTime} AND sku.create_time &lt;= #{endTime} AND (sku.check_status = 1 OR sku.check_status = 4)) as skuPassCount,
        (SELECT MAX(sku.sku_total) FROM t_terminal_sku_check sku WHERE sku.terminal_shop_id = ts.id AND sku.create_time &gt;= #{startTime} AND sku.create_time &lt;= #{endTime} AND (sku.check_status = 1 OR sku.check_status = 4)) as skuTotal
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        LEFT JOIN
        t_terminal_protocol tp
        ON
        ts.id = tp.terminal_shop_id
        LEFT JOIN
        t_terminal_shop_level tsl
        ON
        tp.level_code = tsl.id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND tp.delete_status = 0
        AND tp.protocol_type = 0
        AND tp.check_status=1
        AND tsc.contract_code = #{contractCode}
        ) datas GROUP BY datas.shopType
    </select>

    <select id="findNowStatstics" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.channelStatistics.ChannelShopStatisticsResp">
        SELECT
        COUNT(datas.tsId) as currentStoreNums,
        IFNULL(SUM(datas.currentVisitNums), 0) as currentVisitNums,
        IFNULL(SUM(CASE WHEN datas.protocolProperty = 1 OR datas.protocolProperty = 0 THEN 1 ELSE 0 END), 0) as agreementStoreNums,
        IFNULL(SUM(CASE WHEN (datas.protocolProperty = 1 AND datas.skuCheckCount > 0) OR (datas.protocolProperty = 0  AND datas.skuCheckCount > 0) THEN 1 ELSE 0 END), 0) as alreadyDisplayNums,
        IFNULL(SUM(datas.skuTotal), 0) as auditDisplayNums
        FROM (
        SELECT
        DISTINCT ts.id as tsId,
        (SELECT COUNT(visit.id) FROM t_terminal_visit_record visit WHERE visit.shop_id = ts.member_shop_id AND visit.create_time &gt;= #{startTime} AND visit.create_time &lt;= #{endTime}) as currentVisitNums,
        (SELECT tp.protocol_property FROM t_terminal_protocol tp WHERE tp.delete_status = 0 AND ts.id = tp.terminal_shop_id ORDER BY tp.create_time DESC LIMIT 1) as protocolProperty,
        (SELECT COUNT(sku.id) FROM t_terminal_sku_check sku WHERE sku.terminal_shop_id = ts.id AND sku.create_time &gt;= #{startTime} AND sku.create_time &lt;= #{endTime}) as skuCheckCount,
        (SELECT IFNULL(MAX(sku.sku_total),0) FROM t_terminal_sku_check sku WHERE sku.terminal_shop_id = ts.id AND (check_status = 1 OR check_status = 4) AND sku.create_time &gt;= #{startTime} AND sku.create_time &lt;= #{endTime}) as skuTotal
        FROM
        t_terminal_shop ts
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND ts.shop_type NOT IN(5, 6, 7, 8)
        AND tsc.contract_code = #{contractCode}
        AND ts.create_time &lt;= #{endTime}
        GROUP BY ts.id
        ) datas
    </select>

    <select id="getActivateListByTmId" resultType="com.intelliquor.cloud.shop.terminal.model.TerminalShopModel">
        select
            tts.*
        from
            t_terminal_task_monthly tttm
                join t_terminal_task_receive tttr on
                        tttr.tp_id = tttm.tp_id
                    and tttr.delete_flag = 0
                join t_terminal_shop tts on
                        tts.create_user = tttr.broker_id
                    and tts.is_delete = 0
                    and tts.create_time BETWEEN DATE(#{startTime}) and DATE(#{endTime})
--             与智赢沟通，终端审核状态从t_terminal_shop_info_schedule表中获取
--             join t_terminal_shop_node ttsn on
--             ttsn.terminal_shop_id = tts.id
--             and ttsn.node_type = 1
--             and ttsn.is_back = 0
--             and ttsn.node_status = 1
            join t_terminal_shop_info_schedule ttsis on
            ttsis.terminal_shop_id = tts.id and ttsis.status = 1
        where
            tttm.id = #{tmId}
          and tttm.delete_flag = 0
        order by
            tts.create_user desc
    </select>

    <select id="selectTerminalSimilarList" resultType="com.intelliquor.cloud.shop.terminal.model.TerminalShopModel">

        select ts.* from
            t_terminal_shop ts
                left join t_member_shop ms ON ts.member_shop_id = ms.id
                left join t_terminal_shop_contract tsc ON ts.id = tsc.terminal_shop_id
            where <![CDATA[ts.id <> #{id} ]]>
            and tsc.contract_code = #{contractCode}
            and (ts.leader_phone = #{leaderPhone}
                <if test="licenseCode != null and licenseCode != ''">
                    or ts.license_code = #{licenseCode}
                </if>
                )
            and ts.is_delete = 0
            AND ts.STATUS = 1
            AND ts.merge_type = 0
            and ms.status = 0
    </select>

    <select id="selectTerminalShopModifyByShopId"
            resultType="com.intelliquor.cloud.shop.common.model.TerminalShopModifyModel">
        select
        ts.id,
        ms.id as memberShopId,
        ms.name as shopName,
        ts.shop_type,
        ts.address,
        ts.leader_name,
        ts.leader_phone,
        tsc.contract_type,
        tsc.contract_code,
        ct.contract_name,
        cdi.dealer_code,
        cdi.dealer_name
        from
        t_terminal_shop ts
        left join
        t_member_shop ms on ts.member_shop_id = ms.id
        left join
        t_terminal_shop_contract tsc on tsc.terminal_shop_id = ts.id
        left join
        t_cloud_dealer_info cdi on cdi.dealer_code = tsc.dealer_code
        left join
        t_contract_type ct on ct.contract_type = tsc.contract_type
        where
        ms.id = #{shopId}
        and
        ts.status = 1
        and
        ts.is_delete = 0
    </select>

    <select id="selectDistributorsShopModifyByShopId"
            resultType="com.intelliquor.cloud.shop.common.model.TerminalShopModifyModel">
        select
        ms.id as memberShopId,
        ms.name as shopName,
        ms.address,
        ms.linkman as leader_name,
        ms.linkphone as leader_phone,
        cdid.contract_type,
        cdid.contract_no as contractCode,
        ct.contract_name,
        cdi.dealer_code,
        cdi.dealer_name
        from
        t_member_shop ms
        left join
        t_cloud_dealer_info_detail cdid on cdid.dealer_code = ms.dealer_code
        left join
        t_cloud_dealer_info cdi on cdid.parent_dealer_code = cdi.dealer_code
        left join
        t_contract_type ct on ct.contract_type = cdid.contract_type
        where
        ms.id = #{shopId}
    </select>

    <select id="selectCopartnerShopModifyByShopId"
            resultType="com.intelliquor.cloud.shop.common.model.TerminalShopModifyModel">
        select
        ms.id as memberShopId,
        ms.name as shopName,
        ms.address,
        ms.linkman as leader_name,
        ms.linkphone as leader_phone,
        cdi.contract_type,
        cdi.contract_code,
        ct.contract_name,
        cdii.dealer_code,
        cdii.dealer_name
        from
        t_member_shop ms
        left join
        t_cloud_dealer_info cdi on ms.dealer_code = cdi.dealer_code
        left join
        t_cloud_dealer_info cdii on cdii.id = cdi.superior_id
        left join
        t_contract_type ct on ct.contract_type = cdi.contract_type
        where
        ms.id = #{shopId}
    </select>

    <select id="selectTerminalModifyShopList" resultType="com.intelliquor.cloud.shop.common.model.TerminalShopModifyModel">
        select
            ms.id as memberShopId,ms.`name` as shopName,ms.dealer_code as deputyCode,ms.main_code,ms.is_member,cdi.account_type
        FROM t_member_shop ms
                 LEFT JOIN t_cloud_dealer_info cdi ON cdi.dealer_code = ms.dealer_code
                 LEFT JOIN t_cloud_dealer_relation tcdi ON cdi.id = tcdi.dealer_id
                 LEFT join t_cloud_dealer_info di on tcdi.parent_id = di.id
                 LEFT JOIN t_terminal_shop ts ON ms.id = ts.member_shop_id
                 LEFT JOIN t_terminal_shop_contract tsc ON ts.id = tsc.terminal_shop_id
        where  di.dealer_code = #{dealerCode} and   tsc.contract_code = #{contractCode} and  ms.is_member = #{isMember} and cdi.account_type = 5
    </select>
    <select id="selectDistributorsModifyShopList" resultType="com.intelliquor.cloud.shop.common.model.TerminalShopModifyModel">
        select
            ms.id as memberShopId,ms.`name` as shopName,ms.dealer_code as deputyCode,ms.main_code,ms.is_member,cdi.account_type
        FROM t_member_shop ms
                 LEFT JOIN t_cloud_dealer_info cdi ON cdi.dealer_code = ms.dealer_code
                 LEFT JOIN t_cloud_dealer_info_detail cdid ON cdi.id = cdid.dealer_id
                 LEFT JOIN t_cloud_dealer_relation tcdi ON cdi.id = tcdi.dealer_id
                 LEFT JOIN t_cloud_dealer_info cdii ON cdii.id = tcdi.parent_id
        where cdii.dealer_code =  #{dealerCode} and cdid.contract_no = #{contractCode} and  ms.is_member = 0 and cdi.account_type = 3
    </select>
    <select id="selectCopartnerModifyShopList" resultType="com.intelliquor.cloud.shop.common.model.TerminalShopModifyModel">
        select s.id as memberShopId ,s.`name` as shopName,s.dealer_code as deputyCode,s.main_code,s.is_member,d.account_type
        from t_cloud_dealer_info d
                 inner join t_cloud_dealer_relation r on d.id = r.dealer_id
                 left join t_cloud_dealer_info di on r.parent_id = di.id
                 INNER JOIN t_member_shop s on d.dealer_code = s.dealer_code
        where di.dealer_code= #{dealerCode}  and d.contract_code = #{contractCode}
    </select>
    <select id="selectDistriAndCopaTerminalModifyShopList" resultType="com.intelliquor.cloud.shop.common.model.TerminalShopModifyModel">
        select
            ms.id AS memberShopId,
            ms.`name` AS shopName,
            ms.dealer_code AS deputyCode,
            ms.main_code,
            ms.is_member,
            cdi.account_type,
            1 as isZhiShu,
            msi.id AS distributorId
        from t_member_shop ms
        left join t_cloud_dealer_info cdi ON ms.dealer_code = cdi.dealer_code
        left join t_cloud_dealer_relation r on cdi.id = r.dealer_id
        left join t_cloud_dealer_info cdii on r.parent_id = cdii.id
        left join t_member_shop msi on msi.dealer_code = cdii.dealer_code
        where
            1=1
            and ms.is_member = #{isMember}
            <if test="idList != null and idList.size > 0">
            and cdii.dealer_code in
                <foreach collection="idList" separator="," open="(" close=")" item="id">
                    #{id}
                </foreach>
            </if>
    </select>

    <select id="selectExistModifyShopList" resultType="java.lang.Integer">
        select member_shop_id
        from
            t_terminal_shop_modify_record
        where
        modify_type = #{modifyType}
        and create_time &gt;= #{currYearStart}
        and create_time &lt;= #{currYearEnd}
        <if test="shopIds != null and shopIds.size > 0">
            and member_shop_id in
            <foreach collection="shopIds" separator="," open="(" close=")" item="id">
                #{id}
            </foreach>
        </if>
    </select>

    <select id ="selectMemberShop" resultType="com.intelliquor.cloud.shop.common.model.resp.MemberShopResp">
        select *
        from t_member_shop
        where status = 0 and dealer_code = #{dealerCode}
    </select>

    <select id="getTerminalListByPage" resultType="com.intelliquor.cloud.shop.terminal.model.resp.DistributionTerminalShopResp">
        SELECT
            ms.id as shopId,
            tsl.level_name as levelName,
            ms.`name` as shopName,
            ts.type as shopType,
            ts.merge_type as mergeType,
            ts.address as address,
            ms.linkman as linkman,
            ms.linkphone as linkphone,
            ts.main_code as mainCode,
            ts.deputy_code as deputyCode,
            parentDealer.dealer_name as dealerName,
            contract.contract_code as contractCode,
            contract.contract_type as contractType,
            tam.`name` as createUserName,
            ts.create_time as createTime
        FROM
            t_member_shop ms
                LEFT JOIN
            t_terminal_shop ts
            ON
                ts.member_shop_id = ms.id
                LEFT JOIN
            t_terminal_shop_contract contract
            ON
                contract.member_shop_id = ms.id
                LEFT JOIN
            t_terminal_account_manager tam
            ON
                ts.create_user = tam.id
            LEFT JOIN
            t_terminal_shop_level tsl
            ON
            ts.level_code = tsl.id
                LEFT JOIN
            t_cloud_dealer_info dealer
            ON
                ms.dealer_code = dealer.dealer_code
                LEFT JOIN
            t_cloud_dealer_relation rel
            ON
                rel.dealer_id = dealer.id
                LEFT JOIN
            t_cloud_dealer_relation parentRel
            ON
                rel.parent_id = parentRel.dealer_id
                LEFT JOIN
            t_cloud_dealer_info parentDealer
            ON
                parentRel.dealer_id = parentDealer.id
        WHERE
            dealer.account_type = 5
          AND ms.is_member = 0
          AND rel.parent_id = #{distributionId}
        <if test="shopName != null and shopName != ''">
            AND ms.`name` LIKE CONCAT('%',#{shopName},'%')
        </if>
        <if test="shopType != null and shopType != ''">
            AND ts.type = #{shopType}
        </if>
    </select>

    <select id="getDistributionTerminalCount" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM
            t_cloud_dealer_relation a
        LEFT JOIN
            t_cloud_dealer_info b
        ON
            a.dealer_id = b.id
        WHERE
            b.account_type = 5
          AND a.parent_id = #{distributionId}
    </select>

    <update id="updateDistributionStatus">
        update t_cloud_dealer_info set status = #{status} where id = #{distributionId}
    </update>

    <select id="disposeDistributionAccountManagerList" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT
            ms.id
        FROM
            t_member_shop ms
                LEFT JOIN t_cloud_dealer_info dis ON ms.dealer_code = dis.dealer_code
                LEFT JOIN t_cloud_dealer_relation rel ON rel.dealer_id = dis.id
                LEFT JOIN t_terminal_account_manager_dealer md ON rel.parent_id = md.dealer_id
        WHERE
            dis.account_type = 3
          AND md.account_manager_id = #{userId}
        UNION ALL
        SELECT
            shop.id
        FROM
            t_member_shop shop
                LEFT JOIN t_cloud_dealer_info shopDealer ON shop.dealer_code = shopDealer.dealer_code
                LEFT JOIN t_cloud_dealer_relation shopRel ON shopDealer.id = shopRel.dealer_id
                LEFT JOIN t_cloud_dealer_info dis ON dis.id = shopRel.parent_id
                LEFT JOIN t_member_shop ms ON ms.dealer_code = dis.dealer_code
                LEFT JOIN t_cloud_dealer_relation rel ON rel.dealer_id = dis.id
                LEFT JOIN t_terminal_account_manager_dealer md ON rel.parent_id = md.dealer_id
        WHERE
            dis.account_type = 3
          AND md.account_manager_id = #{userId}
    </select>

    <select id="getMemberShopByIds" resultType="com.intelliquor.cloud.shop.terminal.model.MemberShopModel">
        select
            ms.id,
            ms.name
        from
            t_member_shop ms
        LEFT JOIN
            t_terminal_shop ts
        ON
            ts.member_shop_id = ms.id
        where ts.id in
        <foreach collection="selectData" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="getMemberShopByDistribution" resultType="com.intelliquor.cloud.shop.terminal.model.MemberShopModel">
        SELECT
            ms.id,
            ms.name
        FROM
            t_member_shop ms
                LEFT JOIN
            t_cloud_dealer_info dealer
            ON
                ms.dealer_code = dealer.dealer_code
                LEFT JOIN
            t_cloud_dealer_relation rel
            ON
                rel.dealer_id = dealer.id
                LEFT JOIN
            t_terminal_account_manager_dealer d
            ON
                d.dealer_id = rel.parent_id
        WHERE
            d.account_manager_id = #{id}
          AND account_type = 3
          AND dealer.is_delete = 1
          AND ms.status = 0
    </select>
    <select id="selectTerminalExistListByContractType" resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalShopResp">
        select ms.*,tms.status as terminalStatus
        from t_terminal_shop ms
        left join t_terminal_shop_contract tsc ON ms.id = tsc.terminal_shop_id
        left join t_terminal_shop_info_schedule tsis on ms.id = tsis.terminal_shop_id
        left join t_member_shop tms on ms.member_shop_id = tms.id
        where
          tsis.status in (1,2,4)
          and ms.is_delete = 0
          and ms.shop_type in (0,3)
          and ms.merge_type in (0,1)
          and tsc.contract_type = #{contractType}
          and ms.license_code = #{licenseCode}
        <if test="id != null and id != ''">
            AND ms.id != #{id}
        </if>
          and ms.is_license_success = 1
    </select>

    <select id="selectTerminalShopListByTerminalShopIds" resultType="com.intelliquor.cloud.shop.terminal.model.TerminalShopModel">
        select
        tts.*
        from t_terminal_shop tts
        left join t_member_shop tms on tms.id = tts.member_shop_id
        left join t_terminal_shop_clear_license ttscl on ttscl.id = tts.id
        where tts.status = 1 -- 已激活
        and tts.is_delete = 0 -- 未删除
        and tts.is_license_success = 0 -- 营业执照校验功能上线后，已经校验逻辑并且成功的不再执行本次逻辑
        and tts.whether_license = 1 -- 有无营业执照 0无 1有  选择有的才走清洗逻辑
        and tts.license_img is not null -- 判断有图片
        and tts.license_img != ""
        and tms.is_member = 0 -- 不是高端会员
        and tms.status != 1 -- 不是已禁用终端
        and ttscl.id is null -- 判断未清洗过
        and tts.shop_type != 2 -- 不是团购终端
        and tts.create_user not in (688,689,690,691,692,693,694,695,1610,1707,2369,2370,2453,3194,3214,3225,3607,3914,3995,4145,4159,4596,4774,4933,5224,5881,5964,6752,8401,8422,8729,8773,8916,8927,8930,8940,9211,10418,10421,10422,10423,10424,10426) -- 非测试终端
        <if test="terminalShopIds != null and terminalShopIds.size>0">
            and tts.id IN
              <foreach collection="terminalShopIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
              </foreach>
          </if>
        <if test="terminalSize != '' and terminalSize != null">
            limit #{terminalSize}
        </if>
    </select>

    <select id="selectTerminalExistListByContractTypeByHotel" resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalShopResp">
        select ms.*,tms.status as terminalStatus
        from t_terminal_shop ms
        left join t_terminal_shop_contract tsc ON ms.id = tsc.terminal_shop_id
        left join t_terminal_shop_info_schedule tsis on ms.id = tsis.terminal_shop_id
        left join t_member_shop tms on ms.member_shop_id = tms.id
        where
        tsis.status in (1,2,4)
        and ms.is_delete = 0
        and ms.shop_type in (10,11,12,13)
        and ms.merge_type in (0,1)
        and tsc.contract_type = #{contractType}
        and ms.license_code = #{licenseCode}
        <if test="id != null and id != ''">
            AND ms.id != #{id}
        </if>
        and ms.is_license_success = 1
    </select>
    <select id="selectTerminalExistListByDealerCodeAndHotel" resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalShopResp">
        select ms.*,tms.status as terminalStatus
        from t_terminal_shop ms
        left join t_terminal_shop_contract tsc ON ms.id = tsc.terminal_shop_id
        left join t_terminal_shop_info_schedule tsis on ms.id = tsis.terminal_shop_id
        left join t_member_shop tms on ms.member_shop_id = tms.id
        where
        tsis.status in (1,2,4)
        and ms.is_delete = 0
        and ms.shop_type in (10,11,12,13)
        and ms.merge_type in (0,1)
        and tsc.dealer_code = #{dealerCode}
        and ms.license_code = #{licenseCode}
        <if test="id != null and id != ''">
            AND ms.id != #{id}
        </if>
        and ms.is_license_success = 1
    </select>
    <select id="selectTerminalExistListByDealerCode" resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalShopResp">
        select ms.*,tms.status as terminalStatus
        from t_terminal_shop ms
        left join t_terminal_shop_contract tsc ON ms.id = tsc.terminal_shop_id
        left join t_terminal_shop_info_schedule tsis on ms.id = tsis.terminal_shop_id
        left join t_member_shop tms on ms.member_shop_id = tms.id
        where
        tsis.status in (1,2,4)
        and ms.is_delete = 0
        and ms.shop_type in (0,3)
        and ms.merge_type in (0,1)
        and tsc.dealer_code = #{dealerCode}
        and ms.license_code = #{licenseCode}
        <if test="id != null and id != ''">
            AND ms.id != #{id}
        </if>
        and ms.is_license_success = 1
    </select>
    <select id="selectHotelTerminalExistByLicense" resultType="com.intelliquor.cloud.shop.terminal.model.TerminalShopModel">
        select ms.*
        from t_terminal_shop ms
        left join t_terminal_shop_info_schedule tsis on ms.id = tsis.terminal_shop_id
        where
        tsis.status in (1,2,4)
        and ms.is_delete = 0
        and ms.shop_type in (10,11,12,13)
        and ms.merge_type in (0,1)
        and ms.license_code = #{licenseCode}
        <if test="id != null and id != ''">
            AND ms.id != #{id}
        </if>
        and ms.is_license_success = 1
    </select>
    <select id="selectNotHotelTerminalExistByLicense" resultType="com.intelliquor.cloud.shop.terminal.model.TerminalShopModel">
        select ms.*
        from t_terminal_shop ms
        left join t_terminal_shop_info_schedule tsis on ms.id = tsis.terminal_shop_id
        where
        tsis.status in (1,2,4)
        and ms.is_delete = 0
        and ms.shop_type in (0,3,4,14)
        and ms.merge_type in (0,1)
        and ms.license_code = #{licenseCode}
        and ms.shop_type != #{shopType}
        <if test="id != null and id != ''">
            AND ms.id != #{id}
        </if>
        and ms.is_license_success = 1
    </select>
    <select id="selectIsHotelTerminalExistByLicense" resultType="com.intelliquor.cloud.shop.terminal.model.TerminalShopModel">
        select ms.*
        from t_terminal_shop ms
        left join t_terminal_shop_info_schedule tsis on ms.id = tsis.terminal_shop_id
        where
        tsis.status in (1,2,4)
        and ms.is_delete = 0
        and ms.shop_type in (10,11,12,13)
        and ms.merge_type in (0,1)
        and ms.license_code = #{licenseCode}
        and ms.shop_type != #{shopType}
        <if test="id != null and id != ''">
            AND ms.id != #{id}
        </if>
        and ms.is_license_success = 1
    </select>
    <select id="selectHotelTerminalExistListByShopType" resultType="com.intelliquor.cloud.shop.terminal.model.TerminalShopModel">
        select ms.*
        from t_terminal_shop ms
        left join t_terminal_shop_info_schedule tsis on ms.id = tsis.terminal_shop_id
        where
        tsis.status in (1,2,4)
        and ms.is_delete = 0
        and ms.shop_type in (10,11,12,13)
        and ms.merge_type in (0,1)
        and ms.license_code = #{licenseCode}
        <if test="shopType != null and shopType != ''">
            AND ms.shop_type != #{shopType}
        </if>
        <if test="id != null and id != ''">
            AND ms.id != #{id}
        </if>
        and ms.is_license_success = 1
    </select>
    <select id="selectHotelTerminalExistListByContractCode" resultType="com.intelliquor.cloud.shop.terminal.model.TerminalShopModel">
        select ms.*
        from t_terminal_shop ms
        left join t_terminal_shop_contract tsc ON ms.id = tsc.terminal_shop_id
        left join t_terminal_shop_info_schedule tsis on ms.id = tsis.terminal_shop_id
        where
        tsis.status in (1,2,4)
        and ms.is_delete = 0
        and ms.shop_type in (10,11,12,13)
        and ms.merge_type in (0,1)
        and tsc.contract_code = #{contractCode}
        and ms.license_code = #{licenseCode}
        <if test="id != null and id != ''">
            AND ms.id != #{id}
        </if>
        and ms.is_license_success = 1
    </select>
    <select id="selectNotHotelTerminalExistListByContractCode" resultType="com.intelliquor.cloud.shop.terminal.model.TerminalShopModel">
        select ms.*
        from t_terminal_shop ms
        left join t_terminal_shop_contract tsc ON ms.id = tsc.terminal_shop_id
        left join t_terminal_shop_info_schedule tsis on ms.id = tsis.terminal_shop_id
        where
        tsis.status in (1,2,4)
        and ms.is_delete = 0
        and ms.shop_type in (0,3)
        and ms.merge_type in (0,1)
        and tsc.contract_code = #{contractCode}
        and ms.license_code = #{licenseCode}
        <if test="id != null and id != ''">
            AND ms.id != #{id}
        </if>
        and ms.is_license_success = 1
    </select>
    <select id="selectClearTerminalShopList" resultType="com.intelliquor.cloud.shop.terminal.model.TerminalShopModel">
        SELECT
            tts.*
        FROM
            t_terminal_shop tts
            join t_terminal_shop_info_schedule ttsis on ttsis.terminal_shop_id = tts.id
            join t_member_shop tms on tms.id = tts.member_shop_id
            left join t_terminal_shop_clear_main_code ttscmc on ttscmc.id = tts.id
        WHERE
            tts.is_delete = 0
          and tts.merge_type = 0
          and ttsis.status = 1
          and tms.status = 0
          and tms.is_member = 0
          and tts.license_code not like "%FailInRecog%"
          and tts.license_code is not NULL
          and tts.license_code != ""
          and ttscmc.id is null
          and tts.shop_type in (0,3)
        order by tts.update_time desc, tts.id desc
    </select>
    <select id="selectActivedAndNotMergedTerminalShopModelByLicenseCode" resultType="com.intelliquor.cloud.shop.terminal.model.TerminalShopModel">
        SELECT
            tts.*
        FROM
            t_terminal_shop tts
        JOIN
            t_terminal_shop_info_schedule ttsis
        ON
            tts.id=ttsis.terminal_shop_id
        join t_member_shop tms on tms.id = tts.member_shop_id
        WHERE
            ttsis.status = 1
          AND tts.merge_type in (0,1)
          AND tts.is_delete = 0
          and tms.status = 0
        <if test="deputyCodes != null and deputyCodes.size > 0">
            and tts.deputy_code in
            <foreach collection="deputyCodes" item="deputyCode" separator="," close=")" open="(">
                #{deputyCode}
            </foreach>
        </if>
        ORDER BY tts.create_time
        desc
    </select>
    <select id="cleanTerminalSamePhysical" resultType="com.intelliquor.cloud.shop.terminal.model.TerminalShopModel">
        SELECT
            tts.*
        FROM
            t_terminal_shop tts
                join t_terminal_shop_info_schedule ttsis on ttsis.terminal_shop_id = tts.id
                join t_member_shop tms on tms.id = tts.member_shop_id
                left join t_terminal_shop_physical_code_relation ttspc on ttspc.deputy_code = tts.deputy_code
        WHERE
            tts.is_delete = 0
          and tts.whether_license = 1
          and tts.is_license_success = 1
          and tts.merge_type in (0, 1)
          and ttsis.status = 1
          and tts.license_code not like "%FailInRecog%"
          and tts.license_code is not NULL
          and tts.license_code != ""
          and tms.is_member = 0
          and (ttspc.id is null or ttspc.is_delete = 1)
          and tts.shop_type in (0,3,10,11,12,13)
        <if test="ids != null and ids.size > 0">
            and tts.id in
            <foreach collection="ids" item="id" separator="," close=")" open="(">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>
