<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.terminal.dao.TerminalVisitRecordDao">

    <sql id="baseColumn">
        id,
        shop_id,
        sign_province,
        sign_city,
        sign_distinct,
        sign_address,
        sign_longitude,
        sign_latitude,
        sign_img,
        shop_img,
        remark,
        is_delete,
        create_user_id,
        create_user_type,
        create_time,
        create_date,
        company_id,
        sign_head_img,
        sign_out_time,
        sign_out_province,
        sign_out_city,
        sign_out_distinct,
        sign_out_address,
        sign_out_longitude,
        sign_out_latitude,
        sign_out_head_img,
        summary,
        duration,
        status,
        display_img,
        policy_poster_img,
        policy_card_img,
        policy_explain_img,
        verify_account_img,
        compete_policy,
        account_manager_id,
        live_video,
        in_store_longitude,
        in_store_latitude
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.terminal.model.TerminalVisitRecordModel">
            <id property="id" column="id"/>
            <result property="shopId" column="shop_id"/>
            <result property="signProvince" column="sign_province"/>
            <result property="signCity" column="sign_city"/>
            <result property="signDistinct" column="sign_distinct"/>
            <result property="signAddress" column="sign_address"/>
            <result property="signLongitude" column="sign_longitude"/>
            <result property="signLatitude" column="sign_latitude"/>
            <result property="signImg" column="sign_img"/>
            <result property="shopImg" column="shop_img"/>
            <result property="remark" column="remark"/>
            <result property="isDelete" column="is_delete"/>
            <result property="createUserId" column="create_user_id"/>
            <result property="createUserType" column="create_user_type"/>
            <result property="createTime" column="create_time"/>
            <result property="createDate" column="create_date"/>
            <result property="companyId" column="company_id"/>
        <result property="signHeadImg" column="sign_head_img"/>
        <result property="signOutTime" column="sign_out_time"/>
        <result property="signOutProvince" column="sign_out_province"/>
        <result property="signOutCity" column="sign_out_city"/>
        <result property="signOutDistinct" column="sign_out_distinct"/>
        <result property="signOutAddress" column="sign_out_address"/>
        <result property="signOutLongitude" column="sign_out_longitude"/>
        <result property="signOutLatitude" column="sign_out_latitude"/>
        <result property="signOutHeadImg" column="sign_out_head_img"/>
        <result property="summary" column="summary"/>
        <result property="duration" column="duration"/>
        <result property="status" column="status"/>
        <result property="displayImg" column="display_img"/>
        <result property="policyPosterImg" column="policy_poster_img"/>
        <result property="policyCardImg" column="policy_card_img"/>
        <result property="policyExplainImg" column="policy_explain_img"/>
        <result property="verifyAccountImg" column="verify_account_img"/>
        <result property="competePolicy" column="compete_policy"/>
        <result property="accountManagerId" column="account_manager_id"/>
        <result property="liveVideo" column="live_video"/>
        <result property="inStoreLongitude" column="in_store_longitude"/>
        <result property="inStoreLatitude" column="in_store_latitude"/>
    </resultMap>

    <sql id="selectiveWhere">
        <where>
            r.is_delete = 0
            <if test="id != null">
                and r.id = #{id}
            </if>
            <if test="shopId != null">
                and r.shop_id = #{shopId}
            </if>
            <if test="createUserId != null">
                and r.create_user_id = #{createUserId}
            </if>
            <if test="status != null">
                and r.status = #{status}
            </if>
        </where>
    </sql>


    <select id="selectList" resultType="com.intelliquor.cloud.shop.terminal.model.TerminalVisitRecordModel">
        SELECT
        r.*,
        IFNULL(r.save_time, r.create_time) as createTime,
        s.name as shopName,
        s.is_member as isMember,
        m.name as createUserName,
        m.phone as createUserPhone,
        m.post_id as postId,
        p.post_name as postName,
        mm.name as checkUserName,
        mm.phone as checkUserPhone
        FROM t_terminal_visit_record r
        left join t_member_shop s on r.shop_id = s.id
        left join t_terminal_account_manager m on r.create_user_id = m.id
        left join t_terminal_account_manager mm on r.check_user = mm.id
        left join t_terminal_post p on m.post_id = p.id
        <include refid="selectiveWhere"/>
        ORDER BY r.id desc
    </select>

    <insert id="insert" parameterType="com.intelliquor.cloud.shop.terminal.model.TerminalVisitRecordModel"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_terminal_visit_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="shopId != null ">
            shop_id,
            </if>
            <if test="signProvince != null and signProvince != ''">
            sign_province,
            </if>
            <if test="signCity != null and signCity != ''">
            sign_city,
            </if>
            <if test="signDistinct != null and signDistinct != ''">
            sign_distinct,
            </if>
            <if test="signAddress != null and signAddress != ''">
            sign_address,
            </if>
            <if test="signLongitude != null ">
            sign_longitude,
            </if>
            <if test="signLatitude != null ">
            sign_latitude,
            </if>
            <if test="signImg != null and signImg != ''">
            sign_img,
            </if>
            <if test="shopImg != null and shopImg != ''">
            shop_img,
            </if>
            <if test="remark != null and remark != ''">
            remark,
            </if>
            <if test="isDelete != null ">
            is_delete,
            </if>
            <if test="createUserId != null ">
            create_user_id,
            </if>
            <if test="createUserType != null ">
            create_user_type,
            </if>
            <if test="createTime != null ">
            create_time,
            </if>
            <if test="createDate != null ">
            create_date,
            </if>
            <if test="companyId != null ">
            company_id,
            </if>
        <if test="signHeadImg != null and signHeadImg != ''">
            sign_head_img,
        </if>
        <if test="signOutTime != null ">
            sign_out_time,
        </if>
        <if test="signOutProvince != null and signOutProvince != ''">
            sign_out_province,
        </if>
        <if test="signOutCity != null and signOutCity != ''">
            sign_out_city,
        </if>
        <if test="signOutDistinct != null and signOutDistinct != ''">
            sign_out_distinct,
        </if>
        <if test="signOutAddress != null and signOutAddress != ''">
            sign_out_address,
        </if>
        <if test="signOutLongitude != null ">
            sign_out_longitude,
        </if>
        <if test="signOutLatitude != null ">
            sign_out_latitude,
        </if>
        <if test="signOutHeadImg != null and signOutHeadImg != ''">
            sign_out_head_img,
        </if>
        <if test="summary != null and summary != ''">
            summary,
        </if>
        <if test="duration != null ">
            duration,
        </if>
        <if test="status != null ">
            status,
        </if>
        <if test="displayImg != null and displayImg != ''">
            display_img,
        </if>
        <if test="policyPosterImg != null and policyPosterImg != ''">
            policy_poster_img,
        </if>
        <if test="policyCardImg != null and policyCardImg != ''">
            policy_card_img,
        </if>
        <if test="policyExplainImg != null and policyExplainImg != ''">
            policy_explain_img,
        </if>
        <if test="verifyAccountImg != null and verifyAccountImg != ''">
            verify_account_img,
        </if>
        <if test="competePolicy != null and competePolicy != ''">
            compete_policy,
        </if>
        <if test="accountManagerId != null">
            account_manager_id,
        </if>
        <if test="liveVideo != null and liveVideo != ''">
            live_video,
        </if>
        <if test="inStoreLongitude != null ">
            in_store_longitude,
        </if>
        <if test="inStoreLatitude != null ">
            in_store_latitude,
        </if>
        <if test="tpId != null">
            tp_id,
        </if>
        <if test="stepFlag != null and stepFlag != ''">
            step_flag,
        </if>
        <if test="saveTime != null">
            save_time,
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="shopId != null ">
            #{shopId},
            </if>
            <if test="signProvince != null and signProvince != ''">
            #{signProvince},
            </if>
            <if test="signCity != null and signCity != ''">
            #{signCity},
            </if>
            <if test="signDistinct != null and signDistinct != ''">
            #{signDistinct},
            </if>
            <if test="signAddress != null and signAddress != ''">
            #{signAddress},
            </if>
            <if test="signLongitude != null ">
            #{signLongitude},
            </if>
            <if test="signLatitude != null ">
            #{signLatitude},
            </if>
            <if test="signImg != null and signImg != ''">
            #{signImg},
            </if>
            <if test="shopImg != null and shopImg != ''">
            #{shopImg},
            </if>
            <if test="remark != null and remark != ''">
            #{remark},
            </if>
            <if test="isDelete != null ">
            #{isDelete},
            </if>
            <if test="createUserId != null ">
            #{createUserId},
            </if>
            <if test="createUserType != null ">
            #{createUserType},
            </if>
            <if test="createTime != null ">
            #{createTime},
            </if>
            <if test="createDate != null ">
            #{createDate},
            </if>
            <if test="companyId != null ">
            #{companyId},
            </if>
        <if test="signHeadImg != null and signHeadImg != ''">
            #{signHeadImg},
        </if>
        <if test="signOutTime != null ">
            #{signOutTime},
        </if>
        <if test="signOutProvince != null and signOutProvince != ''">
            #{signOutProvince},
        </if>
        <if test="signOutCity != null and signOutCity != ''">
            #{signOutCity},
        </if>
        <if test="signOutDistinct != null and signOutDistinct != ''">
            #{signOutDistinct},
        </if>
        <if test="signOutAddress != null and signOutAddress != ''">
            #{signOutAddress},
        </if>
        <if test="signOutLongitude != null ">
            #{signOutLongitude},
        </if>
        <if test="signOutLatitude != null ">
            #{signOutLatitude},
        </if>
        <if test="signOutHeadImg != null and signOutHeadImg != ''">
            #{signOutHeadImg},
        </if>
        <if test="summary != null and summary != ''">
            #{summary},
        </if>
        <if test="duration != null ">
            #{duration},
        </if>
        <if test="status != null ">
            #{status},
        </if>
        <if test="displayImg != null and displayImg != ''">
            #{displayImg},
        </if>
        <if test="policyPosterImg != null and policyPosterImg != ''">
            #{policyPosterImg},
        </if>
        <if test="policyCardImg != null and policyCardImg != ''">
            #{policyCardImg},
        </if>
        <if test="policyExplainImg != null and policyExplainImg != ''">
            #{policyExplainImg},
        </if>
        <if test="verifyAccountImg != null and verifyAccountImg != ''">
            #{verifyAccountImg},
        </if>
        <if test="competePolicy != null and competePolicy != ''">
            #{competePolicy},
        </if>
        <if test="accountManagerId != null">
            #{accountManagerId},
        </if>
        <if test="liveVideo != null and liveVideo != ''">
            #{liveVideo},
        </if>
        <if test="inStoreLongitude != null ">
            #{inStoreLongitude},
        </if>
        <if test="inStoreLatitude != null ">
            #{inStoreLatitude},
        </if>
        <if test="tpId != null">
            #{tpId},
        </if>
        <if test="stepFlag != null and stepFlag != ''">
            #{stepFlag},
        </if>
        <if test="saveTime != null">
            #{saveTime},
        </if>
    </trim>
    </insert>

    <update id="update" parameterType="com.intelliquor.cloud.shop.terminal.model.TerminalVisitRecordModel">
        UPDATE t_terminal_visit_record
        <set>
            <if test="shopId != null">
                shop_id = #{shopId},
            </if>
            <if test="signProvince != null and signProvince != ''">
                sign_province = #{signProvince},
            </if>
            <if test="signCity != null and signCity != ''">
                sign_city = #{signCity},
            </if>
            <if test="signDistinct != null and signDistinct != ''">
                sign_distinct = #{signDistinct},
            </if>
            <if test="signAddress != null and signAddress != ''">
                sign_address = #{signAddress},
            </if>
            <if test="signLongitude != null">
                sign_longitude = #{signLongitude},
            </if>
            <if test="signLatitude != null">
                sign_latitude = #{signLatitude},
            </if>
            <if test="signImg != null and signImg != ''">
                sign_img = #{signImg},
            </if>
            <if test="shopImg != null and shopImg != ''">
                shop_img = #{shopImg},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete},
            </if>
            <if test="createUserId != null">
                create_user_id = #{createUserId},
            </if>
            <if test="createUserType != null">
                create_user_type = #{createUserType},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="createDate != null">
                create_date = #{createDate},
            </if>
            <if test="companyId != null">
                company_id = #{companyId},
            </if>
            <if test="signHeadImg != null and signHeadImg != ''">
                sign_head_img = #{signHeadImg},
            </if>
            <if test="signOutTime != null">
                sign_out_time = #{signOutTime},
            </if>
            <if test="signOutProvince != null and signOutProvince != ''">
                sign_out_province = #{signOutProvince},
            </if>
            <if test="signOutCity != null and signOutCity != ''">
                sign_out_city = #{signOutCity},
            </if>
            <if test="signOutDistinct != null and signOutDistinct != ''">
                sign_out_distinct = #{signOutDistinct},
            </if>
            <if test="signOutAddress != null and signOutAddress != ''">
                sign_out_address = #{signOutAddress},
            </if>
            <if test="signOutLongitude != null">
                sign_out_longitude = #{signOutLongitude},
            </if>
            <if test="signOutLatitude != null">
                sign_out_latitude = #{signOutLatitude},
            </if>
            <if test="signOutHeadImg != null and signOutHeadImg != ''">
                sign_out_head_img = #{signOutHeadImg},
            </if>
            <if test="summary != null and summary != ''">
                summary = #{summary},
            </if>
            <if test="duration != null">
                duration = #{duration},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="displayImg != null and displayImg != ''">
                display_img = #{displayImg},
            </if>
            <if test="policyPosterImg != null and policyPosterImg != ''">
                policy_poster_img = #{policyPosterImg},
            </if>
            <if test="policyCardImg != null and policyCardImg != ''">
                policy_card_img = #{policyCardImg},
            </if>
            <if test="policyExplainImg != null and policyExplainImg != ''">
                policy_explain_img = #{policyExplainImg},
            </if>
            <if test="verifyAccountImg != null and verifyAccountImg != ''">
                verify_account_img = #{verifyAccountImg},
            </if>
            <if test="competePolicy != null and competePolicy != ''">
                compete_policy = #{competePolicy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="liveVideo != null and liveVideo != ''">
                live_video = #{liveVideo},
            </if>
            <if test="inStoreLongitude != null ">
                in_store_longitude = #{inStoreLongitude},
            </if>
            <if test="inStoreLatitude != null ">
                in_store_latitude = #{inStoreLatitude},
            </if>
            <if test="checkRemark != null and checkRemark != ''">
                check_remark = #{checkRemark},
            </if>
            <if test="checkTime != null">
                check_time = #{checkTime},
            </if>
            <if test="stepFlag != null and stepFlag != ''">
                step_flag = #{stepFlag},
            </if>
            <if test="checkUser != null">
                check_user = #{checkUser}
            </if>
        </set>
        WHERE id = #{id}
    </update>
    <delete id="delete" parameterType="integer">
        DELETE FROM t_terminal_visit_record
        WHERE id = #{id}
    </delete>

    <select id="getById" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_terminal_visit_record
        WHERE id = #{id}
    </select>

    <select id="selectList4App"
            resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalVisitRecordResp">
        select r.*,
               IFNULL(r.save_time, r.create_time) as createTime,
               s.name          as shopName,
               s.is_member     as isMember,
               m.name          as createUserName,
               s.contract_type as contractType,
               s.level_code    as levelCode,
               c.level_name    as levelShortName,
               m.phone         as createUserPhone,
               m.belong_to     as dealerId
        from t_terminal_visit_record r
                 left join t_member_shop s on r.shop_id = s.id
                 left join t_terminal_account_manager m on r.create_user_id = m.id
                 left join t_terminal_shop_level c on s.level_code = c.id
        where r.is_delete = 0
        <if test="isMember != null">
            and s.is_member = #{isMember}
        </if>
<!--        <if test="isMember == null">-->
<!--            and s.is_member in(0,2)-->
<!--        </if>-->
        <if test="companyId != null">
            and r.company_id = #{companyId}
        </if>
        <if test="shopName != null and shopName != ''">
            and s.name like CONCAT('%', #{shopName}, '%')
        </if>
        <if test="createDate != null and createDate != ''">
            and DATE_FORMAT(r.create_time, '%Y-%m') = #{createDate}
        </if>
        <if test="accountType != null">
            <if test="accountType == 0">
                and (m.parent_id = #{accountId} or r.create_user_id = #{accountId})
            </if>
            <if test="accountType == 1 || accountType == 4 || accountType == 5 || accountType == 6">
                and r.create_user_id = #{accountId}
            </if>
        </if>
        <if test="shopId != null">
            and r.shop_id = #{shopId}
        </if>
        <if test="startTime != null">
            and r.sign_out_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and r.sign_out_time &lt;= #{endTime}
        </if>
        <if test="checkList == 1">
            and r.status >= 1
        </if>
        <if test="auditStatus != null">
            and r.status = #{auditStatus}
        </if>
        order by r.id desc
    </select>

    <select id="getShopListByPage"
            resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalVisitShopResp">
        select distinct s.id as shopId, s.name as shopName,s.province,s.city,s.district,s.address,s.longitude,s.latitude,s.stores_img,
        s.linkman as leaderName,s.linkphone as leaderPhone,tt.contract_type,tt.level_code,tt.level_name as levelShortName
            <if test="latitude != null and longitude != null">
                ,( 6371 * acos( cos( radians(#{latitude}) ) * cos( radians( s.latitude ) ) * cos( radians( s.longitude ) - radians(#{longitude}) ) + sin( radians(#{latitude}) ) * sin( radians( s.latitude ) ) ) ) AS distance
            </if>
            <if test="sortCode != null and sortCode == 'visitTime'">
                ,v.visitTime as visitTime
            </if>
          from t_member_shop s
            left join (select t1.id as terminal_shop_id, t1.member_shop_id, t2.level_code,c.level_name, t1.shop_type, d.contract_type
            from t_terminal_shop t1
            left join t_terminal_protocol t2 on (t1.id = t2.terminal_shop_id and t2.protocol_type = 0 and t2.delete_status = 0 and t2.check_status = 1)
            left join t_terminal_shop_level c on t2.level_code = c.id
            left join t_terminal_shop_contract d on t1.id = d.terminal_shop_id
         ) tt on s.id = tt.member_shop_id
          <if test="sortCode != null and sortCode == 'visitTime'">
              left join (
              select shop_id as shopId, max(create_time) as visitTime from t_terminal_visit_record where is_delete = 0 group by shop_id
              ) v on s.id = v.shopId
          </if>
        where s.status = 0 and tt.shop_type != 5 and tt.shop_type != 2
        <if test="accountType != null">
            <if test="accountType == 1 and belongTo != null">
                and s.id in (
                    select s.id from t_cloud_dealer_relation r
                    left join t_cloud_dealer_info i on r.dealer_id = i.id
                    left join t_member_shop s on s.dealer_code = i.dealer_code
                    where r.parent_id = #{belongTo}
                )
            </if>
            <if test="accountType == 0">
                and s.id in (
                    select s.id
                    from t_cloud_dealer_relation r
                    left join t_cloud_dealer_info i on r.dealer_id = i.id
                    left join t_member_shop s on s.dealer_code = i.dealer_code
                    where r.parent_id in (
                        select d.dealer_id
                        from t_terminal_account_manager_dealer d
                        where d.account_manager_id = #{accountId})
                )
            </if>
            <if test="accountType == 4">
                and s.id in (
                    select member_shop_id from t_terminal_shop where is_delete = 0 and status = 1 and (create_user = #{accountId} or terminal_divide_id = #{accountId} )
                )
            </if>
        </if>
        <if test="shopName != null and shopName != ''">
            and s.name like CONCAT('%',#{shopName},'%')
        </if>
        <if test="province != null and province != ''">
            and s.province = #{province}
        </if>
        <if test="city != null and city != ''">
            and s.city = #{city}
        </if>
        <if test="district != null and district != ''">
            and s.district = #{district}
        </if>
        <if test="address != null and address != ''">
            and s.address like CONCAT('%',#{address},'%')
        </if>
<!--        <if test="sauceLevel != null and sauceLevel != ''">-->
<!--            and s.contract_type = 1 and s.level_code = #{sauceLevel}-->
<!--        </if>-->
<!--        <if test="mainLevel != null and mainLevel != ''">-->
<!--            and s.contract_type = 0 and s.level_code = #{mainLevel}-->
<!--        </if>-->
        <if test="contractType != null">
            and tt.contract_type = #{contractType}
        </if>
        <if test="levelCode != null and levelCode != ''">
            and tt.level_code = #{levelCode}
        </if>
        <choose>
            <when test="sortCode != null and sortCode == 'distance' and latitude != null and longitude != null">
                ORDER BY distance ${sortRole}
            </when>
            <when test="sortCode != null and sortCode == 'visitTime'">
                ORDER BY visitTime ${sortRole}
            </when>
            <otherwise>
                order by s.id desc
            </otherwise>
        </choose>
    </select>
    <select id="getShopListByPage2"
            resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalVisitShopResp">
        select distinct s.id as shopId, s.name as shopName,s.province,s.city,s.district,s.address,s.longitude,s.latitude,s.stores_img,
        s.linkman as leaderName,s.linkphone as leaderPhone
        <if test="latitude != null and longitude != null">
            ,( 6371 * acos( cos( radians(#{latitude}) ) * cos( radians( s.latitude ) ) * cos( radians( s.longitude ) - radians(#{longitude}) ) + sin( radians(#{latitude}) ) * sin( radians( s.latitude ) ) ) ) AS distance
        </if>
        <if test="sortCode != null and sortCode == 'visitTime'">
            ,v.visitTime as visitTime
        </if>
        from t_member_shop s
        <if test="sortCode != null and sortCode == 'visitTime'">
            left join (
            select shop_id as shopId, max(create_time) as visitTime from t_terminal_visit_record where is_delete = 0 group by shop_id
            ) v on s.id = v.shopId
        </if>
        where s.status = 0
        <if test="accountType != null">
            <if test="accountType == 1 and belongTo != null">
                and s.id in (
                SELECT
                t.member_shop_id
                FROM
                t_terminal_shop_contract t,t_cloud_dealer_info tdi
                WHERE
                t.dealer_code=tdi.dealer_code and
                tdi.id=#{belongTo}
                )
            </if>
            <if test="accountType == 0">
                and s.id in (
                SELECT
                DISTINCT member_shop_id
                FROM
                t_terminal_shop_contract
                WHERE
                dealer_code IN ( SELECT d.dealer_code
                FROM t_terminal_account_manager_dealer d
                WHERE d.account_manager_id = #{accountId} )
                )
            </if>
            <if test="accountType == 4 or accountType == 5 or accountType == 6">
                and s.id in (
                select member_shop_id from t_terminal_shop where is_delete = 0 and status = 1 and (create_user = #{accountId} or terminal_divide_id = #{accountId} )
                )
            </if>
        </if>
        <if test="shopName != null and shopName != ''">
            and s.name like CONCAT('%',#{shopName},'%')
        </if>
        <if test="province != null and province != ''">
            and s.province = #{province}
        </if>
        <if test="city != null and city != ''">
            and s.city = #{city}
        </if>
        <if test="district != null and district != ''">
            and s.district = #{district}
        </if>
        <if test="address != null and address != ''">
            and s.address like CONCAT('%',#{address},'%')
        </if>
        <!--        <if test="sauceLevel != null and sauceLevel != ''">-->
        <!--            and s.contract_type = 1 and s.level_code = #{sauceLevel}-->
        <!--        </if>-->
        <!--        <if test="mainLevel != null and mainLevel != ''">-->
        <!--            and s.contract_type = 0 and s.level_code = #{mainLevel}-->
        <!--        </if>-->
        <if test="contractType != null">
            and tt.contract_type = #{contractType}
        </if>
        <if test="levelCode != null and levelCode != ''">
            and tt.level_code = #{levelCode}
        </if>
        <choose>
            <when test="sortCode != null and sortCode == 'distance' and latitude != null and longitude != null">
                ORDER BY distance ${sortRole}
            </when>
            <when test="sortCode != null and sortCode == 'visitTime'">
                ORDER BY visitTime ${sortRole}
            </when>
            <otherwise>
                order by s.id desc
            </otherwise>
        </choose>
    </select>
    <select id="getShopListByPage3"
            resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalVisitShopResp">
        select * from (
        select s.member_shop_id as shopId, s.shop_name as shopName,s.province,s.city,s.district,s.address,s.longitude,s.latitude,s.head_img as storesImg,
        s.leader_name as leaderName,s.leader_phone as leaderPhone,
        tamm.name                            as accountManagerName,
        tamm.phone                           as accountManagerPhone,
        s.terminal_divide_id
        <if test="latitude != null and longitude != null">
            ,IFNULL(( 6371 * acos( cos( radians(#{latitude}) ) * cos( radians( s.latitude ) ) * cos( radians( s.longitude ) - radians(#{longitude}) ) + sin( radians(#{latitude}) ) * sin( radians( s.latitude ) ) ) ),**********99) AS distance
        </if>
        <if test="latitude == null or longitude == null">
            , -1  AS distance
        </if>
        from t_terminal_shop s
        left join t_terminal_account_manager tamDivide on s.terminal_divide_id = tamDivide.id
        left join t_terminal_account_manager tamm on tamm.id = tamDivide.parent_id
        left join (select * from t_terminal_shop_merge where status=0) merge on s.id=merge.from_id
        left join t_member_shop m on s.member_shop_id = m.id
        left join t_terminal_shop_info_schedule tsis on s.id = tsis.terminal_shop_id
        where tsis.status = 1 and s.is_delete = 0 and s.shop_type not in (2,5)
        and m.status = 0
        and s.merge_type in (0,1) and merge.from_id is null
        <if test="memberShopIds != null and memberShopIds.size()>0">
            and s.member_shop_id in
            <foreach collection="memberShopIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="accountType != null">
            <if test="accountType == 4 or accountType == 5  or accountType == 6">
                 and (s.create_user = #{accountId} or s.terminal_divide_id = #{accountId} )
            </if>
        </if>

        <if test="shopName != null and shopName != ''">
            and s.shop_name like CONCAT('%',#{shopName},'%')
        </if>
        ) tab  where 1=1 order by tab.distance
    </select>
    <select id="getShopIdByDealerId" resultType="java.lang.Integer">
        SELECT
        t.member_shop_id
        FROM
        t_terminal_shop_contract t,t_cloud_dealer_info tdi
        WHERE
        t.dealer_code=tdi.dealer_code and
        tdi.id=#{dealerId}
    </select>
    <select id="getShopIdByAccountId" resultType="java.lang.Integer">
        SELECT
        DISTINCT c.member_shop_id
        FROM
        t_terminal_shop_contract c
        left join t_terminal_account_manager_dealer d on c.dealer_code =d.dealer_code

        WHERE d.account_manager_id = #{accountId}
    </select>
    <select id="getOtherTerminalShop"
            resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalVisitShopResp">
        select t1.id as terminal_shop_id, t1.member_shop_id as shopId, t2.level_code,c.level_name as levelShortName, t1.shop_type, d.contract_type
        from t_terminal_shop t1
            left join t_terminal_protocol t2 on (t1.id = t2.terminal_shop_id and t2.protocol_type = 0 and t2.delete_status = 0 and t2.check_status = 1)
            left join t_terminal_shop_level c on t2.level_code = c.id
            left join t_terminal_shop_contract d on t1.id = d.terminal_shop_id
        <where>
            <if test="shopIdList != null and shopIdList != ''">
                AND t1.member_shop_id in
                <foreach collection="shopIdList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
<!--    <select id="getOtherTerminalShop2024"-->
<!--            resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalVisitShopResp">-->
<!--        select t1.id as terminal_shop_id, t1.member_shop_id as shopId, tpr.level_code,c.level_name as levelShortName, t1.shop_type, d.contract_type-->
<!--        from t_terminal_shop t1-->
<!--        left join-->
<!--        (                SELECT *-->
<!--        FROM `t_terminal_product_protocol_relation` a-->
<!--        WHERE a.is_delete = 0-->
<!--        and a.check_status = 1-->
<!--        and a.effective_time &lt;= #{currentDate}-->
<!--        and `effective_time` = (-->
<!--        SELECT MAX(`effective_time`)-->
<!--        FROM `t_terminal_product_protocol_relation` b-->
<!--        WHERE b.`terminal_shop_id` = a.`terminal_shop_id`-->
<!--        and b.is_delete = 0-->
<!--        and b.check_status = 1-->
<!--        and b.effective_time &lt;= #{currentDate}-->
<!--        )-->
<!--        AND `id` = (-->
<!--        SELECT MAX(`id`)-->
<!--        FROM `t_terminal_product_protocol_relation` c-->
<!--        WHERE c.`terminal_shop_id` = a.`terminal_shop_id` AND c.`effective_time` = a.`effective_time`-->
<!--        and c.is_delete = 0-->
<!--        and c.check_status = 1-->
<!--        and c.effective_time &lt;= #{currentDate}-->
<!--        )-->
<!--        GROUP BY `terminal_shop_id`) as tpr-->
<!--        on tpr.terminal_shop_id = t1.id-->
<!--        left join t_terminal_shop_level c on tpr.level_code = c.id-->
<!--        left join t_terminal_shop_contract d on t1.id = d.terminal_shop_id-->
<!--        <where>-->
<!--            <if test="shopIdList != null and shopIdList != ''">-->
<!--                AND t1.member_shop_id in-->
<!--                <foreach collection="shopIdList" item="item" index="index" open="(" close=")" separator=",">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--            </if>-->
<!--        </where>-->
<!--    </select>-->
    <select id="getOtherTerminalShop2024"
            resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalVisitShopResp">
        select t1.id as terminal_shop_id, t1.member_shop_id as shopId, t2.level_code, c.level_name as levelShortName, t1.shop_type, d.contract_type
        from t_terminal_shop t1
        left join t_terminal_product_protocol_relation t2 on (t1.id = t2.terminal_shop_id and t2.is_delete = 0 and t2.check_status = 1)
        left join t_terminal_shop_level c on t2.level_code = c.id
        left join t_terminal_shop_contract d on t1.id = d.terminal_shop_id
        <where>
            <if test="shopIdList != null and shopIdList != ''">
                AND t1.member_shop_id in
                <foreach collection="shopIdList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getShopVisitData"
            resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalVisitShopResp">
        select
            shop_id,
            datediff(now(),max(create_date)) as lastVisitDay,
            count(*) as visitNum
        from t_terminal_visit_record
        <where>
            is_delete = 0
            <if test="shopIds != null and shopIds.size >0">
                and shop_id in
                <foreach collection="shopIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="visitMonth != null and visitMonth != ''">
                and DATE_FORMAT(create_time, '%Y-%m') = #{visitMonth}
            </if>
        </where>
        group by shop_id;
    </select>
    <select id="getDealerListByIds"
            resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalVisitRecordResp">
        select id as dealerId , dealer_name as dealerName from t_cloud_dealer_info
        <where>
            <if test="dealerIds != null and dealerIds.size>0">
                and id in
                <foreach collection="dealerIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getDeputyCode" resultType="java.lang.String">
        SELECT
            dealer_code AS 'deputyCode'
        FROM
            t_member_shop
        WHERE
            id = #{shopId}
    </select>

    <select id="getVisitName" resultType="java.lang.String">
        SELECT
            name AS 'visitname'
        FROM
            t_terminal_account_manager
        WHERE
            id = #{createUserId}
    </select>
    <select id="selectList4Admin"
            resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalVisitRecordAdminResp">
        select r.*,
               IFNULL(r.save_time, r.create_time) as createTime,
               s.name as shopName ,
               m.agent_code as agentCode,
               m.name as createUserName,
               m.phone as createUserPhone,
               m.post_id as postId,
               p.post_name as postName,
                CONCAT(m.name,m.phone) as createUserInfo,
                am.name as accountManagerName,
                am.phone as accountManagerPhone,
                CONCAT(am.name,'(',am.phone,')') as managerUserInfo,
                m.dept_code as deptCode
        from t_terminal_visit_record r
        left join t_member_shop s on r.shop_id = s.id
        left join t_terminal_account_manager m on r.create_user_id = m.id
        left join t_terminal_post p on m.post_id = p.id
        left join t_terminal_account_manager am on r.account_manager_id = am.id
        where r.is_delete = 0
        <if test="postName != null and postName != ''">
            and p.post_name like CONCAT('%',#{postName},'%')
        </if>
        <if test="createUserPhone != null and createUserPhone != ''">
            and m.phone like CONCAT('%',#{createUserPhone},'%')
        </if>
        <if test="agentCodeOrPhone != null and agentCodeOrPhone != ''">
            and(
            m.agent_code like concat('%',#{agentCodeOrPhone},'%')
            or
            m.phone like concat('%',#{agentCodeOrPhone},'%')
            )
        </if>
        <if test="shopName != null and shopName != ''">
            and s.name like CONCAT('%',#{shopName},'%')
        </if>
        <if test="startId != null and startId != ''">
            and r.id &lt; #{startId}
        </if>
        <if test="accountManagerPhone != null and accountManagerPhone != ''">
            and am.phone like CONCAT('%',#{accountManagerPhone},'%')
        </if>
        <if test="accountManagerName != null and accountManagerName != ''">
            and am.name like CONCAT('%',#{accountManagerName},'%')
        </if>
        <if test="startSignOutTime != null and startSignOutTime != '' ">
            and r.sign_out_time &gt;= #{startSignOutTime}
        </if>
        <if test="endSignOutTime != null and endSignOutTime != '' ">
             and r.sign_out_time &lt;= #{endSignOutTime}
        </if>
        <if test="deptCode != null">
            and m.dept_code = #{deptCode}
        </if>
        order by r.id desc
        <if test="maxLimit != null and maxLimit != ''">
            limit #{maxLimit}
        </if>
    </select>
    <select id="getVisitRecordByShopId" resultType="com.intelliquor.cloud.shop.terminal.model.TerminalVisitRecordModel">
        select
        <include refid="baseColumn"></include>
        from t_terminal_visit_record
        where is_delete = 0
            and status = 1
            and shop_id = #{memberShopId}
        order by create_time desc
        limit 1
    </select>
    <select id="getVisitRecordById" resultType="com.intelliquor.cloud.shop.terminal.model.TerminalVisitRecordModel">
        select
            r.*,
            IFNULL(r.save_time, r.create_time) as createTime,
            s.name as shopName,
            m.name as createUserName,
            m.phone as createUserPhone,
            m.post_id as postId,
            p.post_name as postName
        FROM t_terminal_visit_record r
        left join t_member_shop s on r.shop_id = s.id
        left join t_terminal_account_manager m on r.create_user_id = m.id
        left join t_terminal_post p on m.post_id = p.id
        where r.id = #{visitRecordId}
    </select>
    <select id="getByShopIdAndSignOutTime" resultType="com.intelliquor.cloud.shop.terminal.model.TerminalVisitRecordModel">
        select
            r.*,
            s.name as shopName,
            m.name as createUserName,
            m.phone as createUserPhone,
            m.post_id as postId,
            p.post_name as postName
            FROM t_terminal_visit_record r
            left join t_member_shop s on r.shop_id = s.id
            left join t_terminal_account_manager m on r.create_user_id = m.id
            left join t_terminal_post p on m.post_id = p.id
        <where>
            r.is_delete = 0 and r.status = 1
            <if test="memberShopId != null">
                and r.shop_id = #{memberShopId}
            </if>
            <if test="yearMonthDay != null and yearMonthDay != ''">
                and DATE_FORMAT(r.sign_out_time,'%Y-%m-%d') = #{yearMonthDay}
            </if>
        </where>
        order by r.sign_out_time desc
        limit 1
    </select>
    <select id="getVisitRecordByMemberShopId" resultType="com.intelliquor.cloud.shop.terminal.model.TerminalVisitRecordModel">
        select
        r.*,
        s.name as shopName,
        m.name as createUserName,
        m.phone as createUserPhone,
        m.post_id as postId,
        p.post_name as postName
        FROM t_terminal_visit_record r
        left join t_member_shop s on r.shop_id = s.id
        left join t_terminal_account_manager m on r.create_user_id = m.id
        left join t_terminal_post p on m.post_id = p.id
        <where>
            <if test="memberShopId != null">
                and r.shop_id = #{memberShopId}
            </if>
        </where>
        order by r.create_time desc
        limit 1
    </select>
    <select id="selectListByParam"
            resultType="com.intelliquor.cloud.shop.terminal.model.TerminalVisitRecordModel">
        select * from t_terminal_visit_record where is_delete = 0
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="sysState != null">
            and sys_state = #{sysState}
        </if>
        order by create_time desc  limit 10
    </select>
    <select id="getMemberShopListByPage"
            resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalVisitShopResp">
        select
            distinct s.member_shop_id as shopId,
            s.shop_name as shopName,
            s.province,s.city,
            s.district,
            s.address,
            s.head_img as storesImg,
            s.leader_name as leaderName,
            s.leader_phone as leaderPhone
        from
            t_terminal_shop s
        left join
                t_member_shop ms on ms.id = s.member_shop_id
        where
            s.status = 1
          and s.is_delete = 0
          and s.is_prepare = 0
          and s.shop_type = 5
          and s.is_high_member = 0
          and ms.is_member = 1
        <if test="accountType != null">
            <if test="accountType == 1 and belongTo != null">
                and s.member_shop_id in (
                SELECT
                t.member_shop_id
                FROM
                t_terminal_shop_contract t,t_cloud_dealer_info tdi
                WHERE
                t.dealer_code=tdi.dealer_code and
                tdi.id=#{belongTo}
                )
            </if>
            <if test="accountType == 0">
                and s.member_shop_id in (
                SELECT
                    DISTINCT member_shop_id
                FROM
                    t_terminal_shop_contract
                WHERE
                    dealer_code IN ( SELECT d.dealer_code
                    FROM t_terminal_account_manager_dealer d
                    WHERE d.account_manager_id = #{accountId} )

                union all
                select DISTINCT member_shop_id from t_terminal_shop_contract where contract_code in(
                select contract_code from t_dealer_contract_rel where dealer_code in (SELECT d.dealer_code
                FROM t_terminal_account_manager_dealer d
                WHERE d.account_manager_id = #{accountId} ))
                )
            </if>
            <if test="accountType == 4 or accountType == 5 or accountType == 6">
                and s.id in (
                    select
                        id
                    from
                        t_terminal_shop
                    where
                        is_delete = 0
                      and status = 1
                      and (create_user = #{accountId} or terminal_divide_id = #{accountId} )
                )
            </if>
        </if>
        <if test="shopName != null and shopName != ''">
            and s.shop_name like CONCAT('%',#{shopName},'%')
        </if>
    </select>
    <select id="selectVisitTotalList"
            resultType="com.intelliquor.cloud.shop.terminal.model.TerminalVisitRecordModel">
        select * from t_terminal_visit_record where is_delete = 0 and status in (1,2)
        <if test="startDate != null ">
            and sign_out_time &gt;= #{startDate}
        </if>
        <if test="endDate != null ">
            and sign_out_time &lt;= #{endDate}
        </if>
         order by sign_out_time
    </select>
    <update id="updateSysState">
        update t_terminal_visit_record set sys_state = #{sysState},sys_date = #{sysDate},sys_remark= #{sysRmark} where id = #{id}
    </update>
    <update id="updateStepFlag">
        update t_terminal_visit_record set step_flag = #{stepFlag} where id = #{id}
    </update>

    <select id="getTerminalVisitCountByTimeRange" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.channelStatistics.FindMapResp">
        SELECT
        COUNT(DISTINCT visit.id) as count,
        ts.shop_type as shopType
        FROM
        t_terminal_visit_record visit
        LEFT JOIN
        t_terminal_shop ts
        ON
        visit.shop_id = ts.member_shop_id
        LEFT JOIN
        t_terminal_shop_contract tsc
        ON
        ts.id = tsc.terminal_shop_id
        WHERE
        ts.`status` = 1
        AND ts.is_prepare = 0
        AND ts.is_delete = 0
        AND ts.shop_type != 5
        AND tsc.contract_code = #{contractCode}
        AND visit.create_time &gt;= #{startTime}
        AND visit.create_time &lt;= #{endTime}
        <if test="shopType != null and shopType != ''">
            AND ts.shop_type = #{shopType}
        </if>
        GROUP BY ts.shop_type
    </select>

    <select id="selectVisitDataListByTerminalShopId" parameterType="java.util.Map" resultType="com.intelliquor.cloud.shop.terminal.model.resp.shopActivity.visitStatistics.VisitDataResp">
        SELECT
            visit.id as "visitId",
            create_time as "date",
            (SELECT `name` FROM t_terminal_account_manager usr WHERE usr.id = visit.create_user_id) as "visitop"
        FROM
            t_terminal_visit_record visit
        WHERE
            visit.shop_id = #{shopId}
          AND visit.create_time &gt;= #{startTime}
          AND visit.create_time &lt;= #{endTime}
        ORDER BY visit.create_time ASC
    </select>
</mapper>
