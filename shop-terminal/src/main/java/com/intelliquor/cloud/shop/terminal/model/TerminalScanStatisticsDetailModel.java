package com.intelliquor.cloud.shop.terminal.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 终端有效收货核算详情
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@TableName("t_terminal_scan_statistics_detail")
public class TerminalScanStatisticsDetailModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 核算批次
     */
    private String batchId;

    /**
     * 终端id
     */
    private Integer terminalShopId;

    /**
     * 终端店铺名称
     */
    private String shopName;

    /**
     * 终端副编码
     */
    private String deputyCode;

    /**
     * 店铺类型
     * 0:渠道终端 1:餐饮终端 2:团购终端 3:企业终端 4:连锁终端 5:会员终端
     * 6:渠道终端会员 7:连锁终端会员 8:非会员虚拟终端 9:超级终端
     * 10:连锁型餐饮酒店 11:特色型餐饮酒店 12:商务型餐饮酒店
     * 13:宴席型餐饮酒店 14:渠道连锁终端
     */
    private Integer shopType;

    /**
     * 店铺类型名称
     */
    private String shopTypeName;

    /**
     * 核算起始时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 核算截止时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 产品skucode列表
     */
    private String skuCodes;

    /**
     * 有效进货量
     */
    private Integer effectiveQuantity;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime updateTime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDelete;
}
