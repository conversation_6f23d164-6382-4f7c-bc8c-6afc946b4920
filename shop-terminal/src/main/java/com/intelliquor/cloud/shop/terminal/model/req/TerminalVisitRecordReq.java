package com.intelliquor.cloud.shop.terminal.model.req;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class TerminalVisitRecordReq {

    /**
     * 拜访记录Id
     */
    private Integer id;

    private Integer companyId;
    /**
     * 登录人Id
     */
    private Integer accountId;
    /**
     * 登录人类型0-客户经理 1-经销商人员 2-终端人员
     */
    private Integer accountType;
    /**
     * 登录人所属的经销商或终端Id
     */
    private String belongTo;

    private Integer page = 1;
    private Integer limit = 10;

    /**
     * 终端名称
     */
    private String shopName;
    /**
     * 拜访日期 年-月
     */
    private String createDate;

    /**
     * 客户经理Id
     */
    private Integer accountManagerId;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 创建人手机号
     */
    private String createUserPhone;


    /**
     * 导出条件
     */
    private String selectCondition;

    /**
     * 最大查询条数
     */
    private Integer maxLimit;

    /**
     * 起始id
     */
    private Integer startId;

    /**
     * 终端Id
     */
    private Integer shopId;

    /**
     * 客户经理账号
     */
    private String accountManagerPhone;

    /**
     * 客户经理名称
     */
    private String accountManagerName;

    /**
     * 业代编号或业代登录账号（手机号）
     */
    private String agentCodeOrPhone;

    /**
     * 离店时间(start)
     */
    private String startSignOutTime;
    /**
     * 离店时间(end)
     */
    private String endSignOutTime;

    /**
     * 开始时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 1查询审核列表
     */
    private Integer checkList;

    /**
     * 是否为会员 0-否 1-是
     */
    private Integer isMember;

    /**
     * 所属运营中心(0国标运营中心1高端酒运营中心)
     */
    private Integer deptCode;

    /**
     * 审核状态筛选条件
     * null-全部（默认值）
     * 1-未审核（已提交待审核）
     * 2-合格（审核通过）
     * 3-不合格（审核不通过）
     */
    private Integer auditStatus;
}
