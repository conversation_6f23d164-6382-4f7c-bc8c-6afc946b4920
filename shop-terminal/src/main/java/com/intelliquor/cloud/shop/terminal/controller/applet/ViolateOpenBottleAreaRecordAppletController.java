package com.intelliquor.cloud.shop.terminal.controller.applet;


import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.model.FenceViolationOfAgreementAppealModel;
import com.intelliquor.cloud.shop.common.model.TerminalAccountManagerModel;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.common.model.req.BatchAppealServiceReq;
import com.intelliquor.cloud.shop.common.model.req.BatchFenceViolationAppealReq;
import com.intelliquor.cloud.shop.common.model.req.FenceViolateOpenBottleAreaRecordReq;
import com.intelliquor.cloud.shop.common.model.resp.BatchFenceViolationAppealResp;
import com.intelliquor.cloud.shop.common.model.resp.FenceViolateOpenBottleAreaRecordResp;
import com.intelliquor.cloud.shop.common.service.FenceViolateOpenBottleAreaRecordService;
import com.intelliquor.cloud.shop.terminal.service.IFenceViolateOpenBottleAreaRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * 联谊会小程序端违约记录
 *
 * @module 国台领袖会
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/terminalApplet/violateOpenBottleAreaRecord")
public class ViolateOpenBottleAreaRecordAppletController {

    @Resource
    private IFenceViolateOpenBottleAreaRecordService iFenceViolateOpenBottleAreaRecordService;

    @Resource
    private UserContext userContext;

    @Resource
    private FenceViolateOpenBottleAreaRecordService fenceViolateOpenBottleAreaRecordService;


    /**
     * 奖励记录
     */
    @GetMapping("getPageList")
    public RestResponse<PageInfo<FenceViolateOpenBottleAreaRecordResp>> getPageList(@RequestParam(value = "page", defaultValue = "1") int page,
                                                                                    @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                                                    FenceViolateOpenBottleAreaRecordReq fenceViolateOpenBottleAreaRecordReq) {
        //登录用户id
        Integer id = userContext.getTerminalModel().getId();
        //用户类型
        Integer type = userContext.getTerminalModel().getType();
        //当前下级账户绑定的经销商或者终端关系
        String dealerIdOrShopId = userContext.getTerminalModel().getBelongTo();
        try {
            // 小程序端获取违约记录 过滤掉无需处理的白名单数据
            fenceViolateOpenBottleAreaRecordReq.setShowDealStatus("1");
            // 小程序端不使用经销商名称搜索，由前端控制改为箱码搜索（SZYXPT-2138）
            fenceViolateOpenBottleAreaRecordReq.setViolateDealerName(null);
            List<FenceViolateOpenBottleAreaRecordResp> list = iFenceViolateOpenBottleAreaRecordService.getTerminalPageList(page, limit, id, type, dealerIdOrShopId, fenceViolateOpenBottleAreaRecordReq);
            PageInfo<FenceViolateOpenBottleAreaRecordResp> pageInfo = new PageInfo<>(list);
            return RestResponse.success("查询成功", pageInfo);
        } catch (Exception e) {
            return RestResponse.error(e.getMessage());
        }
    }


    /**
     * 奖励记录
     */
    @GetMapping("getById")
    public RestResponse<FenceViolateOpenBottleAreaRecordResp> getById(Integer id) {
        try {
            FenceViolateOpenBottleAreaRecordResp fenceViolateOpenBottleAreaRecordResp = fenceViolateOpenBottleAreaRecordService.queryById(id);
            return RestResponse.success("查询成功", fenceViolateOpenBottleAreaRecordResp);
        } catch (Exception e) {
            return RestResponse.error(e.getMessage());
        }
    }

    /**
     * 发起申诉
     *
     * @param fenceViolationOfAgreementAppealModel 违约记录id
     * @return Response
     */
    @PostMapping("addAppeal")
    public RestResponse<String> addAppeal(@RequestBody FenceViolationOfAgreementAppealModel fenceViolationOfAgreementAppealModel) {
        if (Objects.isNull(fenceViolationOfAgreementAppealModel.getViolationOfAgreementInfoId())) {
            throw new BusinessException("id不能为空");
        }
        fenceViolationOfAgreementAppealModel.setCreateUser(userContext.getTerminalModel().getId());
        fenceViolationOfAgreementAppealModel.setCreateUserName(userContext.getTerminalModel().getName());
        fenceViolationOfAgreementAppealModel.setCompanyId(userContext.getTerminalModel().getCompanyId());
        fenceViolationOfAgreementAppealModel.setAppealUserTel(userContext.getTerminalModel().getPhone());
        fenceViolateOpenBottleAreaRecordService.addAppeal(fenceViolationOfAgreementAppealModel);
        return RestResponse.success("申诉成功");
    }

    /**
     * 批量发起申诉
     *
     * @param request 批量申诉请求
     * @return Response
     */
    @PostMapping("batchAddAppeal")
    public RestResponse<BatchFenceViolationAppealResp> batchAddAppeal(@RequestBody @Valid BatchFenceViolationAppealReq request) {
        try {
            // 构建Service层请求参数
            BatchAppealServiceReq serviceReq = buildBatchAppealServiceReq(request);
            
            // 调用批量申诉服务
            BatchFenceViolationAppealResp response = fenceViolateOpenBottleAreaRecordService.batchAddAppeal(serviceReq);
            
            // 根据结果返回相应消息
            return buildBatchAppealResponse(response);
        } catch (Exception e) {
            // 这个catch块现在只用于捕获未预料到的系统级异常，例如数据库连接失败
            log.error("批量申诉时发生未知系统错误: {}", e.getMessage(), e);
            return RestResponse.error("批量申诉失败，系统繁忙，请稍后重试");
        }
    }

    /**
     * 构建批量申诉Service层请求参数
     */
    private BatchAppealServiceReq buildBatchAppealServiceReq(BatchFenceViolationAppealReq request) {
        // 获取当前用户信息（只调用一次）
        TerminalAccountManagerModel terminalModel = userContext.getTerminalModel();
        
        return BatchAppealServiceReq.builder()
                .violationOfAgreementInfoIds(request.getViolationOfAgreementInfoIds())
                .reason(request.getReason())
                .appealFileUrl(request.getAppealFileUrl())
                .createUser(terminalModel.getId())
                .createUserName(terminalModel.getName())
                .companyId(terminalModel.getCompanyId())
                .appealUserTel(terminalModel.getPhone())
                .build();
    }

    /**
     * 构建批量申诉响应结果
     */
    private RestResponse<BatchFenceViolationAppealResp> buildBatchAppealResponse(BatchFenceViolationAppealResp response) {
        if (response.getFailedItems().isEmpty()) {
            return RestResponse.success("批量申诉全部成功", response);
        } else if (response.getSuccessIds().isEmpty()) {
            return RestResponse.success("批量申诉全部失败", response);
        } else {
            return RestResponse.success("批量申诉部分成功", response);
        }
    }
}
