package com.intelliquor.cloud.shop.terminal.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.intelliquor.cloud.shop.common.constant.RedisConstant;
import com.intelliquor.cloud.shop.common.dao.TerminalShopDisableRecordCommonDao;
import com.intelliquor.cloud.shop.common.dao.TerminalShopMergeDao;
import com.intelliquor.cloud.shop.common.dao.TerminalShopNodeDao;
import com.intelliquor.cloud.shop.common.enums.DeleteFlagEnum;
import com.intelliquor.cloud.shop.common.enums.ProtocolCheckStatusEnum;
import com.intelliquor.cloud.shop.common.enums.TerminalShopNodeEnum;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.model.*;
import com.intelliquor.cloud.shop.common.model.req.TerminalShopNodeReq;
import com.intelliquor.cloud.shop.common.model.resp.TerminalProtocolResp;
import com.intelliquor.cloud.shop.common.model.resp.TerminalShopContractResp;
import com.intelliquor.cloud.shop.common.model.resp.TerminalShopNodeResp;
import com.intelliquor.cloud.shop.common.service.IRequestLogService;
import com.intelliquor.cloud.shop.common.utils.ObjectUtil;
import com.intelliquor.cloud.shop.terminal.service.TerminalProtocolActivityRelationService;
import com.intelliquor.cloud.shop.common.utils.DateUtils;
import com.intelliquor.cloud.shop.common.utils.GuotaiUtil;
import com.intelliquor.cloud.shop.terminal.dao.*;
import com.intelliquor.cloud.shop.terminal.model.TerminalProtocolChangeModel;
import com.intelliquor.cloud.shop.terminal.model.TerminalShopContractModel;
import com.intelliquor.cloud.shop.terminal.model.TerminalShopInfoScheduleModel;
import com.intelliquor.cloud.shop.terminal.model.TerminalShopModel;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalDataLogReq;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalProtocolChangeReq;
import com.intelliquor.cloud.shop.terminal.model.resp.*;
import com.intelliquor.cloud.shop.terminal.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.Year;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TerminalProtocolChangeServiceImpl implements TerminalProtocolChangeService {

    @Autowired
    private TerminalProtocolChangeDao terminalProtocolChangeDao;

    @Autowired
    private TerminalShopInfoScheduleDao terminalShopInfoScheduleDao;

    @Autowired
    private TerminalProtocolDao terminalProtocolDao;

    @Autowired
    private TerminalShopDao terminalShopDao;

    @Autowired
    private TerminalShopNodeService terminalShopNodeService;

    @Autowired
    private TerminalShopNodeDao terminalShopNodeDao;

    @Autowired
    private TerminalAccountManagerDao terminalAccountManagerDao;

    @Autowired
    private UserContext userContext;

    @Autowired
    private TerminalShopContractDao terminalShopContractDao;


    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private IRequestLogService requestLogService;

    @Autowired
    private TerminalShopService terminalShopService;

    @Resource
    private TerminalShopDisableRecordCommonDao terminalShopDisableRecordCommonDao;

    @Resource
    private TerminalShopMergeDao terminalShopMergeDao;

    @Autowired
    private ITerminalProductProtocolRelationService terminalProductProtocolRelationService;

    @Autowired
    private TerminalProductProtocolRelationChangeDetailDao terminalProductProtocolRelationChangeDetailDao;

    @Autowired
    private TerminalProductProtocolRelationChangeDao terminalProductProtocolRelationChangeDao;

    @Autowired
    private TerminalProtocolActivityRelationService terminalProtocolActivityRelationService;

    @Autowired
    private ITerminalProtocolActivityRelationService iTerminalProtocolActivityRelationService;

    @Override
    public TerminalProtocolChangeResp selectTerminalProtocolById(Integer terminalShopId, Integer sortType) {
        // 查询终端基本信息
        TerminalShopResp terminalShopResp = terminalShopDao.selectTerminalShopById(terminalShopId);
        // 复制基本信息到TerminalProtocolChangeResp
        TerminalProtocolChangeResp changeModel = new TerminalProtocolChangeResp();
        BeanUtils.copyProperties(terminalShopResp, changeModel);
        // 查询经销商编码 主品合同或者酱品
        TerminalShopContractResp primaryContractModel = terminalShopContractDao.selectTerminalShopContractResp(terminalShopResp.getId());
        changeModel.setDealerCode(primaryContractModel.getDealerCode());
        // 查询终端配置的协议列表
        List<TerminalProtocolResp> protocolList = terminalProtocolChangeDao.selectTerminalProtocolListByShopId(terminalShopId, sortType);
        changeModel.setTerminalProtocolList(protocolList);

        //终端十五年不允许新增附加协议
        if (terminalShopResp.getShopType() == 6 || terminalShopResp.getShopType() == 7 || terminalShopResp.getShopType() == 8) {
            changeModel.setIsAddDeputyProtocolDetailStatus(false);
        } else {
            changeModel.setIsAddDeputyProtocolDetailStatus(true);
        }
        return changeModel;
    }

    @Override
    @Transactional
    public void insertTerminalProtocol(TerminalProtocolChangeReq changeReq) {
        StopWatch watch = new StopWatch();
        RequestLog requestLog = new RequestLog();
        requestLog.setReqName("新增协议:" + changeReq.getTerminalShopId());
        requestLog.setReqType(49);
        requestLog.setReqUrlPath("TerminalProtocolChangeServiceImpl->insertTerminalProtocol");
        requestLog.setCreateDate(new Date());
        requestLog.setReqJson(JSON.toJSONString(changeReq));
        requestLog.setReqKey(changeReq.getTerminalShopId()+"");
        String reMsg = "";
        String redisKey = "";
        Integer id = 0;
        try{

            // 是否是客户经理
            Boolean isManage = false;
            if(userContext.getTerminalModel().getType() == 0){//客户经理  不需要确认
                isManage = true;
            }

            // 团购终端和会员终端不能进行协议变更
            TerminalShopResp terminalShopResp = terminalShopDao.selectTerminalShopById(changeReq.getTerminalShopId());
            if (terminalShopResp != null && (terminalShopResp.getShopType() == 2 || terminalShopResp.getShopType() == 5)) {
                throw new BusinessException("400", "团购终端和会员终端不能进行新增协议");
            }
            if (Objects.isNull(changeReq.getEffectiveTime())) {
                throw new BusinessException("400", "请选择协议生效的时间");
            }
            //如果是终端十五年，则不能新增附加协议 2023/9/27 sunshine
            if(terminalShopResp !=null
                    && changeReq.getProtocolType() == 1
                    && (terminalShopResp.getShopType() == 6
                        || terminalShopResp.getShopType() == 7
                        || terminalShopResp.getShopType() == 8)) {
                throw new BusinessException("400", "终端十五年不允许添加附加协议");
            }
            id = terminalShopResp.getId();
            redisKey =  String.format(RedisConstant.REDIS_ADD_UPDATE_PROTOCOL, id);
            Boolean aBoolean = redisTemplate.opsForValue().setIfAbsent(redisKey, terminalShopResp.getId(), 5, TimeUnit.MINUTES);
            if (!aBoolean) {//存在说明重复激活
                requestLog.setReqName("【重复】"+requestLog.getReqName());
                throw new BusinessException("400", "十分钟内只允许更改一次，请勿频繁更改");
            }

            // 查询终端配置的协议列表: 若有审核中的协议则不能新增
            checkTerminalProtocolIsChecking(changeReq.getTerminalShopId(), changeReq.getProductProtocolConfigId());

            // 新增主协议时
            if (changeReq.getProtocolType() == 0 && isManage) {
                // 查询是否之前是否有已经通过审核并且还没有删除的主协议
                QueryWrapper<TerminalProtocolModel> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("terminal_shop_id", changeReq.getTerminalShopId());
                queryWrapper.eq("protocol_type", 0);
                queryWrapper.eq("delete_status", 0);
                queryWrapper.eq("check_status", 1);
                List<TerminalProtocolModel> list = terminalProtocolDao.selectList(queryWrapper);
                reMsg = reMsg.concat("已存在"+list+"条主协议");
                //处理特殊情况  新增时也有生效的主协议
                for (TerminalProtocolModel protocolModel : list) {
                    reMsg = reMsg.concat(","+protocolModel.getId());
                    protocolModel.setRemark(protocolModel.getRemark()+">"+DateUtils.convert2StringYYYYMMddHHmmss(new Date())+"又新增了主协议,更新为无效");
                    protocolModel.setDeleteStatus(1);
                    terminalProtocolDao.updateById(protocolModel);
                }
                /*if (CollectionUtils.isNotEmpty(list)) {
                    // 1: 直接删除之前的，逻辑删除
                    for (TerminalProtocolModel protocolModel : list) {
                        terminalProtocolDao.deleteProtocolById(protocolModel.getId());
                    }
                    // 2：抛异常
                    // throw new BusinessException("终端已存在主协议");
                }*/
            }

            TerminalAccountManagerModel terminalModel = userContext.getTerminalModel();
            // 设置审核状态为客户经理审核
            changeReq.setCheckStatus(2);
            // 未删除
            changeReq.setDeleteStatus(0);
            // 新增时完成状态为未完成
            changeReq.setEndStatus(1);
            // 创建人
            changeReq.setCreateUser(terminalModel.getId());
            // 创建时间
            changeReq.setCreateTime(new Date());
            log.info("改变的协议数据生效时间：：：{}------》{}", changeReq.getEffectiveTime(),JSONObject.toJSONString(changeReq));
            TerminalProtocolModel model = changeReq2Model(changeReq);
            // 插入到`t_terminal_protocol`表中
            model.setMemberShopId(Long.valueOf(terminalShopResp.getMemberShopId()));
            log.info("改变的协议插入的数据：：：{}------》{}", model.getEffectiveTime(),JSONObject.toJSONString(model));
            model.setSourceType(1);
            if(isManage){
                model.setCheckStatus(1);
                model.setEndStatus(2);
                model.setRemark("自动通过");
            }
            terminalProtocolDao.insert(model);
            // 中台审核同意后再插入到`t_terminal_protocol_change`表中
            TerminalProtocolChangeModel changeModel = new TerminalProtocolChangeModel();
            changeModel.setNewId(model.getId().intValue());
            changeModel.setChangeType(0);
            changeModel.setApprovalStatus(1);
            if(isManage){
                changeModel.setApprovalStatus(3);
            }

            terminalProtocolChangeDao.insertTerminalProtocolChange(changeModel);
            // 查询scheduleShopId
            Integer scheduleId = terminalShopInfoScheduleDao.getTerminalShopScheduleIdByTerminalShopId(changeReq.getTerminalShopId());
            // 插入到`t_terminal_shop_node`表
            TerminalShopNodeModel nodeModel = TerminalShopNodeModel.builder()
                    .terminalShopId(changeReq.getTerminalShopId())
                    .scheduleShopId(scheduleId)
                    .protocolId(model.getId().intValue())
                    .companyId(changeReq.getCompanyId())
                    .nodeName("终端协议新增申请")
                    .nodeLevel("0")
                    .nodeType(4)
                    .isBack("0")
                    .nodeStatus("0")
                    .createTime(new Date())
                    .dataLogId(0)
                    .build();
            setMangerInfo(userContext.getTerminalModel().getId(), nodeModel);
            if(isManage){
                nodeModel.setApprovalUser(userContext.getTerminalModel().getId());
                nodeModel.setNodeStatus("1");
                nodeModel.setUpdateMsg("自动审批通过");
                nodeModel.setUpdateDate(new Date());
                Integer protocolType = model.getProtocolType();
                if (protocolType == 0) {
                    TerminalShopModel terminalShopModel = terminalShopDao.selectById(model.getTerminalShopId());
                    TerminalShopInfoScheduleModel scheduleModel = new TerminalShopInfoScheduleModel();
                    BeanUtils.copyProperties(terminalShopModel, scheduleModel);
                    scheduleModel.setId(null);
                    scheduleModel.setTerminalShopId(model.getTerminalShopId());
                    LambdaQueryWrapper<TerminalShopContractModel> qw = Wrappers.lambdaQuery();
                    qw.eq(TerminalShopContractModel::getTerminalShopId, model.getTerminalShopId());
                    qw.orderByDesc(TerminalShopContractModel::getId);
                    List<TerminalShopContractModel> contractModelList = terminalShopContractDao.selectList(qw);
                    TerminalShopContractResp terminalShopContractResp = new TerminalShopContractResp();
                    if (contractModelList.size() > 0) {
                        TerminalShopContractModel contractModel = contractModelList.get(0);
                        BeanUtils.copyProperties(contractModel, terminalShopContractResp);
                    }
                    terminalShopService.sendZtData(scheduleModel, terminalShopModel, terminalShopContractResp, 1, "协议", model);
                }
                terminalShopService.sendProtocolMessage(model, 1);
            }

            terminalShopNodeService.save(nodeModel);
            requestLog.setResCode("0");
            redisTemplate.delete(redisKey);
            /*if(userContext.getTerminalModel().getType() == 0){ //客户经理  不需要确认
                TerminalShopNodeReq terminalShopNodeReq = new TerminalShopNodeReq();
                BeanUtils.copyProperties(nodeModel, terminalShopNodeReq);
                terminalShopNodeReq.setNodeStatus("1");
                terminalShopNodeReq.setIsBack("0");
                terminalShopNodeReq.setUpdateMsg("自动通过");
                terminalShopService.approvalTerminalShop(terminalShopNodeReq);
            }*/
        }catch (Exception e){
            e.printStackTrace();
            requestLog.setResCode("-1");
            requestLog.setResCode("新增协议失败:"+e.getMessage());
            throw new BusinessException("新增协议失败:"+e.getMessage());
        }finally {
            requestLog.setResMsg(reMsg);
            String diffTime = DateUtils.longToHMS(watch.getTotalTimeMillis());
            requestLog.setReqTime(diffTime);
            requestLogService.insertLog(requestLog);
        }

    }

    @Override
    @Transactional
    public void updateTerminalProtocol(TerminalProtocolChangeReq changeReq) {
        StopWatch watch = new StopWatch();
        RequestLog requestLog = new RequestLog();
        requestLog.setReqName("变更协议:" + changeReq.getTerminalShopId());
        requestLog.setReqType(49);
        requestLog.setReqUrlPath("TerminalProtocolChangeServiceImpl->updateTerminalProtocol");
        requestLog.setCreateDate(new Date());
        requestLog.setReqJson(JSON.toJSONString(changeReq));
        requestLog.setReqKey(changeReq.getTerminalShopId()+"");
        String redisTerminalIdKey = "";
        Integer id = 0;
        String reMsg = "";
        try{
            // 是否是客户经理
            Boolean isManage = false;
            if(userContext.getTerminalModel().getType() == 0){//客户经理  不需要确认
                isManage = true;
            }
            // 查询旧协议
            if (changeReq.getOldId() == null) {
                // 主协议更新时，如果oldId为空则是新增否则走更新逻辑
                if (changeReq.getProtocolType() == 0) {
                    requestLog.setReqName("【新增】协议:" + changeReq.getTerminalShopId());
                    insertTerminalProtocol(changeReq);
                    requestLog.setResCode("0");
                    return;
                }
                throw new BusinessException("400", "旧协议id不能为空");
            }
            TerminalProtocolModel oldData = terminalProtocolDao.selectById(changeReq.getOldId());
            // 为null则表示没有审核通过
            if (oldData == null) {
                throw new BusinessException("400", "旧协议id错误");
            }
            // 旧协议必须审核通过后才能进行变更
            Integer checkStatus = oldData.getCheckStatus();
            if (checkStatus != 1 && checkStatus != 3 && checkStatus != 5) {
                throw new BusinessException("400", "当前协议审核未通过");
            }
            TerminalShopResp terminalShopResp = terminalShopDao.selectTerminalShopById(oldData.getTerminalShopId());
            Integer protocolUpdateStatus = terminalShopResp.getProtocolUpdateStatus();
            if(protocolUpdateStatus == 0 && checkStatus == 1){
                throw new BusinessException("400", "该终端的主协议不能修改");
            }
            // 团购终端和会员终端不能进行协议变更
            if (terminalShopResp != null && (terminalShopResp.getShopType() == 2 || terminalShopResp.getShopType() == 5)) {
                throw new BusinessException("400", "团购终端和会员终端不能进行协议变更");
            }
            id = terminalShopResp.getId();
            redisTerminalIdKey = String.format(RedisConstant.REDIS_ADD_UPDATE_PROTOCOL, id);

            Boolean aBoolean = redisTemplate.opsForValue().setIfAbsent(redisTerminalIdKey, id, 5, TimeUnit.MINUTES);
            if (!aBoolean) {
                requestLog.setReqName("【重复】"+requestLog.getReqName());
                throw new BusinessException("400", "十分钟内只允许更改一次，请勿频繁更改");
            }

            // 查询终端配置的协议列表: 若有审核中的协议则不能新增
            checkTerminalProtocolIsChecking(changeReq.getTerminalShopId(), changeReq.getProductProtocolConfigId());

            TerminalAccountManagerModel terminalModel = userContext.getTerminalModel();
            oldData.setUpdateUser(terminalModel.getId());
            oldData.setUpdateTime(LocalDateTime.now());
            /*
             逻辑有问题  新协议没有审批通过  不能删除之前的协议
            // 删除终端协议配置表中对应的旧数据（逻辑删除）
            terminalProtocolChangeDao.deleteTerminalProtocolById(oldData);
            */

            // 设置审核状态为客户经理审核
            changeReq.setCheckStatus(2);
            // 设置删除状态
            changeReq.setDeleteStatus(0);
            // 新增时完成状态为未完成
            changeReq.setEndStatus(1);
            // 创建人
            changeReq.setCreateUser(terminalModel.getId());
            // 创建时间
            changeReq.setCreateTime(new Date());
            log.info("改变的协议数据{}", JSONObject.toJSONString(changeReq));
            TerminalProtocolModel model = changeReq2Model(changeReq);
            // 插入到`t_terminal_protocol`表中
            model.setMemberShopId(oldData.getMemberShopId());
            log.info("改变的协议插入的数据{}", JSONObject.toJSONString(model));
            model.setSourceType(2);
            if(isManage){
                model.setCheckStatus(1);
                model.setEndStatus(2);
                model.setRemark("自动通过");
                if(model.getProtocolType() == 0){//主协议 特殊处理
                    QueryWrapper<TerminalProtocolModel> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("terminal_shop_id", model.getTerminalShopId());
                    queryWrapper.eq("protocol_type", 0);
                    queryWrapper.eq("delete_status", 0);
                    queryWrapper.eq("check_status", 1);
                    List<TerminalProtocolModel> list = terminalProtocolDao.selectList(queryWrapper);
                    reMsg = reMsg.concat("已存在"+list+"条主协议");
                    //处理特殊情况  新增时也有生效的主协议
                    for (TerminalProtocolModel protocolModel : list) {
                        reMsg = reMsg.concat(","+protocolModel.getId());
                        protocolModel.setRemark(protocolModel.getRemark()+">"+DateUtils.convert2StringYYYYMMddHHmmss(new Date())+"又更新了主协议,更新为无效，客户经理直接通过");
                        protocolModel.setDeleteStatus(1);
                        terminalProtocolDao.updateById(protocolModel);
                    }
                }
            }
            terminalProtocolDao.insert(model);
            // 插入到`t_terminal_protocol_change`表中
            TerminalProtocolChangeModel changeModel = new TerminalProtocolChangeModel();
            changeModel.setNewId(model.getId().intValue());
            changeModel.setOldId(changeReq.getOldId());
            changeModel.setChangeType(1);
            changeModel.setApprovalStatus(1);
            if(isManage){
                changeModel.setApprovalStatus(3);
            }
            terminalProtocolChangeDao.insertTerminalProtocolChange(changeModel);
            // 查询scheduleShopId
            Integer scheduleId = terminalShopInfoScheduleDao.getTerminalShopScheduleIdByTerminalShopId(changeReq.getTerminalShopId());
            // 插入到`t_terminal_shop_node`表
            TerminalShopNodeModel nodeModel = TerminalShopNodeModel.builder()
                    .terminalShopId(changeReq.getTerminalShopId())
                    .scheduleShopId(scheduleId)
                    .protocolId(model.getId().intValue())
                    .companyId(changeReq.getCompanyId())
                    .nodeName("终端协议变更申请")
                    .nodeLevel("0")
                    .nodeType(2)
                    .isBack("0")
                    .nodeStatus("0")
                    .createTime(new Date())
                    .dataLogId(0)
                    .build();
            setMangerInfo(userContext.getTerminalModel().getId(), nodeModel);
            if(isManage){
                nodeModel.setApprovalUser(userContext.getTerminalModel().getId());
                nodeModel.setNodeStatus("1");
                nodeModel.setUpdateMsg("自动审批通过");
                nodeModel.setUpdateDate(new Date());
                Integer protocolType = model.getProtocolType();
                if (protocolType == 0) {
                    TerminalShopModel terminalShopModel = terminalShopDao.selectById(model.getTerminalShopId());
                    TerminalShopInfoScheduleModel scheduleModel = new TerminalShopInfoScheduleModel();
                    BeanUtils.copyProperties(terminalShopModel, scheduleModel);
                    scheduleModel.setId(null);
                    scheduleModel.setTerminalShopId(model.getTerminalShopId());
                    LambdaQueryWrapper<TerminalShopContractModel> qw = Wrappers.lambdaQuery();
                    qw.eq(TerminalShopContractModel::getTerminalShopId, model.getTerminalShopId());
                    qw.orderByDesc(TerminalShopContractModel::getId);
                    List<TerminalShopContractModel> contractModelList = terminalShopContractDao.selectList(qw);
                    TerminalShopContractResp terminalShopContractResp = new TerminalShopContractResp();
                    if (contractModelList.size() > 0) {
                        TerminalShopContractModel contractModel = contractModelList.get(0);
                        BeanUtils.copyProperties(contractModel, terminalShopContractResp);
                    }
                    terminalShopService.sendZtData(scheduleModel, terminalShopModel, terminalShopContractResp, 1, "协议", model);
                }

                // 更新以前的协议无效
                Integer oldId = changeReq.getOldId();
                if(Objects.nonNull(oldId)){
                    TerminalProtocolModel oldTerminalProtocol = terminalProtocolDao.selectById(oldId);
                    if(Objects.nonNull(oldTerminalProtocol)){
                        oldTerminalProtocol.setRemark(oldTerminalProtocol.getRemark()+">"+DateUtils.convert2StringYYYYMMddHHmmss(new Date())+"激活了id="+model.getId()+"的协议，此协议无效。");
                        oldTerminalProtocol.setDeleteStatus(1);
                        terminalProtocolDao.updateById(oldTerminalProtocol);
                    }
                }

                terminalShopService.sendProtocolMessage(model, 1);
            }

            terminalShopNodeService.save(nodeModel);
            requestLog.setResCode("0");
            redisTemplate.delete(redisTerminalIdKey);

            /*if(userContext.getTerminalModel().getType() == 0){ //客户经理  不需要确认
                TerminalShopNodeReq terminalShopNodeReq = new TerminalShopNodeReq();
                BeanUtils.copyProperties(nodeModel, terminalShopNodeReq);
                terminalShopNodeReq.setNodeStatus("1");
                terminalShopNodeReq.setIsBack("0");
                terminalShopNodeReq.setUpdateMsg("自动通过");
                terminalShopService.approvalTerminalShop(terminalShopNodeReq);
            }*/
        }catch (Exception e){
            e.printStackTrace();
            requestLog.setResCode("-1");
            requestLog.setResMsg("更新协议失败:"+e.getMessage());
            throw new BusinessException("更新协议失败:"+e.getMessage());
        }finally {
            requestLog.setResMsg(reMsg);
            String diffTime = DateUtils.longToHMS(watch.getTotalTimeMillis());
            requestLog.setReqTime(diffTime);
            requestLogService.insertLog(requestLog);
        }
    }

    @Override
    public TerminalProtocolChangeResp getTerminalProtocolChangeDetail(TerminalDataLogReq req) {
        if (req.getTerminalShopId() == null) {
            throw new BusinessException("400", "终端id不能为空");
        }
        if (req.getProtocolId() == null) {
            throw new BusinessException("400", "protocolId不能为空");
        }
        // 查询终端基本信息
        TerminalShopResp terminalShopResp = terminalShopDao.selectTerminalShopById(req.getTerminalShopId());
        if (terminalShopResp == null) {
            throw new BusinessException("400", "终端id错误");
        }
        // 复制基本信息到TerminalProtocolChangeResp
        TerminalProtocolChangeResp changeModel = new TerminalProtocolChangeResp();
        BeanUtils.copyProperties(terminalShopResp, changeModel);

        // 查询当前协议
        TerminalProtocolChangeDetailResp curProtocol = terminalProtocolChangeDao.selectDetailById(req.getProtocolId());
        changeModel.setNewProtocol(curProtocol);
        // 查询之前协议
        Integer oldId = terminalProtocolChangeDao.getOldIdByNewId(req.getProtocolId());
        TerminalProtocolChangeDetailResp oldProtocol = terminalProtocolChangeDao.selectDetailById(oldId);
        changeModel.setOldProtocol(oldProtocol);
        // 通过req获取
        // 如果状态是协议变更
        // 获取当前节点的新协议ids
//        TerminalShopNodeModel currentTerminalShopNodeModel = terminalShopNodeDao.selectById(req.getNodeId());
//        if(currentTerminalShopNodeModel.getNodeType().equals(14)){
//            List<String> protocolIds = Arrays.asList(currentTerminalShopNodeModel.getProtocolIds().split(",")); // getProtocolIds()返回的是逗号分隔的字符串
//            LambdaQueryWrapper<TerminalProductProtocolRelationChangeDetailModel> queryWrapper = new LambdaQueryWrapper<>();
//            queryWrapper.in(TerminalProductProtocolRelationChangeDetailModel::getId, protocolIds);
//
//            List<TerminalProductProtocolRelationChangeDetailModel> detailModelList = terminalProductProtocolRelationChangeDetailDao.selectList(queryWrapper);
//            if (CollectionUtils.isNotEmpty(detailModelList)) {
//                List<Long> newIdList = new ArrayList<>();
//                List<Long> oldIdList = new ArrayList<>();
//                detailModelList.forEach(detailModel -> {
//                    newIdList.add(detailModel.getNewId());
//                    oldIdList.add(detailModel.getOldId());
//                });
//                String newIds = newIdList.stream()
//                        .map(Object::toString) // 将Long转换为String
//                        .collect(Collectors.joining(","));;
//                String oldIds = oldIdList.stream()
//                        .map(Object::toString) // 将Long转换为String
//                        .collect(Collectors.joining(","));;
//
//                //查询终端协议新信息
//                List<TerminalProductProtocolRelationResp> terminalNewProductProtocolRelationList = terminalProductProtocolRelationService.selectTerminalProductProtocolRelationByIds(newIds);
//                if (CollectionUtils.isNotEmpty(terminalNewProductProtocolRelationList)) {
//                    changeModel.setNewTerminalProductProtocolRelationList(terminalNewProductProtocolRelationList);
//                }
//
//                //查询终端协议旧信息
//                List<TerminalProductProtocolRelationResp> terminalOldProductProtocolRelationList = terminalProductProtocolRelationService.selectTerminalProductProtocolRelationByIds(oldIds);
//                if (CollectionUtils.isNotEmpty(terminalOldProductProtocolRelationList)) {
//                    changeModel.setOldTerminalProductProtocolRelationList(terminalOldProductProtocolRelationList);
//                }
//            }
//        }

        // 查询变更类型
        Integer changeType = terminalProtocolChangeDao.getChangeTypeByTerminalShopId(req.getProtocolId());
        changeModel.setChangeType(changeType);
        // 查询经销商编码 主品合同或者酱品
        TerminalShopContractResp primaryContractModel = terminalShopContractDao.selectTerminalShopContractResp(terminalShopResp.getId());
        changeModel.setDealerCode(primaryContractModel.getDealerCode());
        // 查询审核记录
        TerminalShopNodeReq terminalShopNodeReq = new TerminalShopNodeReq();
        terminalShopNodeReq.setTerminalShopId(req.getTerminalShopId());
        terminalShopNodeReq.setProtocolId(req.getProtocolId());
        terminalShopNodeReq.setNodeType(req.getCheckType());
        List<TerminalShopNodeResp> terminalShopNodeRespList = terminalShopNodeDao.selectTerminalShopNodeList(terminalShopNodeReq);
        changeModel.setTerminalShopNodeList(terminalShopNodeRespList);
        if(Objects.nonNull(req.getCheckType())){
            TerminalShopNodeModel terminalShopNodeModel = terminalShopNodeDao.selectById(req.getNodeId());
            //启用禁用审核信息
            if(req.getCheckType().equals(9) || req.getCheckType().equals(10)){
                TerminalShopDisableRecordCommonModel disableRecord = terminalShopDisableRecordCommonDao.selectById(terminalShopNodeModel.getNodeSourceId());
                changeModel.setDisableRecord(disableRecord);
            }
            //合并信息
            if(req.getCheckType().equals(8)){
                TerminalShopMergeModel mergeRecord = terminalShopMergeDao.selectMergeInfoById(terminalShopNodeModel.getNodeSourceId());
                changeModel.setMergeRecord(mergeRecord);
            }
        }
        return changeModel;
    }

    @Override
    public TerminalProtocolChangeResp getTerminalProtocolChangeDetailNewProtocol(TerminalDataLogReq req) {
        if (req.getTerminalShopId() == null) {
            throw new BusinessException("400", "终端id不能为空");
        }
        if (req.getProtocolId() == null) {
            throw new BusinessException("400", "protocolId不能为空");
        }
        // 查询终端基本信息
        TerminalShopResp terminalShopResp = terminalShopDao.selectTerminalShopById(req.getTerminalShopId());
        if (terminalShopResp == null) {
            throw new BusinessException("400", "终端id错误");
        }
        // 复制基本信息到TerminalProtocolChangeResp
        TerminalProtocolChangeResp changeModel = new TerminalProtocolChangeResp();
        BeanUtils.copyProperties(terminalShopResp, changeModel);

        // 获取当前节点的新协议ids
        TerminalShopNodeModel currentTerminalShopNodeModel = terminalShopNodeDao.selectById(req.getNodeId());
        if(ObjectUtil.isNull(currentTerminalShopNodeModel)){
            throw new BusinessException("400", "找不到对应节点记录");
        }

        // 查询当前协议
        List<TerminalProductProtocolRelationResp> newTerminalProductProtocolRelationRespList = terminalProductProtocolRelationService.selectTerminalProductProtocolRelationByIds(currentTerminalShopNodeModel.getProtocolIds());
        if(CollectionUtils.isNotEmpty(newTerminalProductProtocolRelationRespList)) {
            changeModel.setNewTerminalProductProtocolRelationList(newTerminalProductProtocolRelationRespList);
        }
        // 获取上一年进货量
        String lastYearString = String.valueOf(Year.now().minusYears(1).getValue());
        Integer lastYearPurchaseQuantity = terminalShopService.getYearReceiveNum(terminalShopResp.getMemberShopId().intValue(), lastYearString);
        changeModel.setLastYearPurchaseQuantity(lastYearPurchaseQuantity);
        // 查询之前协议
        Integer oldId = terminalProtocolChangeDao.getOldIdByNewId(req.getProtocolId());
        // 如果状态是协议变更
        // 获取当前节点的新协议ids
        if(currentTerminalShopNodeModel.getNodeType().equals(14)){
            List<String> protocolIds = Arrays.asList(currentTerminalShopNodeModel.getProtocolIds().split(",")); // getProtocolIds()返回的是逗号分隔的字符串
            LambdaQueryWrapper<TerminalProductProtocolRelationChangeDetailModel> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(TerminalProductProtocolRelationChangeDetailModel::getId, protocolIds);

            List<TerminalProductProtocolRelationChangeDetailModel> detailModelList = terminalProductProtocolRelationChangeDetailDao.selectList(queryWrapper);
            if (CollectionUtils.isNotEmpty(detailModelList)) {
                List<Long> newIdList = new ArrayList<>();
                List<Long> oldIdList = new ArrayList<>();
                // 取changeId
                Long changeId = detailModelList.get(0).getChangeId();
                detailModelList.forEach(detailModel -> {
                    newIdList.add(detailModel.getNewId());
                    oldIdList.add(detailModel.getOldId());

                });
                // newIdList与oldIdList为空则抛错
                if (CollectionUtils.isEmpty(newIdList) || CollectionUtils.isEmpty(oldIdList)) {
                    throw new BusinessException("400", "找不到对应协议");
                }
                String newIds = newIdList.stream()
                        .filter(Objects::nonNull)
                        .map(Object::toString)
                        .collect(Collectors.joining(","));
                String oldIds = oldIdList.stream()
                        .filter(Objects::nonNull)
                        .map(Object::toString)
                        .collect(Collectors.joining(","));

                //查询终端协议新信息
                List<TerminalProductProtocolRelationResp> terminalNewProductProtocolRelationList = terminalProductProtocolRelationService.selectTerminalProductProtocolRelationByIds(newIds);
                if (CollectionUtils.isNotEmpty(terminalNewProductProtocolRelationList)) {
                    changeModel.setNewTerminalProductProtocolRelationList(terminalNewProductProtocolRelationList);
                }

                //查询终端协议旧信息
                List<TerminalProductProtocolRelationResp> terminalOldProductProtocolRelationList = terminalProductProtocolRelationService.selectTerminalProductProtocolRelationByIds(oldIds);
                if (CollectionUtils.isNotEmpty(terminalOldProductProtocolRelationList)) {
                    changeModel.setOldTerminalProductProtocolRelationList(terminalOldProductProtocolRelationList);
                }
                // 获取终端信息并塞进去
                TerminalProductProtocolRelationChangeModel terminalProductProtocolRelationChangeModel = terminalProductProtocolRelationChangeDao.selectById(changeId);
                changeModel.setCheckStatus(terminalProductProtocolRelationChangeModel.getCheckStatus());
                changeModel.setCheckStatusName(ProtocolCheckStatusEnum.getValue(terminalProductProtocolRelationChangeModel.getCheckStatus()));
            }
        }

        // TODO新的协议变更记录逻辑
        //if(Objects.nonNull(oldId)) {
        //    List<TerminalProductProtocolRelationResp> oldTerminalProductProtocolRelationRespList = terminalProductProtocolRelationService.selectTerminalProductProtocolRelationByIds(oldId);
        //    if (CollectionUtils.isNotEmpty(oldTerminalProductProtocolRelationRespList)) {
        //        changeModel.setOldTerminalProductProtocolRelationList(oldTerminalProductProtocolRelationRespList);
        //    }
        //}
        // 查询变更类型
        Integer changeType = terminalProtocolChangeDao.getChangeTypeByTerminalShopId(req.getProtocolId());
        changeModel.setChangeType(changeType);
        // 查询经销商编码 主品合同或者酱品
        TerminalShopContractResp primaryContractModel = terminalShopContractDao.selectTerminalShopContractResp(terminalShopResp.getId());
        changeModel.setDealerCode(primaryContractModel.getDealerCode());
        // 查询审核记录
        TerminalShopNodeReq terminalShopNodeReq = new TerminalShopNodeReq();
        terminalShopNodeReq.setTerminalShopId(req.getTerminalShopId());
        terminalShopNodeReq.setProtocolId(req.getProtocolId());
        terminalShopNodeReq.setNodeType(req.getCheckType());
        terminalShopNodeReq.setId(req.getNodeId());
        List<TerminalShopNodeResp> terminalShopNodeRespList = terminalShopNodeDao.selectTerminalShopNodeList(terminalShopNodeReq);
        changeModel.setTerminalShopNodeList(terminalShopNodeRespList);
        if(Objects.nonNull(req.getCheckType())){
            TerminalShopNodeModel terminalShopNodeModel = terminalShopNodeDao.selectById(req.getNodeId());
            //启用禁用审核信息
            if(req.getCheckType().equals(9) || req.getCheckType().equals(10)){
                TerminalShopDisableRecordCommonModel disableRecord = terminalShopDisableRecordCommonDao.selectById(terminalShopNodeModel.getNodeSourceId());
                changeModel.setDisableRecord(disableRecord);
            }
            //合并信息
            if(req.getCheckType().equals(8)){
                TerminalShopMergeModel mergeRecord = terminalShopMergeDao.selectMergeInfoById(terminalShopNodeModel.getNodeSourceId());
                changeModel.setMergeRecord(mergeRecord);
            }
        }
        return changeModel;
    }

    @Override
    public TerminalProtocolChangeResp getTerminalActiveProtocol(TerminalDataLogReq req) {
        if (req.getTerminalShopId() == null) {
            throw new BusinessException("400", "终端id不能为空");
        }
        if (req.getProtocolId() == null) {
            throw new BusinessException("400", "protocolId不能为空");
        }
        // 查询终端基本信息
        TerminalShopResp terminalShopResp = terminalShopDao.selectTerminalShopById(req.getTerminalShopId());
        if (terminalShopResp == null) {
            throw new BusinessException("400", "终端id错误");
        }
        // 复制基本信息到TerminalProtocolChangeResp
        TerminalProtocolChangeResp changeModel = new TerminalProtocolChangeResp();
        BeanUtils.copyProperties(terminalShopResp, changeModel);

        // 获取当前节点的新协议ids
        TerminalShopNodeModel currentTerminalShopNodeModel = terminalShopNodeDao.selectById(req.getNodeId());

        // 查询当前协议
        if(currentTerminalShopNodeModel.getNodeType() == TerminalShopNodeEnum.ACTIVITY_PROTOCOL.getType()) {
            List<TerminalProtocolActivityRelationModel> terminalProtocolActiveRelationList = terminalProtocolActivityRelationService.getTerminalProtocolActivityRelationListByIds(currentTerminalShopNodeModel.getProtocolIds(), DeleteFlagEnum.NOT_DELETE.getKey());
            if (CollectionUtils.isNotEmpty(terminalProtocolActiveRelationList)) {
                List<TerminalProtocolActiveRelationResp> terminalProtocolActiveRelationRespList = new ArrayList<>();
                for(TerminalProtocolActivityRelationModel model : terminalProtocolActiveRelationList) {
                    TerminalProtocolActiveRelationResp resp = iTerminalProtocolActivityRelationService.fillActivityInfo(model);
                    terminalProtocolActiveRelationRespList.add(resp);
                }
                changeModel.setNewTerminalProtocolActiveRelationList(terminalProtocolActiveRelationRespList);
            }
        } else if(currentTerminalShopNodeModel.getNodeType() == TerminalShopNodeEnum.ACTIVITY_PROTOCOL_EDIT.getType()) {
            // 如果状态是协议变更
            // 获取当前节点的新协议ids
            List<String> protocolIds = Arrays.asList(currentTerminalShopNodeModel.getProtocolIds().split(",")); // getProtocolIds()返回的是逗号分隔的字符串
            LambdaQueryWrapper<TerminalProductProtocolRelationChangeDetailModel> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(TerminalProductProtocolRelationChangeDetailModel::getId, protocolIds);

            List<TerminalProductProtocolRelationChangeDetailModel> detailModelList = terminalProductProtocolRelationChangeDetailDao.selectList(queryWrapper);
            if (CollectionUtils.isNotEmpty(detailModelList)) {
                List<Long> newIdList = new ArrayList<>();
                List<Long> oldIdList = new ArrayList<>();
                // 取changeId
                Long changeId = detailModelList.get(0).getChangeId();
                detailModelList.forEach(detailModel -> {
                    newIdList.add(detailModel.getNewId());
                    oldIdList.add(detailModel.getOldId());

                });
                // newIdList与oldIdList为空则抛错
                String newIds = "";
                String oldIds = "";

                if (CollectionUtils.isNotEmpty(newIdList)) {
                    newIds = newIdList.stream()
                            .filter(Objects::nonNull)
                            .map(Object::toString)
                            .collect(Collectors.joining(","));
                }
                if (CollectionUtils.isNotEmpty(oldIdList)) {
                    oldIds = oldIdList.stream()
                            .filter(Objects::nonNull)
                            .map(Object::toString)
                            .collect(Collectors.joining(","));
                }

                //查询终端协议新信息
                //List<TerminalProductProtocolRelationResp> terminalNewProductProtocolRelationList = terminalProductProtocolRelationService.selectTerminalProductProtocolRelationByIds(newIds);
                //if (CollectionUtils.isNotEmpty(terminalNewProductProtocolRelationList)) {
                //    changeModel.setNewTerminalProductProtocolRelationList(terminalNewProductProtocolRelationList);
                //}

                // 查询终端新的活动协议信息
                List<TerminalProtocolActivityRelationModel> terminalNewProtocolActiveRelationList = terminalProtocolActivityRelationService.getTerminalProtocolActivityRelationListByIds(newIds, DeleteFlagEnum.NOT_DELETE.getKey());
                if (CollectionUtils.isNotEmpty(terminalNewProtocolActiveRelationList)) {
                    List<TerminalProtocolActiveRelationResp> terminalProtocolActiveRelationRespList = new ArrayList<>();
                    for(TerminalProtocolActivityRelationModel model : terminalNewProtocolActiveRelationList) {
                        TerminalProtocolActiveRelationResp resp = iTerminalProtocolActivityRelationService.fillActivityInfo(model);
                        terminalProtocolActiveRelationRespList.add(resp);
                    }
                    changeModel.setNewTerminalProtocolActiveRelationList(terminalProtocolActiveRelationRespList);
                }

                //查询终端协议旧信息
                //List<TerminalProductProtocolRelationResp> terminalOldProductProtocolRelationList = terminalProductProtocolRelationService.selectTerminalProductProtocolRelationByIds(oldIds);
                //if (CollectionUtils.isNotEmpty(terminalOldProductProtocolRelationList)) {
                //    changeModel.setOldTerminalProductProtocolRelationList(terminalOldProductProtocolRelationList);
                //}

                // 查询终端旧的活动协议信息
                List<TerminalProtocolActivityRelationModel> terminalOldProtocolActiveRelationList = terminalProtocolActivityRelationService.getTerminalProtocolActivityRelationListByIds(oldIds, null);
                if (CollectionUtils.isNotEmpty(terminalOldProtocolActiveRelationList)) {
                    List<TerminalProtocolActiveRelationResp> terminalProtocolActiveRelationRespList = new ArrayList<>();
                    for(TerminalProtocolActivityRelationModel model : terminalOldProtocolActiveRelationList) {
                        TerminalProtocolActiveRelationResp resp = iTerminalProtocolActivityRelationService.fillActivityInfo(model);
                        terminalProtocolActiveRelationRespList.add(resp);
                    }
                    changeModel.setOldTerminalProtocolActiveRelationList(terminalProtocolActiveRelationRespList);
                }

                // 获取终端信息并塞进去
                TerminalProductProtocolRelationChangeModel terminalProductProtocolRelationChangeModel = terminalProductProtocolRelationChangeDao.selectById(changeId);
                changeModel.setCheckStatus(terminalProductProtocolRelationChangeModel.getCheckStatus());
                changeModel.setCheckStatusName(ProtocolCheckStatusEnum.getValue(terminalProductProtocolRelationChangeModel.getCheckStatus()));
            }
        }

        // TODO新的协议变更记录逻辑
        //if(Objects.nonNull(oldId)) {
        //    List<TerminalProductProtocolRelationResp> oldTerminalProductProtocolRelationRespList = terminalProductProtocolRelationService.selectTerminalProductProtocolRelationByIds(oldId);
        //    if (CollectionUtils.isNotEmpty(oldTerminalProductProtocolRelationRespList)) {
        //        changeModel.setOldTerminalProductProtocolRelationList(oldTerminalProductProtocolRelationRespList);
        //    }
        //}
        // 查询变更类型
        Integer changeType = terminalProtocolChangeDao.getChangeTypeByTerminalShopId(req.getProtocolId());
        changeModel.setChangeType(changeType);
        // 查询经销商编码 主品合同或者酱品
        TerminalShopContractResp primaryContractModel = terminalShopContractDao.selectTerminalShopContractResp(terminalShopResp.getId());
        changeModel.setDealerCode(primaryContractModel.getDealerCode());
        // 查询审核记录
        TerminalShopNodeReq terminalShopNodeReq = new TerminalShopNodeReq();
        terminalShopNodeReq.setTerminalShopId(req.getTerminalShopId());
        terminalShopNodeReq.setProtocolId(req.getProtocolId());
        terminalShopNodeReq.setNodeType(req.getCheckType());
        terminalShopNodeReq.setId(req.getNodeId());
        List<TerminalShopNodeResp> terminalShopNodeRespList = terminalShopNodeDao.selectTerminalShopNodeList(terminalShopNodeReq);
        changeModel.setTerminalShopNodeList(terminalShopNodeRespList);
        if(Objects.nonNull(req.getCheckType())){
            TerminalShopNodeModel terminalShopNodeModel = terminalShopNodeDao.selectById(req.getNodeId());
            //启用禁用审核信息
            if(req.getCheckType().equals(9) || req.getCheckType().equals(10)){
                TerminalShopDisableRecordCommonModel disableRecord = terminalShopDisableRecordCommonDao.selectById(terminalShopNodeModel.getNodeSourceId());
                changeModel.setDisableRecord(disableRecord);
            }
            //合并信息
            if(req.getCheckType().equals(8)){
                TerminalShopMergeModel mergeRecord = terminalShopMergeDao.selectMergeInfoById(terminalShopNodeModel.getNodeSourceId());
                changeModel.setMergeRecord(mergeRecord);
            }
        }
        return changeModel;
    }

    @Override
    public TerminalProtocolChangeResp gtGetTerminalProtocolChangeDetail(TerminalDataLogReq req, HttpServletRequest request) {
        //先走外部接口验证 验证没问题才可以继续走
        String token = request.getHeader("token");
        String timestampStr = request.getHeader("timestamp");
        GuotaiUtil.verifyToken(token, timestampStr);
        return getTerminalProtocolChangeDetail(req);
    }

    @Override
    public List<TerminalProtocolChangeModel> getTerminalProtocolChangeList(Integer terminalShopId, Integer page, Integer limit) {
        PageHelper.startPage(page, limit);
        return terminalProtocolChangeDao.getTerminalProtocolChangeList(terminalShopId);
    }

    private void setMangerInfo(Integer createUser, TerminalShopNodeModel terminalShopNodeModel) {
        QueryWrapper<TerminalAccountManagerModel> queryWrapper = new QueryWrapper<TerminalAccountManagerModel>().eq("id", createUser);
        TerminalAccountManagerModel terminalAccountManagerModel = terminalAccountManagerDao.selectOne(queryWrapper);
        if (terminalAccountManagerModel.getType() == 0) {// 是客户经理
            terminalShopNodeModel.setUpdateUser(terminalAccountManagerModel.getId());
            terminalShopNodeModel.setUpdateName(terminalAccountManagerModel.getName());
            terminalShopNodeModel.setUpdatePhone(terminalAccountManagerModel.getPhone());
        } else {
            TerminalAccountManagerModel accountManagerModel = terminalAccountManagerDao.selectOne(new QueryWrapper<TerminalAccountManagerModel>().eq("id", terminalAccountManagerModel.getParentId()));
            terminalShopNodeModel.setUpdateUser(accountManagerModel.getId());
            terminalShopNodeModel.setUpdateName(accountManagerModel.getName());
            terminalShopNodeModel.setUpdatePhone(accountManagerModel.getPhone());
        }
    }

    /**
     * 将TerminalProtocolChangeReq类型转为TerminalProtocolModel类型
     */
    private TerminalProtocolModel changeReq2Model(TerminalProtocolChangeReq changeReq) {
        TerminalProtocolModel model = new TerminalProtocolModel();
        model.setProtocolType(changeReq.getProtocolType());
        model.setProtocolProperty(changeReq.getProtocolProperty());
        model.setProductType(changeReq.getProductType());
        model.setProtocolImage(changeReq.getProtocolImage());
        model.setTerminalShopId(changeReq.getTerminalShopId());
        model.setProductProtocolConfigId(changeReq.getProductProtocolConfigId().longValue());
        model.setCompanyId(changeReq.getCompanyId());
        model.setDisplaySurface(changeReq.getDisplaySurface());
        model.setMonthScanInNum(changeReq.getMonthScanInNum());
        model.setYearScanInNum(changeReq.getYearScanInNum());
        model.setDisplayAmount(changeReq.getDisplayAmount());
        model.setPackageAmount(changeReq.getPackageAmount());
        model.setCheckStatus(changeReq.getCheckStatus());
        model.setEndStatus(changeReq.getEndStatus());
        model.setDeleteStatus(changeReq.getDeleteStatus());
        if (changeReq.getCreateTime() != null) {
            model.setCreateTime(LocalDateTime.ofInstant(changeReq.getCreateTime().toInstant(), ZoneId.systemDefault()));
        }
        model.setCreateUser(changeReq.getCreateUser());
        model.setLevelCode(changeReq.getLevelCode());
        model.setDealerCode(changeReq.getDealerCode());
        model.setEffectiveTime(changeReq.getEffectiveTime());
        model.setFailureTime(changeReq.getFailureTime());
        return model;
    }

    /**
     * 检查当前终端是否有协议仍在审核中，有则无法审核当前协议
     */
    private void checkTerminalProtocolIsChecking(Integer terminalShopId, Integer protocolId) {
        // List<TerminalProtocolResp> protocolList = terminalProtocolChangeDao.selectTerminalProtocolListByShopId(terminalShopId, 0);
        List<TerminalProtocolChangeReq> protocolList = terminalProtocolChangeDao.getTerminalProtocolListByShopId(terminalShopId, 0);
        if (CollectionUtils.isNotEmpty(protocolList)) {
            for (TerminalProtocolChangeReq protocol : protocolList) {
                // 不能是传进来的协议
                boolean flag = !Objects.equals(protocolId, protocol.getProductProtocolConfigId());
                Integer checkStatus = protocol.getCheckStatus();
                if (flag && checkStatus != 1 && checkStatus != 3 && checkStatus != 5) {
                    throw new BusinessException("400", "当前终端仍有协议在审核中");
                }
            }
        }
    }
}
