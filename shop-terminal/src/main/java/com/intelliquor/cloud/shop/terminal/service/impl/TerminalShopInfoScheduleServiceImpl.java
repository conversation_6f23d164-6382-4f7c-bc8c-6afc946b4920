package com.intelliquor.cloud.shop.terminal.service.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.intelliquor.cloud.shop.common.dao.*;
import com.intelliquor.cloud.shop.common.enums.TerminalShopNodeEnum;
import com.intelliquor.cloud.shop.common.utils.ObjectUtil;
import com.intelliquor.cloud.shop.terminal.util.enums.TerminalShopTypeEnum;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.model.*;
import com.intelliquor.cloud.shop.common.model.req.TerminalShopNodeReq;
import com.intelliquor.cloud.shop.common.model.resp.TerminalShopContractResp;
import com.intelliquor.cloud.shop.common.service.IRequestLogService;
import com.intelliquor.cloud.shop.common.service.ITerminalShopCommonService;
import com.intelliquor.cloud.shop.common.service.TerminalShopContractCommonService;
import com.intelliquor.cloud.shop.common.service.TerminalShopPhysicalCodeRelationService;
import com.intelliquor.cloud.shop.common.utils.DateUtils;
import com.intelliquor.cloud.shop.common.utils.RandomUtils;
import com.intelliquor.cloud.shop.terminal.dao.*;
import com.intelliquor.cloud.shop.terminal.model.*;
import com.intelliquor.cloud.shop.terminal.model.req.AddTerminalShopInfoScheduleReq;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalProtocolReq;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalShopContractReq;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalProductProtocolRelationResp;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalProtocolLogResp;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalShopLogResp;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalShopResp;
import com.intelliquor.cloud.shop.terminal.service.ITerminalProductProtocolRelationService;
import com.intelliquor.cloud.shop.terminal.service.ITerminalShopInfoScheduleService;
import com.intelliquor.cloud.shop.terminal.service.TerminalDataLogService;
import com.intelliquor.cloud.shop.terminal.service.TerminalShopService;
import com.intelliquor.cloud.shop.terminal.util.ZTUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 终端信息附表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
@Slf4j
@Service
public class TerminalShopInfoScheduleServiceImpl extends ServiceImpl<TerminalShopInfoScheduleDao, TerminalShopInfoScheduleModel> implements ITerminalShopInfoScheduleService {


    @Resource
    private TerminalShopDao terminalShopDao;

    @Resource
    private TerminalShopInfoScheduleDao terminalShopInfoScheduleDao;

    @Resource
    private ShopDao shopDao;

    @Resource
    private ShopUserDao shopUserDao;

    @Resource
    private UserContext userContext;

    @Resource
    private TerminalProtocolDao terminalProtocolDao;

    @Resource
    private TerminalShopContractDao terminalShopContractDao;

    @Resource
    private TerminalDataLogDao terminalDataLogDao;

    @Resource
    private TerminalShopNodeDao terminalShopNodeDao;

    @Resource
    private TerminalAccountManagerDao terminalAccountManagerDao;

    @Resource
    private TerminalShopContractCommonService terminalShopContractCommonService;

    @Resource
    private TerminalDataLogService terminalDataLogService;

    @Resource
    private StrangeTerminalShopDao strangeTerminalShopDao;

    @Value("${gt_company_id}")
    private Integer gtCompanyId;

    @Value("${xuanwu.sendYbcjTerminalData.url}")
    private String sendYbcjTerminalDataUrl;

    @Autowired
    private ZTUtils ztUtils;

    @Autowired
    private IRequestLogService requestLogService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private TerminalShopService terminalShopService;

    @Resource
    private ITerminalShopCommonService terminalShopCommonService;

    @Autowired
    private ITerminalProductProtocolRelationService terminalProductProtocolRelationService;

    @Autowired
    private TerminalLicenseRecordDao terminalLicenseRecordDao;

    @Autowired
    private MemberShopDao memberShopDao;

    @Resource
    private TerminalShopPhysicalCodeDao terminalShopPhysicalCodeDao;

    @Resource
    private TerminalShopPhysicalCodeRelationDao terminalShopPhysicalCodeRelationDao;

    @Autowired
    private TerminalShopPhysicalCodeRelationService terminalShopPhysicalCodeRelationService;

    @Resource
    private TerminalShopCommonDao terminalShopCommonDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveTerminalShopNotActive(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq) {
        String TERMINAL_SHOP_INFO_SCHEDULE_REDIS_KEY = "T_TERMINAL_SHOP_INFO_SCHEDULE:" + addTerminalShopInfoScheduleReq.getLatitude() + ":" + addTerminalShopInfoScheduleReq.getLongitude() + ":" + addTerminalShopInfoScheduleReq.getShopName();
        Boolean bBoolean = redisTemplate.opsForValue().setIfAbsent(TERMINAL_SHOP_INFO_SCHEDULE_REDIS_KEY, addTerminalShopInfoScheduleReq.getShopName(), 10, TimeUnit.MINUTES);
        if (!bBoolean) {//存在说明重复激活
            throw new BusinessException("400", "请勿重复操作...");
        }
        log.info("终端采集保存参数：{}", JSON.toJSONString(addTerminalShopInfoScheduleReq));
        try {
            //先判断必填参数是否为空
            verifyBaseInfoParam(addTerminalShopInfoScheduleReq, 1);

            //校验是否已经存在
            verifyExist(addTerminalShopInfoScheduleReq);

            //处理编码参数
            disposeMainCodeAndDeputyCode(addTerminalShopInfoScheduleReq);
            //判断是否为高端会员
            if(Objects.nonNull(addTerminalShopInfoScheduleReq.getPrimaryContract()) && (Objects.nonNull(addTerminalShopInfoScheduleReq.getPrimaryContract().getContractType()) && (addTerminalShopInfoScheduleReq.getPrimaryContract().getContractType().equals(12)))){
                        addTerminalShopInfoScheduleReq.setIsHighMember(1);
            }
            //初始化主副表对象
            TerminalShopInfoScheduleModel scheduleModel = new TerminalShopInfoScheduleModel();
            TerminalShopModel mainModel = new TerminalShopModel();
            //判断新增或者修改
            Integer isInsert = 1;
            if (Objects.isNull(addTerminalShopInfoScheduleReq.getTerminalShopId())) {
                //新增
                BeanUtils.copyProperties(addTerminalShopInfoScheduleReq, scheduleModel);
                //填入公司id
                scheduleModel.setCompanyId(userContext.getTerminalModel().getCompanyId());
                //填入创建人
                scheduleModel.setCreateUser(userContext.getTerminalModel().getId());
                //填入创建时间
                scheduleModel.setCreateTime(new Date());
                scheduleModel.setIsPrepare(1);
                scheduleModel.setStatus(0);
                BeanUtils.copyProperties(scheduleModel, mainModel);
                if(terminalShopService.isHotelTerminalByShopType(addTerminalShopInfoScheduleReq.getShopType())) {
                    if (addTerminalShopInfoScheduleReq.getIsLicenseSuccess() != null) {
                        mainModel.setIsLicenseSuccess(addTerminalShopInfoScheduleReq.getIsLicenseSuccess());
                    } else {
                        throw new BusinessException("400", "需上传营业执照同时营业执照为真");
                    }
                    if (addTerminalShopInfoScheduleReq.getFoodBusinessLicenseCode() != null) {
                        mainModel.setFoodBusinessLicenseCode(addTerminalShopInfoScheduleReq.getFoodBusinessLicenseCode());
                    } else {
                        throw new BusinessException("400", "需上传食品经营许可证");
                    }
                    if (addTerminalShopInfoScheduleReq.getFoodLicenseCode() != null) {
                        mainModel.setFoodLicenseCode(addTerminalShopInfoScheduleReq.getFoodLicenseCode());
                    } else {
                        throw new BusinessException("400", "需上传食品经营许可证");
                    }
                } else {
                    // 写入执照鉴真记录
                    if(!addTerminalShopInfoScheduleReq.getShopType().equals(2)) {
                        if (addTerminalShopInfoScheduleReq.getIsLicenseSuccess() != null) {
                            mainModel.setIsLicenseSuccess(addTerminalShopInfoScheduleReq.getIsLicenseSuccess());
                        } else {
                            throw new BusinessException("400", "需上传营业执照同时营业执照为真");
                        }
                    } else {
                        mainModel.setIsLicenseSuccess(0);
                    }
                }
                terminalShopDao.insert(mainModel);
                scheduleModel.setTerminalShopId(mainModel.getId());
                terminalShopInfoScheduleDao.insert(scheduleModel);

            } else {
                //修改
                isInsert = 2;
                isInsert = 2;
                TerminalShopInfoScheduleModel oldScheduleModel = terminalShopInfoScheduleDao.selectOne(new QueryWrapper<TerminalShopInfoScheduleModel>()
                        .eq("terminal_shop_id", addTerminalShopInfoScheduleReq.getTerminalShopId())
                        .eq("is_delete", 0)
                        .last("limit 1"));
                TerminalShopModel oldMainModel = terminalShopDao.selectOne(new QueryWrapper<TerminalShopModel>()
                        .eq("id", addTerminalShopInfoScheduleReq.getTerminalShopId())
                        .eq("is_delete", 0)
                        .last("limit 1"));
                scheduleModel = getScheduleModel(oldScheduleModel, addTerminalShopInfoScheduleReq);
                mainModel = getMainModel(oldMainModel, addTerminalShopInfoScheduleReq);
                scheduleModel.setUpdateTime(new Date());
                mainModel.setUpdateTime(new Date());
                mainModel.setStatus(0);
                // 写入执照鉴真记录
                if(Optional.ofNullable(addTerminalShopInfoScheduleReq.getShopType())
                        .map(shopType -> terminalShopService.isHotelTerminalByShopType(shopType))
                        .orElse(false) ||
                            Optional.ofNullable(addTerminalShopInfoScheduleReq.getTerminalShopId())
                        .map(terminalShopCommonService::isHotelTerminalByTerminalShopId)
                        .orElse(false)) {
                    if (addTerminalShopInfoScheduleReq.getIsLicenseSuccess() != null) {
                        mainModel.setIsLicenseSuccess(addTerminalShopInfoScheduleReq.getIsLicenseSuccess());
                    } else {
                        throw new BusinessException("400", "需上传营业执照同时营业执照为真");
                    }
                    if (addTerminalShopInfoScheduleReq.getFoodBusinessLicenseCode() != null) {
                        mainModel.setFoodBusinessLicenseCode(addTerminalShopInfoScheduleReq.getFoodBusinessLicenseCode());
                    } else {
                        throw new BusinessException("400", "需上传食品经营许可证");
                    }
                    if (addTerminalShopInfoScheduleReq.getFoodLicenseCode() != null) {
                        mainModel.setFoodLicenseCode(addTerminalShopInfoScheduleReq.getFoodLicenseCode());
                    } else {
                        throw new BusinessException("400", "需上传食品经营许可证");
                    }
                } else {
                    if(!addTerminalShopInfoScheduleReq.getShopType().equals(2)) {
                        if (addTerminalShopInfoScheduleReq.getIsLicenseSuccess() != null) {
                            mainModel.setIsLicenseSuccess(addTerminalShopInfoScheduleReq.getIsLicenseSuccess());
                        } else {
                            throw new BusinessException("400", "需上传营业执照同时营业执照为真");
                        }
                    } else {
                        mainModel.setIsLicenseSuccess(0);
                    }
                }
                terminalShopInfoScheduleDao.updateById(scheduleModel);
                terminalShopDao.updateById(mainModel);
                if (CollectionUtils.isNotEmpty(addTerminalShopInfoScheduleReq.getProtocolList())) {
                    //删除协议
                    terminalProtocolDao.deleteByShopId(mainModel.getId());
                }
                if (Objects.nonNull(addTerminalShopInfoScheduleReq.getPrimaryContract())) {
                    //删除合同
                    terminalShopContractDao.deleteByShopId(mainModel.getId());
                }
            }

            // 更新陌生终端表关联id
            if (Objects.nonNull(addTerminalShopInfoScheduleReq.getStrangeTerminalId())) {
                StrangeTerminalShopModel strangeTerminalShopModel = new StrangeTerminalShopModel();
                strangeTerminalShopModel.setId(addTerminalShopInfoScheduleReq.getStrangeTerminalId());
                strangeTerminalShopModel.setLinkId(mainModel.getId());
                strangeTerminalShopDao.updateById(strangeTerminalShopModel);
            }

            //添加协议
            List<TerminalProtocolModel> protocolModelList = insertProtocol(addTerminalShopInfoScheduleReq.getProtocolList(), mainModel, addTerminalShopInfoScheduleReq.getPrimaryContract());
            //添加合同
            insertContract(addTerminalShopInfoScheduleReq.getPrimaryContract(), mainModel.getId(), mainModel.getMemberShopId());

            //推送中台 保存终端不推送中台
//            sendZtYcjData(scheduleModel, isInsert);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("400", e.getMessage());
        } finally {
            redisTemplate.delete(TERMINAL_SHOP_INFO_SCHEDULE_REDIS_KEY);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void activateTerminal(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq) {
        StopWatch watch = new StopWatch();
        watch.start();
        RequestLog requestLog = new RequestLog();
        requestLog.setReqType(30);
        requestLog.setReqUrlPath("TerminalShopInfoScheduleServiceImpl/activateTerminal");
        requestLog.setReqKey(addTerminalShopInfoScheduleReq.getShopName());
        requestLog.setReqJson(JSONObject.toJSONString(addTerminalShopInfoScheduleReq));
        requestLog.setResCode("0");
        requestLog.setCreateDate(new Date());
        String reqName = "【提交终端信息】" + addTerminalShopInfoScheduleReq.getShopName();
        String TERMINAL_SHOP_INFO_SCHEDULE_REDIS_KEY = "";
        String leaderPhone = addTerminalShopInfoScheduleReq.getLeaderPhone();
        String mainCode = addTerminalShopInfoScheduleReq.getMainCode();
        if (StringUtils.isEmpty(mainCode)) {
            TERMINAL_SHOP_INFO_SCHEDULE_REDIS_KEY = "T_TERMINAL_SHOP_INFO_SCHEDULE:" + leaderPhone + ":" + addTerminalShopInfoScheduleReq.getShopName();
        } else {
            TERMINAL_SHOP_INFO_SCHEDULE_REDIS_KEY = "T_TERMINAL_SHOP_INFO_SCHEDULE:" + mainCode + ":" + addTerminalShopInfoScheduleReq.getDeputyCode();
        }
        Boolean bBoolean = redisTemplate.opsForValue().setIfAbsent(TERMINAL_SHOP_INFO_SCHEDULE_REDIS_KEY, addTerminalShopInfoScheduleReq.getShopName(), 10, TimeUnit.MINUTES);
        if (!bBoolean) {//存在说明重复激活
            throw new BusinessException("400", "请勿重复操作...");
        }
        Set<String> redisKeyList = new HashSet<>();
        redisKeyList.add(TERMINAL_SHOP_INFO_SCHEDULE_REDIS_KEY);
        log.info("终端采集激活参数：{}", JSON.toJSONString(addTerminalShopInfoScheduleReq));
        try {
            // 是否是客户经理
            Boolean isManage = false;
            if (userContext.getTerminalModel().getType() == 0) {//客户经理  不需要确认
                isManage = true;
            }
            //先判断必填参数是否为空
            verifyBaseInfoParam(addTerminalShopInfoScheduleReq, 0);
            verifyActivateParam(addTerminalShopInfoScheduleReq);

            //校验是否已经存在
            verifyExist(addTerminalShopInfoScheduleReq);

            //填入公司id
            addTerminalShopInfoScheduleReq.setCompanyId(gtCompanyId);

            //处理编码参数
            disposeMainCodeAndDeputyCode(addTerminalShopInfoScheduleReq);
            //判断是否为高端会员
            if(Objects.nonNull(addTerminalShopInfoScheduleReq.getPrimaryContract()) && (Objects.nonNull(addTerminalShopInfoScheduleReq.getPrimaryContract().getContractType()) && (addTerminalShopInfoScheduleReq.getPrimaryContract().getContractType().equals(12)))){
                addTerminalShopInfoScheduleReq.setIsHighMember(1);
            }
            //初始化主副表对象
            TerminalShopInfoScheduleModel scheduleModel = new TerminalShopInfoScheduleModel();
            TerminalShopModel mainModel = new TerminalShopModel();
            List<TerminalProtocolReq> protocolList = addTerminalShopInfoScheduleReq.getProtocolList();
            Boolean isInsert = true;
            //判断新增或者修改
            if (Objects.isNull(addTerminalShopInfoScheduleReq.getTerminalShopId())) {
                //新增
                BeanUtils.copyProperties(addTerminalShopInfoScheduleReq, scheduleModel);
                //附表状态为审批状态
                scheduleModel.setStatus(2);
                //填入公司id
                scheduleModel.setCompanyId(userContext.getTerminalModel().getCompanyId());
                //填入创建人
                scheduleModel.setCreateUser(userContext.getTerminalModel().getId());
                //填入创建时间
                scheduleModel.setCreateTime(new Date());
                BeanUtils.copyProperties(scheduleModel, mainModel);
                //主表只有激活和未激活状态
                mainModel.setStatus(0);
                mainModel.setIsPrepare(1);
                scheduleModel.setIsPrepare(1);
                if (isManage) {
                    mainModel.setStatus(1);
                    mainModel.setIsPrepare(0);
                    scheduleModel.setStatus(1);
                    scheduleModel.setIsPrepare(0);
                }

                terminalShopDao.insert(mainModel);
                scheduleModel.setTerminalShopId(mainModel.getId());
                log.info("1111----->{}", scheduleModel.getStatus());
                terminalShopInfoScheduleDao.insert(scheduleModel);
            } else {
                isInsert = false;
                //修改
                TerminalShopInfoScheduleModel oldScheduleModel = terminalShopInfoScheduleDao.selectOne(new QueryWrapper<TerminalShopInfoScheduleModel>()
                        .eq("terminal_shop_id", addTerminalShopInfoScheduleReq.getTerminalShopId())
                        .eq("is_delete", 0)
                        .last("limit 1"));
                TerminalShopModel oldMainModel = terminalShopDao.selectOne(new QueryWrapper<TerminalShopModel>()
                        .eq("id", addTerminalShopInfoScheduleReq.getTerminalShopId())
                        .eq("is_delete", 0)
                        .last("limit 1"));
                scheduleModel = getScheduleModel(oldScheduleModel, addTerminalShopInfoScheduleReq);
                mainModel = getMainModel(oldMainModel, addTerminalShopInfoScheduleReq);
                scheduleModel.setStatus(2);
                mainModel.setStatus(0);
                scheduleModel.setUpdateTime(new Date());
                mainModel.setUpdateTime(new Date());

                if (isManage) {
                    mainModel.setStatus(1);
                    mainModel.setIsPrepare(0);
                    scheduleModel.setStatus(1);
                    scheduleModel.setIsPrepare(0);
                }

                log.info("1112----->{}", scheduleModel.getStatus());
                terminalShopInfoScheduleDao.updateById(scheduleModel);
                terminalShopDao.updateById(mainModel);
                if (CollectionUtils.isNotEmpty(protocolList)) {
                    //删除协议
                    terminalProtocolDao.deleteByShopId(mainModel.getId());
                }
                if (Objects.nonNull(addTerminalShopInfoScheduleReq.getPrimaryContract())) {
                    //删除合同
                    terminalShopContractDao.deleteByShopId(mainModel.getId());
                }
            }
            // 更新陌生终端表关联id
            if (Objects.nonNull(addTerminalShopInfoScheduleReq.getStrangeTerminalId())) {
                StrangeTerminalShopModel strangeTerminalShopModel = new StrangeTerminalShopModel();
                strangeTerminalShopModel.setId(addTerminalShopInfoScheduleReq.getStrangeTerminalId());
                strangeTerminalShopModel.setLinkId(mainModel.getId());
                strangeTerminalShopDao.updateById(strangeTerminalShopModel);
            }
            //添加协议
            List<TerminalProtocolModel> protocolModelList = insertProtocol(addTerminalShopInfoScheduleReq.getProtocolList(), mainModel, addTerminalShopInfoScheduleReq.getPrimaryContract());
            //添加合同
            insertContract(addTerminalShopInfoScheduleReq.getPrimaryContract(), mainModel.getId(), mainModel.getMemberShopId());

            //添加日志记录
            Integer dataLogId = insertDataLog(addTerminalShopInfoScheduleReq, mainModel, scheduleModel, protocolList, isInsert);
            log.info("1113----->{}", scheduleModel.getStatus());
            //添加审批的节点数据
            TerminalShopNodeModel terminalShopNodeModel = TerminalShopNodeModel.builder()
                    .terminalShopId(mainModel.getId())
                    .companyId(mainModel.getCompanyId())
                    .nodeName("终端激活申请")
                    .nodeLevel("0")
                    .nodeType(1)
                    .isBack("0")
                    .nodeStatus("0")
                    .createTime(new Date())
                    .dataLogId(dataLogId)
                    .scheduleShopId(scheduleModel.getId())
                    .build();
            //设置客户经理信息
            setMangerInfo(mainModel.getCreateUser(), terminalShopNodeModel);
            String currentYm = DateUtils.convert2String(new Date(), "yyyy-MM");
            terminalShopNodeModel.setMonthYear(currentYm);
            if (isManage) {
                terminalShopNodeModel.setNodeStatus("1");
                terminalShopNodeModel.setUpdateMsg("自动审核通过");
                terminalShopNodeModel.setApprovalUser(userContext.getTerminalModel().getId());
                terminalShopNodeModel.setUpdateDate(new Date());
            }
            terminalShopNodeDao.insert(terminalShopNodeModel);
            TerminalShopNodeReq terminalShopNodeReq = new TerminalShopNodeReq();
            BeanUtils.copyProperties(terminalShopNodeModel, terminalShopNodeReq);
            terminalShopService.handleYbTerminalJh(terminalShopNodeReq, protocolModelList, redisKeyList, mainModel.getStatus());

            /*if(userContext.getTerminalModel().getType() == 0){ //客户经理  不需要确认
                terminalShopNodeReq.setNodeStatus("1");
                terminalShopNodeReq.setIsBack("0");
                terminalShopNodeReq.setUpdateMsg("自动通过");
                log.info("客户经理异步通过{}", JSONObject.toJSONString(terminalShopNodeReq));
                terminalShopService.approvalTerminalShop(terminalShopNodeReq);
            }*/
            reqName = reqName.concat("【成功】");
            requestLog.setResMsg("提交终端信息成功");
        } catch (Exception e) {
            e.printStackTrace();
            requestLog.setResCode("-1");
            requestLog.setResMsg("提交终端信息失败:" + e.getMessage());
            throw new BusinessException("400", e.getMessage());
        } finally {
            watch.stop();
            redisTemplate.delete(redisKeyList);
            String diffTime = DateUtils.longToHMS(watch.getTotalTimeMillis());
            requestLog.setReqTime(diffTime);
            requestLog.setReqName(reqName);
            requestLogService.insertLog(requestLog);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void activateTerminalNewProtocol(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq) {
        StopWatch watch = new StopWatch();
        watch.start();
        RequestLog requestLog = new RequestLog();
        requestLog.setReqType(30);
        requestLog.setReqUrlPath("TerminalShopInfoScheduleServiceImpl/activateTerminalNew");
        requestLog.setReqKey(addTerminalShopInfoScheduleReq.getShopName());
        requestLog.setReqJson(JSONObject.toJSONString(addTerminalShopInfoScheduleReq));
        requestLog.setResCode("0");
        requestLog.setCreateDate(new Date());
        String reqName = "【提交终端信息】" + addTerminalShopInfoScheduleReq.getShopName();
        String TERMINAL_SHOP_INFO_SCHEDULE_REDIS_KEY = "";
        String leaderPhone = addTerminalShopInfoScheduleReq.getLeaderPhone();
        String mainCode = addTerminalShopInfoScheduleReq.getMainCode();
        if (StringUtils.isEmpty(mainCode)) {
            TERMINAL_SHOP_INFO_SCHEDULE_REDIS_KEY = "T_TERMINAL_SHOP_INFO_SCHEDULE:" + leaderPhone + ":" + addTerminalShopInfoScheduleReq.getShopName();
        } else {
            TERMINAL_SHOP_INFO_SCHEDULE_REDIS_KEY = "T_TERMINAL_SHOP_INFO_SCHEDULE:" + mainCode + ":" + addTerminalShopInfoScheduleReq.getDeputyCode();
        }
        Boolean bBoolean = redisTemplate.opsForValue().setIfAbsent(TERMINAL_SHOP_INFO_SCHEDULE_REDIS_KEY, addTerminalShopInfoScheduleReq.getShopName(), 10, TimeUnit.MINUTES);
        if (!bBoolean) {//存在说明重复激活
            throw new BusinessException("400", "请勿重复操作...");
        }
        Set<String> redisKeyList = new HashSet<>();
        redisKeyList.add(TERMINAL_SHOP_INFO_SCHEDULE_REDIS_KEY);
        log.info("终端采集激活参数：{}", JSON.toJSONString(addTerminalShopInfoScheduleReq));
        try {
            // 是否是客户经理
            Boolean isManage = false;
            if (userContext.getTerminalModel().getType() == 0) {//客户经理  不需要确认
                isManage = true;
            }
            //先判断必填参数是否为空
            verifyBaseInfoParam(addTerminalShopInfoScheduleReq, 0);
            verifyActivateNewProtocolParam(addTerminalShopInfoScheduleReq);
            //校验是否已经存在
            verifyExist(addTerminalShopInfoScheduleReq);
            if (Optional.ofNullable(addTerminalShopInfoScheduleReq.getShopType())
                    .map(shopType -> terminalShopService.isHotelTerminalByShopType(shopType))
                    .orElse(false) ||
                    Optional.ofNullable(addTerminalShopInfoScheduleReq.getTerminalShopId())
                            .map(terminalShopCommonService::isHotelTerminalByTerminalShopId)
                            .orElse(false)) {
                if (!addTerminalShopInfoScheduleReq.getLicenseCode().equals(addTerminalShopInfoScheduleReq.getFoodLicenseCode())) {
                    throw new BusinessException("400", "食品经营许可证的社会统一信息代码需与营业执照社会统一信息代码一致");
                }
                verifyLicense(addTerminalShopInfoScheduleReq);
                // 五合一验证
                // 该营业执照下是否存在连锁型餐饮酒店，特色型餐饮酒店，商务型餐饮酒店，宴席型餐饮酒店类型已激活/审核中、合并状态为未合并/主店的终端
                // 如存在，是否与当前采集的终端一致；如不一致提示【该营业执照已存在“终端类型”】（多个终端类型用“，”隔开），如一致验证通过进入下一个验证；
                // 如不存在，验证通过进入下一个验证；
                verifyLicenseCodeAndShopType(addTerminalShopInfoScheduleReq);
                // 先查询是否有相同的营业执照和终端类型的终端
                verifyExistTerminalShopByLisence(addTerminalShopInfoScheduleReq, "Hotel");
                // 餐饮渠道下，营业执照编号一致，合同类型一致，弹出提示信息，该营业执照下已有激活启用中】终端绑定该类型合同
//                verifyLisenceAndContractTypeByHotel(addTerminalShopInfoScheduleReq);
                // 2025年新政策，不再验证合同类型
                // 餐饮渠道下，营业执照编号一致，经销商编号一致，弹出提示信息，该营业执照下已有激活启用终端绑定该经销商
                // SZYXPT-1758 相同营业执照号可以绑定相同的经销商不能绑定相同合同编号 故注释经销商类型验证
//                verifyLisenceAndDealerCodeByHotel(addTerminalShopInfoScheduleReq);
                // 餐饮渠道下，营业执照编号一致，合同编号一致，弹出提示信息，该营业执照下已有激活启用终端绑定该合同编号
                verifyLisenceAndContractCode(addTerminalShopInfoScheduleReq, "Hotel");
            } else {
                //校验营业执照是否存在且为真，如果不是真，那么抛异常，提示【需上传营业执照同时营业执照为真】 团购终端不用验证
                if (!addTerminalShopInfoScheduleReq.getShopType().equals(TerminalShopTypeEnum.together_shopping_terminal.getType())) {
                    verifyLicense(addTerminalShopInfoScheduleReq);
                }

                //校验营业执照与合同类型是否已有已激活的终端，如果有，那么抛异常，提示【该营业执照已存在该合同类型】 连锁终端、渠道连锁终端，团购终端不验证营业执照与相同合同类型
                if (!addTerminalShopInfoScheduleReq.getShopType().equals(TerminalShopTypeEnum.together_shopping_terminal.getType())) {
                    // 同一张营业执照已有酒店餐饮终端的情况下，不允许激活非酒店餐饮终端
                    verifyLisenceByHotelTerminal(addTerminalShopInfoScheduleReq);
                    // 先查询是否有相同的营业执照和终端类型的终端
//                    该营业执照下是否存在烟酒终端，企业终端，连锁终端，渠道连锁终端类型已激活/审核中、合并状态为未合并/主店的终端
//                    如存在，是否与当前采集的终端类型一致；如不一致提示【该营业执照已存在“终端类型”】（多个终端类型用“，”隔开），如一致验证通过进入下一个验证；
//                    如不存在，验证通过进入下一个验证；
                    verifyExistTerminalShopByLisence(addTerminalShopInfoScheduleReq, "notHotel");
                    // 查询是否有相同的营业执照和合同类型的终端
//                    verifyLisenceAndContractType(addTerminalShopInfoScheduleReq);
                    // 2025年新政策，不再验证合同类型
                    // SZYXPT-1758 相同营业执照号可以绑定相同的经销商不能绑定相同合同编号 故注释经销商类型验证，加入合同编号验证
//                    verifyLisenceAndDealerCode(addTerminalShopInfoScheduleReq);
                    verifyLisenceAndContractCode(addTerminalShopInfoScheduleReq, "notHotel");
                }

            }
            //填入公司id
            addTerminalShopInfoScheduleReq.setCompanyId(gtCompanyId);

            //处理编码参数
            disposeMainCodeAndDeputyCode(addTerminalShopInfoScheduleReq);
            //判断是否为高端会员
            if(Objects.nonNull(addTerminalShopInfoScheduleReq.getPrimaryContract()) && (Objects.nonNull(addTerminalShopInfoScheduleReq.getPrimaryContract().getContractType()) && (addTerminalShopInfoScheduleReq.getPrimaryContract().getContractType().equals(12)))){
                addTerminalShopInfoScheduleReq.setIsHighMember(1);
            }
            //初始化主副表对象
            TerminalShopInfoScheduleModel scheduleModel = new TerminalShopInfoScheduleModel();
            TerminalShopModel mainModel = new TerminalShopModel();
            //List<TerminalProtocolReq> protocolList = addTerminalShopInfoScheduleReq.getProtocolList();
            Boolean isInsert = true;
            //判断新增或者修改
            if (Objects.isNull(addTerminalShopInfoScheduleReq.getTerminalShopId())) {
                //新增
                BeanUtils.copyProperties(addTerminalShopInfoScheduleReq, scheduleModel);
                //附表状态为审批状态
                scheduleModel.setStatus(2);
                //填入公司id
                scheduleModel.setCompanyId(userContext.getTerminalModel().getCompanyId());
                //填入创建人
                scheduleModel.setCreateUser(userContext.getTerminalModel().getId());
                //填入创建时间
                scheduleModel.setCreateTime(new Date());
                BeanUtils.copyProperties(scheduleModel, mainModel);
                //主表只有激活和未激活状态
                mainModel.setStatus(0);
                mainModel.setIsPrepare(1);
                scheduleModel.setIsPrepare(1);
                if (isManage) {
                    mainModel.setStatus(1);
                    mainModel.setIsPrepare(0);
                    scheduleModel.setStatus(1);
                    scheduleModel.setIsPrepare(0);
                }
                if (Optional.ofNullable(addTerminalShopInfoScheduleReq.getShopType())
                        .map(shopType -> terminalShopService.isHotelTerminalByShopType(shopType))
                        .orElse(false) ||
                        Optional.ofNullable(addTerminalShopInfoScheduleReq.getTerminalShopId())
                                .map(terminalShopCommonService::isHotelTerminalByTerminalShopId)
                                .orElse(false)) {
                    if (addTerminalShopInfoScheduleReq.getIsLicenseSuccess() != null) {
                        mainModel.setIsLicenseSuccess(addTerminalShopInfoScheduleReq.getIsLicenseSuccess());
                    } else {
                        throw new BusinessException("400", "需上传营业执照同时营业执照为真");
                    }
                    if (addTerminalShopInfoScheduleReq.getFoodBusinessLicenseCode() != null) {
                        mainModel.setFoodBusinessLicenseCode(addTerminalShopInfoScheduleReq.getFoodBusinessLicenseCode());
                    } else {
                        throw new BusinessException("400", "需上传食品经营许可证");
                    }
                    if (addTerminalShopInfoScheduleReq.getFoodLicenseCode() != null) {
                        mainModel.setFoodLicenseCode(addTerminalShopInfoScheduleReq.getFoodLicenseCode());
                    } else {
                        throw new BusinessException("400", "需上传食品经营许可证");
                    }
                } else {
                    // 写入执照鉴真记录
                    if(!addTerminalShopInfoScheduleReq.getShopType().equals(2)) {
                        if (addTerminalShopInfoScheduleReq.getIsLicenseSuccess() != null) {
                            mainModel.setIsLicenseSuccess(addTerminalShopInfoScheduleReq.getIsLicenseSuccess());
                        } else {
                            throw new BusinessException("400", "需上传营业执照同时营业执照为真");
                        }
                    } else {
                        mainModel.setIsLicenseSuccess(0);
                    }
                }
                // 酒店终端的终端地址审批直接通过
                if(isManage) {
                    if (Optional.ofNullable(addTerminalShopInfoScheduleReq.getShopType())
                            .map(shopType -> terminalShopService.isHotelTerminalByShopType(shopType))
                            .orElse(false) ||
                            Optional.ofNullable(addTerminalShopInfoScheduleReq.getTerminalShopId())
                                    .map(terminalShopCommonService::isHotelTerminalByTerminalShopId)
                                    .orElse(false)) {
                        mainModel.setAddressStatus(1);
                    }
                }
                //SZYXPT-1181 常规渠道终端验证为同一物理终端 【审核通过】验证 客户经理直接审核通过
                if(isManage) {
                    // 同一物理终端编码操作
                    setSamePhysicalCode(mainModel, 0);
                }

                terminalShopDao.insert(mainModel);
                scheduleModel.setTerminalShopId(mainModel.getId());
                log.info("1111----->{}", scheduleModel.getStatus());
                terminalShopInfoScheduleDao.insert(scheduleModel);
            } else {
                isInsert = false;
                //修改
                TerminalShopInfoScheduleModel oldScheduleModel = terminalShopInfoScheduleDao.selectOne(new QueryWrapper<TerminalShopInfoScheduleModel>()
                        .eq("terminal_shop_id", addTerminalShopInfoScheduleReq.getTerminalShopId())
                        .eq("is_delete", 0)
                        .last("limit 1"));
                TerminalShopModel oldMainModel = terminalShopDao.selectOne(new QueryWrapper<TerminalShopModel>()
                        .eq("id", addTerminalShopInfoScheduleReq.getTerminalShopId())
                        .eq("is_delete", 0)
                        .last("limit 1"));
                scheduleModel = getScheduleModel(oldScheduleModel, addTerminalShopInfoScheduleReq);
                mainModel = getMainModel(oldMainModel, addTerminalShopInfoScheduleReq);
                scheduleModel.setStatus(2);
                mainModel.setStatus(0);
                scheduleModel.setUpdateTime(new Date());
                mainModel.setUpdateTime(new Date());

                if (isManage) {
                    mainModel.setStatus(1);
                    mainModel.setIsPrepare(0);
                    scheduleModel.setStatus(1);
                    scheduleModel.setIsPrepare(0);
                }
                if (Optional.ofNullable(addTerminalShopInfoScheduleReq.getShopType())
                        .map(shopType -> terminalShopService.isHotelTerminalByShopType(shopType))
                        .orElse(false) ||
                        Optional.ofNullable(addTerminalShopInfoScheduleReq.getTerminalShopId())
                                .map(terminalShopCommonService::isHotelTerminalByTerminalShopId)
                                .orElse(false)) {
                    if (addTerminalShopInfoScheduleReq.getIsLicenseSuccess() != null) {
                        mainModel.setIsLicenseSuccess(addTerminalShopInfoScheduleReq.getIsLicenseSuccess());
                    } else {
                        throw new BusinessException("400", "需上传营业执照同时营业执照为真");
                    }
                    if (addTerminalShopInfoScheduleReq.getFoodBusinessLicenseCode() != null) {
                        mainModel.setFoodBusinessLicenseCode(addTerminalShopInfoScheduleReq.getFoodBusinessLicenseCode());
                    } else {
                        throw new BusinessException("400", "需上传食品经营许可证");
                    }
                    if (addTerminalShopInfoScheduleReq.getFoodLicenseCode() != null) {
                        mainModel.setFoodLicenseCode(addTerminalShopInfoScheduleReq.getFoodLicenseCode());
                    } else {
                        throw new BusinessException("400", "需上传食品经营许可证");
                    }
                } else {
                    // 写入执照鉴真记录
                    if(!addTerminalShopInfoScheduleReq.getShopType().equals(2)) {
                        if (addTerminalShopInfoScheduleReq.getIsLicenseSuccess() != null) {
                            mainModel.setIsLicenseSuccess(addTerminalShopInfoScheduleReq.getIsLicenseSuccess());
                        } else {
                            throw new BusinessException("400", "需上传营业执照同时营业执照为真");
                        }
                    } else {
                        mainModel.setIsLicenseSuccess(0);
                    }
                }
                // 酒店终端的终端地址审批直接通过
                if(isManage) {
                    if (Optional.ofNullable(addTerminalShopInfoScheduleReq.getShopType())
                            .map(shopType -> terminalShopService.isHotelTerminalByShopType(shopType))
                            .orElse(false) ||
                            Optional.ofNullable(addTerminalShopInfoScheduleReq.getTerminalShopId())
                                    .map(terminalShopCommonService::isHotelTerminalByTerminalShopId)
                                    .orElse(false)) {
                        mainModel.setAddressStatus(1);
                    }
                }
                //SZYXPT-1181 常规渠道终端验证为同一物理终端 【审核通过】验证 客户经理直接审核通过
                if(isManage) {
                    // 同一物理终端编码操作
                    setSamePhysicalCode(mainModel, 0);
                }
                log.info("1112----->{}", scheduleModel.getStatus());
                terminalShopInfoScheduleDao.updateById(scheduleModel);
                terminalShopDao.updateById(mainModel);
                // 2024年新政策，采集终端时不需要绑定协议,修改时也不修改协议信息
                //if (CollectionUtils.isNotEmpty(protocolList)) {
                //    //删除协议
                //    terminalProtocolDao.deleteByShopId(mainModel.getId());
                //}
                if (Objects.nonNull(addTerminalShopInfoScheduleReq.getPrimaryContract())) {
                    //删除合同
                    terminalShopContractDao.deleteByShopId(mainModel.getId());
                }
            }
            // 更新陌生终端表关联id
            if (Objects.nonNull(addTerminalShopInfoScheduleReq.getStrangeTerminalId())) {
                StrangeTerminalShopModel strangeTerminalShopModel = new StrangeTerminalShopModel();
                strangeTerminalShopModel.setId(addTerminalShopInfoScheduleReq.getStrangeTerminalId());
                strangeTerminalShopModel.setLinkId(mainModel.getId());
                strangeTerminalShopDao.updateById(strangeTerminalShopModel);
            }
            //添加协议
            //List<TerminalProtocolModel> protocolModelList = insertProtocol(addTerminalShopInfoScheduleReq.getProtocolList(), mainModel, addTerminalShopInfoScheduleReq.getPrimaryContract());
            //添加合同
            insertContract(addTerminalShopInfoScheduleReq.getPrimaryContract(), mainModel.getId(), mainModel.getMemberShopId());

            //添加日志记录
            Integer dataLogId = newInsertDataLog(addTerminalShopInfoScheduleReq, mainModel, scheduleModel, null, isInsert);
            log.info("1113----->{}", scheduleModel.getStatus());
            //添加审批的节点数据
            TerminalShopNodeModel terminalShopNodeModel = TerminalShopNodeModel.builder()
                    .terminalShopId(mainModel.getId())
                    .companyId(mainModel.getCompanyId())
                    .nodeName(TerminalShopNodeEnum.TERMINAL_COLLECT.getName())
                    .nodeLevel("0")
                    .nodeType(TerminalShopNodeEnum.TERMINAL_COLLECT.getType())
                    .isBack("0")
                    .nodeStatus("0")
                    .createTime(new Date())
                    .dataLogId(dataLogId)
                    .scheduleShopId(scheduleModel.getId())
                    .build();
            //设置客户经理信息
            setMangerInfo(mainModel.getCreateUser(), terminalShopNodeModel);
            String currentYm = DateUtils.convert2String(new Date(), "yyyy-MM");
            terminalShopNodeModel.setMonthYear(currentYm);
            if (isManage) {
                terminalShopNodeModel.setNodeStatus("1");
                terminalShopNodeModel.setUpdateMsg("自动审核通过");
                terminalShopNodeModel.setApprovalUser(userContext.getTerminalModel().getId());
                terminalShopNodeModel.setUpdateDate(new Date());
            }
            terminalShopNodeDao.insert(terminalShopNodeModel);
            TerminalShopNodeReq terminalShopNodeReq = new TerminalShopNodeReq();
            BeanUtils.copyProperties(terminalShopNodeModel, terminalShopNodeReq);
            // 经销商业代，经销商人员点击提交审核不同步中台
            terminalShopService.handleYbTerminalJhNewProtocol(terminalShopNodeReq, redisKeyList, scheduleModel.getStatus(), isManage);

            /*if(userContext.getTerminalModel().getType() == 0){ //客户经理  不需要确认
                terminalShopNodeReq.setNodeStatus("1");
                terminalShopNodeReq.setIsBack("0");
                terminalShopNodeReq.setUpdateMsg("自动通过");
                log.info("客户经理异步通过{}", JSONObject.toJSONString(terminalShopNodeReq));
                terminalShopService.approvalTerminalShop(terminalShopNodeReq);
            }*/
            reqName = reqName.concat("【成功】");
            requestLog.setResMsg("提交终端信息成功");
        } catch (Exception e) {
            e.printStackTrace();
            requestLog.setResCode("-1");
            requestLog.setResMsg("提交终端信息失败:" + e.getMessage());
            throw new BusinessException("400", e.getMessage());
        } finally {
            watch.stop();
            redisTemplate.delete(redisKeyList);
            String diffTime = DateUtils.longToHMS(watch.getTotalTimeMillis());
            requestLog.setReqTime(diffTime);
            requestLog.setReqName(reqName);
            requestLogService.insertLog(requestLog);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void activateTerminalNewProtocolByHotel(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq) {
        StopWatch watch = new StopWatch();
        watch.start();
        RequestLog requestLog = new RequestLog();
        requestLog.setReqType(30);
        requestLog.setReqUrlPath("TerminalShopInfoScheduleServiceImpl/activateTerminalNewProtocolByHotel");
        requestLog.setReqKey(addTerminalShopInfoScheduleReq.getShopName());
        requestLog.setReqJson(JSONObject.toJSONString(addTerminalShopInfoScheduleReq));
        requestLog.setResCode("0");
        requestLog.setCreateDate(new Date());
        String reqName = "【提交酒店餐饮终端信息】" + addTerminalShopInfoScheduleReq.getShopName();
        String TERMINAL_SHOP_INFO_SCHEDULE_REDIS_KEY = "";
        String leaderPhone = addTerminalShopInfoScheduleReq.getLeaderPhone();
        String mainCode = addTerminalShopInfoScheduleReq.getMainCode();
        if (StringUtils.isEmpty(mainCode)) {
            TERMINAL_SHOP_INFO_SCHEDULE_REDIS_KEY = "T_TERMINAL_SHOP_INFO_SCHEDULE:" + leaderPhone + ":" + addTerminalShopInfoScheduleReq.getShopName();
        } else {
            TERMINAL_SHOP_INFO_SCHEDULE_REDIS_KEY = "T_TERMINAL_SHOP_INFO_SCHEDULE:" + mainCode + ":" + addTerminalShopInfoScheduleReq.getDeputyCode();
        }
        Boolean bBoolean = redisTemplate.opsForValue().setIfAbsent(TERMINAL_SHOP_INFO_SCHEDULE_REDIS_KEY, addTerminalShopInfoScheduleReq.getShopName(), 10, TimeUnit.MINUTES);
        if (!bBoolean) {//存在说明重复激活
            throw new BusinessException("400", "请勿重复操作...");
        }
        Set<String> redisKeyList = new HashSet<>();
        redisKeyList.add(TERMINAL_SHOP_INFO_SCHEDULE_REDIS_KEY);
        log.info("酒店终端采集激活参数：{}", JSON.toJSONString(addTerminalShopInfoScheduleReq));
        try {
            // 是否是客户经理
            Boolean isManage = false;
            if (userContext.getTerminalModel().getType() == 0) {//客户经理  不需要确认
                isManage = true;
            }
            //先判断必填参数是否为空
            verifyBaseInfoParam(addTerminalShopInfoScheduleReq, 0);
            verifyActivateNewProtocolParam(addTerminalShopInfoScheduleReq);

            //校验是否已经存在
            verifyExist(addTerminalShopInfoScheduleReq);

            //校验营业执照是否存在且为真，如果不是真，那么抛异常，提示【需上传营业执照同时营业执照为真】
            verifyLicense(addTerminalShopInfoScheduleReq);

            //营业执照，合同类型，合同编号，终端类型，食品许可证验证
//            verifyLisenceAndContractType(addTerminalShopInfoScheduleReq);

            //填入公司id
            addTerminalShopInfoScheduleReq.setCompanyId(gtCompanyId);

            //处理编码参数
            disposeMainCodeAndDeputyCode(addTerminalShopInfoScheduleReq);
            //判断是否为高端会员
            if(Objects.nonNull(addTerminalShopInfoScheduleReq.getPrimaryContract()) && (Objects.nonNull(addTerminalShopInfoScheduleReq.getPrimaryContract().getContractType()) && (addTerminalShopInfoScheduleReq.getPrimaryContract().getContractType().equals(12)))){
                addTerminalShopInfoScheduleReq.setIsHighMember(1);
            }
            //初始化主副表对象
            TerminalShopInfoScheduleModel scheduleModel = new TerminalShopInfoScheduleModel();
            TerminalShopModel mainModel = new TerminalShopModel();
            //List<TerminalProtocolReq> protocolList = addTerminalShopInfoScheduleReq.getProtocolList();
            Boolean isInsert = true;
            //判断新增或者修改
            if (Objects.isNull(addTerminalShopInfoScheduleReq.getTerminalShopId())) {
                //新增
                BeanUtils.copyProperties(addTerminalShopInfoScheduleReq, scheduleModel);
                //附表状态为审批状态
                scheduleModel.setStatus(2);
                //填入公司id
                scheduleModel.setCompanyId(userContext.getTerminalModel().getCompanyId());
                //填入创建人
                scheduleModel.setCreateUser(userContext.getTerminalModel().getId());
                //填入创建时间
                scheduleModel.setCreateTime(new Date());
                BeanUtils.copyProperties(scheduleModel, mainModel);
                //主表只有激活和未激活状态
                mainModel.setStatus(0);
                mainModel.setIsPrepare(1);
                scheduleModel.setIsPrepare(1);
                if (isManage) {
                    mainModel.setStatus(1);
                    mainModel.setIsPrepare(0);
                    scheduleModel.setStatus(1);
                    scheduleModel.setIsPrepare(0);
                }
                // 写入执照鉴真记录
                if (addTerminalShopInfoScheduleReq.getIsLicenseSuccess() != null) {
                    mainModel.setIsLicenseSuccess(addTerminalShopInfoScheduleReq.getIsLicenseSuccess());
                } else {
                    throw new BusinessException("400", "需上传营业执照同时营业执照为真");
                }
                terminalShopDao.insert(mainModel);
                scheduleModel.setTerminalShopId(mainModel.getId());
                log.info("1111----->{}", scheduleModel.getStatus());
                terminalShopInfoScheduleDao.insert(scheduleModel);
            } else {
                isInsert = false;
                //修改
                TerminalShopInfoScheduleModel oldScheduleModel = terminalShopInfoScheduleDao.selectOne(new QueryWrapper<TerminalShopInfoScheduleModel>()
                        .eq("terminal_shop_id", addTerminalShopInfoScheduleReq.getTerminalShopId())
                        .eq("is_delete", 0)
                        .last("limit 1"));
                TerminalShopModel oldMainModel = terminalShopDao.selectOne(new QueryWrapper<TerminalShopModel>()
                        .eq("id", addTerminalShopInfoScheduleReq.getTerminalShopId())
                        .eq("is_delete", 0)
                        .last("limit 1"));
                scheduleModel = getScheduleModel(oldScheduleModel, addTerminalShopInfoScheduleReq);
                mainModel = getMainModel(oldMainModel, addTerminalShopInfoScheduleReq);
                scheduleModel.setStatus(2);
                mainModel.setStatus(0);
                scheduleModel.setUpdateTime(new Date());
                mainModel.setUpdateTime(new Date());

                if (isManage) {
                    mainModel.setStatus(1);
                    mainModel.setIsPrepare(0);
                    scheduleModel.setStatus(1);
                    scheduleModel.setIsPrepare(0);
                }

                // 写入执照鉴真记录
                if (addTerminalShopInfoScheduleReq.getIsLicenseSuccess() != null) {
                    mainModel.setIsLicenseSuccess(addTerminalShopInfoScheduleReq.getIsLicenseSuccess());
                } else {
                    throw new BusinessException("400", "需上传营业执照同时营业执照为真");
                }
                log.info("1112----->{}", scheduleModel.getStatus());
                terminalShopInfoScheduleDao.updateById(scheduleModel);
                terminalShopDao.updateById(mainModel);
                // 2024年新政策，采集终端时不需要绑定协议,修改时也不修改协议信息
                //if (CollectionUtils.isNotEmpty(protocolList)) {
                //    //删除协议
                //    terminalProtocolDao.deleteByShopId(mainModel.getId());
                //}
                if (Objects.nonNull(addTerminalShopInfoScheduleReq.getPrimaryContract())) {
                    //删除合同
                    terminalShopContractDao.deleteByShopId(mainModel.getId());
                }
            }
            // 更新陌生终端表关联id
            if (Objects.nonNull(addTerminalShopInfoScheduleReq.getStrangeTerminalId())) {
                StrangeTerminalShopModel strangeTerminalShopModel = new StrangeTerminalShopModel();
                strangeTerminalShopModel.setId(addTerminalShopInfoScheduleReq.getStrangeTerminalId());
                strangeTerminalShopModel.setLinkId(mainModel.getId());
                strangeTerminalShopDao.updateById(strangeTerminalShopModel);
            }
            //添加协议
            //List<TerminalProtocolModel> protocolModelList = insertProtocol(addTerminalShopInfoScheduleReq.getProtocolList(), mainModel, addTerminalShopInfoScheduleReq.getPrimaryContract());
            //添加合同
            insertContract(addTerminalShopInfoScheduleReq.getPrimaryContract(), mainModel.getId(), mainModel.getMemberShopId());

            //添加日志记录
            Integer dataLogId = newInsertDataLog(addTerminalShopInfoScheduleReq, mainModel, scheduleModel, null, isInsert);
            log.info("1113----->{}", scheduleModel.getStatus());
            //添加审批的节点数据
            TerminalShopNodeModel terminalShopNodeModel = TerminalShopNodeModel.builder()
                    .terminalShopId(mainModel.getId())
                    .companyId(mainModel.getCompanyId())
                    .nodeName(TerminalShopNodeEnum.TERMINAL_COLLECT.getName())
                    .nodeLevel("0")
                    .nodeType(TerminalShopNodeEnum.TERMINAL_COLLECT.getType())
                    .isBack("0")
                    .nodeStatus("0")
                    .createTime(new Date())
                    .dataLogId(dataLogId)
                    .scheduleShopId(scheduleModel.getId())
                    .build();
            //设置客户经理信息
            setMangerInfo(mainModel.getCreateUser(), terminalShopNodeModel);
            String currentYm = DateUtils.convert2String(new Date(), "yyyy-MM");
            terminalShopNodeModel.setMonthYear(currentYm);
            if (isManage) {
                terminalShopNodeModel.setNodeStatus("1");
                terminalShopNodeModel.setUpdateMsg("自动审核通过");
                terminalShopNodeModel.setApprovalUser(userContext.getTerminalModel().getId());
                terminalShopNodeModel.setUpdateDate(new Date());
            }
            terminalShopNodeDao.insert(terminalShopNodeModel);
            TerminalShopNodeReq terminalShopNodeReq = new TerminalShopNodeReq();
            BeanUtils.copyProperties(terminalShopNodeModel, terminalShopNodeReq);
            // 经销商业代，经销商人员点击提交审核不同步中台
            terminalShopService.handleYbTerminalJhNewProtocol(terminalShopNodeReq, redisKeyList, scheduleModel.getStatus(), isManage);

            /*if(userContext.getTerminalModel().getType() == 0){ //客户经理  不需要确认
                terminalShopNodeReq.setNodeStatus("1");
                terminalShopNodeReq.setIsBack("0");
                terminalShopNodeReq.setUpdateMsg("自动通过");
                log.info("客户经理异步通过{}", JSONObject.toJSONString(terminalShopNodeReq));
                terminalShopService.approvalTerminalShop(terminalShopNodeReq);
            }*/
            reqName = reqName.concat("【成功】");
            requestLog.setResMsg("提交终端信息成功");
        } catch (Exception e) {
            e.printStackTrace();
            requestLog.setResCode("-1");
            requestLog.setResMsg("提交终端信息失败:" + e.getMessage());
            throw new BusinessException("400", e.getMessage());
        } finally {
            watch.stop();
            redisTemplate.delete(redisKeyList);
            String diffTime = DateUtils.longToHMS(watch.getTotalTimeMillis());
            requestLog.setReqTime(diffTime);
            requestLog.setReqName(reqName);
            requestLogService.insertLog(requestLog);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTerminalShopBaseInfo(AddTerminalShopInfoScheduleReq terminalShopReq) {
        StopWatch watch = new StopWatch();
        watch.start();
        log.info("终端采集修改参数：{}", JSON.toJSONString(terminalShopReq));
        RequestLog requestLog = new RequestLog();
        requestLog.setReqType(30);
        requestLog.setReqUrlPath("TerminalShopInfoScheduleServiceImpl/updateTerminalShopBaseInfo");
        requestLog.setReqKey(terminalShopReq.getShopName());
        requestLog.setReqJson(JSONObject.toJSONString(terminalShopReq));
        requestLog.setResCode("0");
        requestLog.setCreateDate(new Date());
        String resRtnJson = "";
        String reqName = "【修改终端信息】" + terminalShopReq.getShopName() + ",id=" + terminalShopReq.getId();
        Set<String> redisKeyList = new HashSet<>();
        try {
            // 是否是客户经理
            Boolean isManage = false;
            if (userContext.getTerminalModel().getType() == 0) {//客户经理  不需要确认
                isManage = true;
                resRtnJson = "客户经理【" + userContext.getTerminalModel().getName() + "】修改";
            } else {
                resRtnJson = "【" + userContext.getTerminalModel().getName() + "】修改";
            }

            //查询主表数据
            TerminalShopModel mainModel = terminalShopDao.selectOne(new QueryWrapper<TerminalShopModel>()
                    .eq("id", terminalShopReq.getId())
                    .eq("is_delete", 0)
                    .last("limit 1"));
            if (Objects.isNull(mainModel)) {
                throw new BusinessException("400", "该终端已停用");
            }
            if (1 != mainModel.getStatus()) {
                throw new BusinessException("400", "终端未激活");
            }
            Integer isPrepare = mainModel.getIsPrepare();
            resRtnJson = resRtnJson.concat(isPrepare == 0 ? ",非预备" : "预备");
            resRtnJson = resRtnJson.concat(",shop_id=" + mainModel.getMemberShopId());
            //查询是否有审核记录
            TerminalShopInfoScheduleModel oldModel = terminalShopInfoScheduleDao.selectOne(new QueryWrapper<TerminalShopInfoScheduleModel>()
                    .eq("terminal_shop_id", terminalShopReq.getId())
                    .eq("is_delete", 0)
                    .last("limit 1"));
            if (Objects.nonNull(oldModel)) {
                if (oldModel.getStatus().equals(0)) {
                    throw new BusinessException("400", "终端未激活");
                }
                if (oldModel.getStatus().equals(2) || oldModel.getStatus().equals(4)) {
                    throw new BusinessException("400", "终端资料审批中");
                }
            }

            //先判断必填参数是否为空
            verifyBaseInfoParam(terminalShopReq, 0);

            //校验是否已经存在的接口
            verifyExist(terminalShopReq);

            //转换类型
            TerminalShopInfoScheduleModel terminalShopModel = new TerminalShopInfoScheduleModel();
            if (Objects.nonNull(oldModel)) {
                terminalShopModel = getScheduleModel(oldModel, terminalShopReq);
                terminalShopModel.setStatus(2);
                if (isManage) {
                    terminalShopModel.setStatus(1);
                    mainModel.setStatus(1);
                    terminalShopModel.setIsPrepare(0);
                    mainModel.setIsPrepare(0);
                }
                terminalShopModel.setUpdateTime(new Date());
                //更新
                terminalShopInfoScheduleDao.updateById(terminalShopModel);
            } else {
                BeanUtils.copyProperties(mainModel, terminalShopModel);
                terminalShopModel = getScheduleModel(terminalShopModel, terminalShopReq);
                terminalShopModel.setTerminalShopId(mainModel.getId());
                terminalShopModel.setId(null);
                terminalShopModel.setStatus(2);
                if (isManage) {
                    terminalShopModel.setStatus(1);
                    mainModel.setStatus(1);
                    terminalShopModel.setIsPrepare(0);
                    mainModel.setIsPrepare(0);
                }
                terminalShopModel.setUpdateTime(new Date());
                terminalShopInfoScheduleDao.insert(terminalShopModel);
            }
            //添加日志记录
            Integer dataLogId = insertDataLog(terminalShopReq, mainModel, terminalShopModel, terminalShopReq.getProtocolList(), false);


            //添加审批的节点数据
            TerminalShopNodeModel terminalShopNodeModel = TerminalShopNodeModel.builder()
                    .terminalShopId(mainModel.getId())
                    .companyId(mainModel.getCompanyId())
                    .nodeName("终端资料修改申请")
                    .nodeLevel("0")
                    .nodeType(3)
                    .isBack("0")
                    .nodeStatus("0")
                    .createTime(new Date())
                    .dataLogId(dataLogId)
                    .scheduleShopId(terminalShopModel.getId())
                    .build();
            //设置客户经理信息
            setMangerInfo(terminalShopReq.getCreateUser(), terminalShopNodeModel);
            String currentYm = DateUtils.convert2String(new Date(), "yyyy-MM");
            terminalShopNodeModel.setMonthYear(currentYm);

            if (isManage) {
                TerminalShopModel newShopModel = new TerminalShopModel();
                BeanUtils.copyProperties(terminalShopModel, newShopModel);
                newShopModel.setId(terminalShopModel.getTerminalShopId());

                terminalShopNodeModel.setNodeStatus("1");
                terminalShopNodeModel.setUpdateMsg("自动审核通过");
                terminalShopNodeModel.setApprovalUser(userContext.getTerminalModel().getId());
                terminalShopNodeModel.setUpdateDate(new Date());
                TerminalShopContractResp terminalShopContractResp = terminalShopContractDao.selectTerminalShopContractResp(terminalShopModel.getTerminalShopId());
                TerminalProtocolModel terminalProtocolModel = terminalProtocolDao.selectProtocolByTerminalShopId(terminalShopModel.getTerminalShopId());
                Integer mShopId = terminalShopService.insertHandleMemberShopAndMemberUser(terminalShopModel, terminalShopContractResp, terminalProtocolModel, mainModel, true, redisKeyList);
                newShopModel.setMemberShopId(mShopId);
                newShopModel.setMemberShopId(mShopId);

                terminalShopService.sendZtData(terminalShopModel, newShopModel, terminalShopContractResp, 1, "终端", null);
                if (isPrepare == 1) {
                    //处理积分数据
                    terminalShopCommonService.handlePrepareTerminalData(mainModel.getMemberShopId());

                    //更新正式终端
                    shopDao.updateIsPrepareByShopId(mainModel.getMemberShopId());
                }

                terminalShopDao.updateById(newShopModel);

                // terminalShopService.sendMessage(terminalShopModel, 1);
            }
            resRtnJson = resRtnJson.concat(",state=" + mainModel.getStatus());
            terminalShopNodeDao.insert(terminalShopNodeModel);

            /*TerminalShopNodeReq terminalShopNodeReq = new TerminalShopNodeReq();
            BeanUtils.copyProperties(terminalShopNodeModel, terminalShopNodeReq);
            if(userContext.getTerminalModel().getType() == 0){ //客户经理  不需要确认
                terminalShopNodeReq.setNodeStatus("1");
                terminalShopNodeReq.setIsBack("0");
                terminalShopNodeReq.setUpdateMsg("自动通过");
                terminalShopService.approvalTerminalShop(terminalShopNodeReq);
            }*/
            reqName = reqName.concat("【成功】");
            requestLog.setResMsg("修改终端信息成功");
        } catch (Exception e) {
            e.printStackTrace();
            requestLog.setResCode("-1");
            requestLog.setResMsg("修改终端信息失败:" + e.getMessage());
            throw new BusinessException("400", e.getMessage());
        } finally {
            redisTemplate.delete(redisKeyList);
            watch.stop();
            requestLog.setResRtnJson(resRtnJson);
            String diffTime = DateUtils.longToHMS(watch.getTotalTimeMillis());
            requestLog.setReqTime(diffTime);
            requestLog.setReqName(reqName);
            requestLogService.insertLog(requestLog);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTerminalShopBaseInfoNewProtocol(AddTerminalShopInfoScheduleReq terminalShopReq) {
        StopWatch watch = new StopWatch();
        watch.start();
        log.info("终端采集修改参数：{}", JSON.toJSONString(terminalShopReq));
        RequestLog requestLog = new RequestLog();
        requestLog.setReqType(30);
        requestLog.setReqUrlPath("TerminalShopInfoScheduleServiceImpl/updateTerminalShopBaseInfoNewProtocol");
        requestLog.setReqKey(terminalShopReq.getShopName());
        requestLog.setReqJson(JSONObject.toJSONString(terminalShopReq));
        requestLog.setResCode("0");
        requestLog.setCreateDate(new Date());
        String resRtnJson = "";
        String reqName = "【修改终端信息】" + terminalShopReq.getShopName() + ",id=" + terminalShopReq.getId();
        Set<String> redisKeyList = new HashSet<>();
        try {
            // 是否是客户经理
            Boolean isManage = false;
            if (userContext.getTerminalModel().getType() == 0) {//客户经理  不需要确认
                isManage = true;
                resRtnJson = "客户经理【" + userContext.getTerminalModel().getName() + "】修改";
            } else {
                resRtnJson = "【" + userContext.getTerminalModel().getName() + "】修改";
            }

            //查询主表数据
            TerminalShopModel mainModel = terminalShopDao.selectOne(new QueryWrapper<TerminalShopModel>()
                    .eq("id", terminalShopReq.getId())
                    .eq("is_delete", 0)
                    .last("limit 1"));
            if (Objects.isNull(mainModel)) {
                throw new BusinessException("400", "该终端已停用");
            }
            if (1 != mainModel.getStatus()) {
                throw new BusinessException("400", "终端未激活");
            }
            Integer isPrepare = mainModel.getIsPrepare();
            resRtnJson = resRtnJson.concat(isPrepare == 0 ? ",非预备" : "预备");
            resRtnJson = resRtnJson.concat(",shop_id=" + mainModel.getMemberShopId());
            //查询是否有审核记录
            TerminalShopInfoScheduleModel oldModel = terminalShopInfoScheduleDao.selectOne(new QueryWrapper<TerminalShopInfoScheduleModel>()
                    .eq("terminal_shop_id", terminalShopReq.getId())
                    .eq("is_delete", 0)
                    .last("limit 1"));
            if (Objects.nonNull(oldModel)) {
                if (oldModel.getStatus().equals(0)) {
                    throw new BusinessException("400", "终端未激活");
                }
                if (oldModel.getStatus().equals(2) || oldModel.getStatus().equals(4)) {
                    throw new BusinessException("400", "终端资料审批中");
                }
            }

            //先判断必填参数是否为空
            verifyBaseInfoParam(terminalShopReq, 0);

            //校验是否已经存在的接口
            verifyExist(terminalShopReq);

            if (Optional.ofNullable(terminalShopReq.getShopType())
                    .map(shopType -> terminalShopService.isHotelTerminalByShopType(shopType))
                    .orElse(false) ||
                    Optional.ofNullable(terminalShopReq.getTerminalShopId())
                            .map(terminalShopCommonService::isHotelTerminalByTerminalShopId)
                            .orElse(false)) {
                if (terminalShopReq.getIsLicenseSuccess() != null) {
                    verifyLicense(terminalShopReq);
                } else {
                    throw new BusinessException("400", "需上传营业执照同时营业执照为真");
                }
                if (!terminalShopReq.getLicenseCode().equals(terminalShopReq.getFoodLicenseCode())) {
                    throw new BusinessException("400", "食品经营许可证的社会统一信息代码需与营业执照社会统一信息代码一致");
                }
                // 五合一验证
                verifyLicenseCodeAndShopType(terminalShopReq);
                // 先查询是否有相同的营业执照和终端类型的终端
                verifyExistTerminalShopByLisence(terminalShopReq, "Hotel");
                verifyLisenceAndContractTypeByHotel(terminalShopReq);
                verifyLisenceAndContractCode(terminalShopReq, "Hotel");
            } else {
                //校验营业执照是否存在且为真
                if(!terminalShopReq.getShopType().equals(TerminalShopTypeEnum.together_shopping_terminal.getType())) {
                    if (terminalShopReq.getIsLicenseSuccess() != null) {
                        verifyLicense(terminalShopReq);
                    } else {
                        throw new BusinessException("400", "需上传营业执照同时营业执照为真");
                    }
                }

                //校验营业执照与合同类型是否已有已激活的终端,修改了营业执照
                // 编辑时不验证执照与合同类型
//                if (!terminalShopReq.getShopType().equals(2) && mainModel.getLicenseCode() != terminalShopReq.getLicenseCode()) {
                    // 先查询是否有相同的营业执照和终端类型的终端
                    // 修改资料时相同营业执照下有不同终端类型仍能修改
//                    verifyExistTerminalShopByLisence(terminalShopReq, "notHotel");
//                    verifyLisenceAndContractType(terminalShopReq);
                    // 同一张营业执照已有酒店餐饮终端的情况下，不允许激活非酒店餐饮终端
//                    verifyLisenceByHotelTerminal(terminalShopReq);
//                }
            }

            //转换类型
            TerminalShopInfoScheduleModel terminalShopModel = new TerminalShopInfoScheduleModel();
            if (Objects.nonNull(oldModel)) {
                terminalShopModel = getScheduleModel(oldModel, terminalShopReq);
                terminalShopModel.setStatus(2);
                if (isManage) {
                    terminalShopModel.setStatus(1);
                    mainModel.setStatus(1);
                    terminalShopModel.setIsPrepare(0);
                    mainModel.setIsPrepare(0);
                }
                terminalShopModel.setUpdateTime(new Date());
                //更新
                terminalShopInfoScheduleDao.updateById(terminalShopModel);
                //更新主表状态
                mainModel.setStatus(2);
                terminalShopDao.updateById(mainModel);
            } else {
                BeanUtils.copyProperties(mainModel, terminalShopModel);
                terminalShopModel = getScheduleModel(terminalShopModel, terminalShopReq);
                terminalShopModel.setTerminalShopId(mainModel.getId());
                terminalShopModel.setId(null);
                terminalShopModel.setStatus(2);
                if (isManage) {
                    terminalShopModel.setStatus(1);
                    mainModel.setStatus(1);
                    terminalShopModel.setIsPrepare(0);
                    mainModel.setIsPrepare(0);
                }
                terminalShopModel.setUpdateTime(new Date());
                terminalShopInfoScheduleDao.insert(terminalShopModel);
                //更新主表状态
//                mainModel.setStatus(2);
//                terminalShopDao.insert(mainModel);
            }

            // 写入执照鉴真记录
            TerminalShopModel shopModelByLicense = terminalShopDao.selectOne(new QueryWrapper<TerminalShopModel>()
                    .eq("id", mainModel.getId())
                    .eq("is_delete", 0)
                    .last("limit 1"));
            if(!terminalShopReq.getShopType().equals(2)) {
                shopModelByLicense.setIsLicenseSuccess(terminalShopReq.getIsLicenseSuccess());
            } else {
                shopModelByLicense.setIsLicenseSuccess(0);
            }
            terminalShopDao.updateById(shopModelByLicense);
            // 写入食品经营许可证编码
            if (Optional.ofNullable(terminalShopReq.getShopType())
                    .map(shopType -> terminalShopService.isHotelTerminalByShopType(shopType))
                    .orElse(false) ||
                    Optional.ofNullable(terminalShopReq.getTerminalShopId())
                            .map(terminalShopCommonService::isHotelTerminalByTerminalShopId)
                            .orElse(false)) {
                TerminalShopModel shopModelByFoodLicense = terminalShopDao.selectOne(new QueryWrapper<TerminalShopModel>()
                        .eq("id", mainModel.getId())
                        .eq("is_delete", 0)
                        .last("limit 1"));
                shopModelByLicense.setFoodBusinessLicenseCode(terminalShopReq.getFoodBusinessLicenseCode());
                terminalShopDao.updateById(shopModelByFoodLicense);
            }

            //添加日志记录
            // Integer dataLogId = insertDataLog(terminalShopReq, mainModel, terminalShopModel, terminalShopReq.getProtocolList(), false);
            List<TerminalProductProtocolRelationResp> terminalProductProtocolRelationRespList = terminalProductProtocolRelationService.selectTerminalProductProtocolRelationByTerminalId(mainModel.getId().longValue());
            Integer dataLogId = newInsertDataLog(terminalShopReq, mainModel, terminalShopModel, terminalProductProtocolRelationRespList, false);


            //添加审批的节点数据
            TerminalShopNodeModel terminalShopNodeModel = TerminalShopNodeModel.builder()
                    .terminalShopId(mainModel.getId())
                    .companyId(mainModel.getCompanyId())
                    .nodeName(TerminalShopNodeEnum.TERMINAL_COLLECT_EDIT.getName())
                    .nodeLevel("0")
                    .nodeType(TerminalShopNodeEnum.TERMINAL_COLLECT_EDIT.getType())
                    .isBack("0")
                    .nodeStatus("0")
                    .createTime(new Date())
                    .dataLogId(dataLogId)
                    .scheduleShopId(terminalShopModel.getId())
                    .build();
            //设置客户经理信息
            setMangerInfo(terminalShopReq.getCreateUser(), terminalShopNodeModel);
            String currentYm = DateUtils.convert2String(new Date(), "yyyy-MM");
            terminalShopNodeModel.setMonthYear(currentYm);

            if (isManage) {
                TerminalShopModel newShopModel = new TerminalShopModel();
                BeanUtils.copyProperties(terminalShopModel, newShopModel);
                newShopModel.setId(terminalShopModel.getTerminalShopId());

                terminalShopNodeModel.setNodeStatus("1");
                terminalShopNodeModel.setUpdateMsg("自动审核通过");
                terminalShopNodeModel.setApprovalUser(userContext.getTerminalModel().getId());
                terminalShopNodeModel.setUpdateDate(new Date());
                TerminalShopContractResp terminalShopContractResp = terminalShopContractDao.selectTerminalShopContractResp(terminalShopModel.getTerminalShopId());
                TerminalProtocolModel terminalProtocolModel = terminalProtocolDao.selectProtocolByTerminalShopId(terminalShopModel.getTerminalShopId());
                Integer mShopId = terminalShopService.insertHandleMemberShopAndMemberUser(terminalShopModel, terminalShopContractResp, terminalProtocolModel, mainModel, true, redisKeyList);
                newShopModel.setMemberShopId(mShopId);

                // terminalShopService.sendZtData(terminalShopModel, newShopModel, terminalShopContractResp, 1, "终端", null);
                // 客户经理修改终端信息时，同步中台
                terminalShopService.sendZtStateDataNewProtocol(terminalShopModel, terminalShopContractResp, 1);
                if (isPrepare == 1) {
                    //处理积分数据
                    terminalShopCommonService.handlePrepareTerminalData(mainModel.getMemberShopId());

                    //更新正式终端
                    shopDao.updateIsPrepareByShopId(mainModel.getMemberShopId());
                }

                terminalShopDao.updateById(newShopModel);
                if (isManage) {
                    // 修改资料时如果营业执照为假或无营业执照
                    if(mainModel.getIsLicenseSuccess().equals(0) || mainModel.getWhetherLicense().equals(0)) {
                        // 同一物理终端编码操作
                        newShopModel.setMergeType(mainModel.getMergeType());
                        setSamePhysicalCode(newShopModel, 1);
                    }
                }

                // terminalShopService.sendMessage(terminalShopModel, 1);
            }
            resRtnJson = resRtnJson.concat(",state=" + mainModel.getStatus());
            terminalShopNodeDao.insert(terminalShopNodeModel);

            /*TerminalShopNodeReq terminalShopNodeReq = new TerminalShopNodeReq();
            BeanUtils.copyProperties(terminalShopNodeModel, terminalShopNodeReq);
            if(userContext.getTerminalModel().getType() == 0){ //客户经理  不需要确认
                terminalShopNodeReq.setNodeStatus("1");
                terminalShopNodeReq.setIsBack("0");
                terminalShopNodeReq.setUpdateMsg("自动通过");
                terminalShopService.approvalTerminalShop(terminalShopNodeReq);
            }*/
            reqName = reqName.concat("【成功】");
            requestLog.setResMsg("修改终端信息成功");
        } catch (Exception e) {
            e.printStackTrace();
            requestLog.setResCode("-1");
            requestLog.setResMsg("修改终端信息失败:" + e.getMessage());
            throw new BusinessException("400", e.getMessage());
        } finally {
            redisTemplate.delete(redisKeyList);
            watch.stop();
            requestLog.setResRtnJson(resRtnJson);
            String diffTime = DateUtils.longToHMS(watch.getTotalTimeMillis());
            requestLog.setReqTime(diffTime);
            requestLog.setReqName(reqName);
            requestLogService.insertLog(requestLog);
        }
    }

    @Override
    public List<TerminalShopModel> checkExist(String contractCode, String licenseCode) {
        List<TerminalShopModel> list = terminalShopDao.checkExist(contractCode, licenseCode);
        return Optional.of(list).orElse(new ArrayList<>());
    }


    /**
     * @description: 终端采集参数保存校验，不包含会员
     * @author: MAX
     * @date: 2023/2/28 16:15
     * @param: [addTerminalShopInfoScheduleReq]
     * @return: void
     **/
    public void verifyBaseInfoParam(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq, int flag) {
        // 判断是否是酒店终端
        if (Optional.ofNullable(addTerminalShopInfoScheduleReq.getShopType())
                .map(shopType -> terminalShopService.isHotelTerminalByShopType(shopType))
                .orElse(false) ||
                Optional.ofNullable(addTerminalShopInfoScheduleReq.getTerminalShopId())
                        .map(terminalShopCommonService::isHotelTerminalByTerminalShopId)
                        .orElse(false)) {
            if (null == addTerminalShopInfoScheduleReq.getShopType()) {
                throw new BusinessException("400", "终端类型为空");
            }
            if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getShopName())) {
                throw new BusinessException("400", "终端名称为空");
            }
            if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getFoodBusinessLicense())) {
                throw new BusinessException("400", "食品经营许可证为空");
            }
            if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getFoodBusinessLicenseCode())) {
                throw new BusinessException("400", "食品经营许可证-许可证编号为空");
            }
            if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getFoodLicenseCode())) {
                throw new BusinessException("400", "食品经营许可证-社会信用代码为空");
            }
            if (Objects.isNull(addTerminalShopInfoScheduleReq.getWhetherLicense())) {
                throw new BusinessException("400", "终端店有无营业执照为空");
            } else {
                //判断有无营业执照 1是有 有的情况下验证营业执照照片和营业执照号
                if (1 == addTerminalShopInfoScheduleReq.getWhetherLicense()) {
                    if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getLicenseImg())) {
                        throw new BusinessException("400", "终端店营业执照照片为空");
                    }

                    if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getLicenseCode())) {
                        throw new BusinessException("400", "终端店营业执照编码为空");
                    }
                }
            }
            if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getHeadImg())) {
                throw new BusinessException("400", "终端门头照为空");
            }
            if (flag == 0) {
                if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getLeaderName())) {
                    throw new BusinessException("400", "负责人名称为空");
                }

                if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getLeaderPhone())) {
                    throw new BusinessException("400", "负责人手机号为空");
                }
                if (Objects.isNull(addTerminalShopInfoScheduleReq.getWhetherProprietaryTrading())) {
                    throw new BusinessException("400", "终端店是否是自营终端为空");
                }

                if (Objects.isNull(addTerminalShopInfoScheduleReq.getIsImage())) {
                    throw new BusinessException("400", "终端店是否是形象店为空");
                } else {
                    //如果形象店不为空 需要判断形象店门头照是不是为空
                    if (addTerminalShopInfoScheduleReq.getIsImage().equals(1)) {
                        if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getImageHeadPicture())) {
                            throw new BusinessException("400", "终端店形象店门头照为空");
                        }
                    }
                }
            }
        } else {
            //        if (Objects.isNull(addTerminalShopInfoScheduleReq.getTag())) {
//            throw new BusinessException("400", "终端标记为空");
//        }
            if (null == addTerminalShopInfoScheduleReq.getShopType()) {
                throw new BusinessException("400", "终端类型为空");
            }
//        //国台让去掉区的校验
//        if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getDistrict())) {
//            throw new BusinessException("400", "终端店区为空");
//        }

            if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getProvince())) {
                throw new BusinessException("400", "终端店省为空");
            }

            if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getCity())) {
                throw new BusinessException("400", "终端店市为空");
            }

            if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getAddress())) {
                throw new BusinessException("400", "终端店地址为空");
            }

            if (null == addTerminalShopInfoScheduleReq.getLongitude()) {
                throw new BusinessException("400", "终端店经度为空");
            }

            if (null == addTerminalShopInfoScheduleReq.getLatitude()) {
                throw new BusinessException("400", "终端店纬度为空");
            }

            if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getShopName())) {
                throw new BusinessException("400", "终端名称为空");
            }

            if (flag == 0) {
                if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getLeaderName())) {
                    throw new BusinessException("400", "负责人名称为空");
                }

                if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getLeaderPhone())) {
                    throw new BusinessException("400", "负责人手机号为空");
                }
            }

            //团购终端不判断营业执照等信息
            if (!addTerminalShopInfoScheduleReq.getShopType().equals(2)) {
                if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getHeadImg())) {
                    throw new BusinessException("400", "终端门头照为空");
                }
            }
            //渠道和连锁需要验证自营和形象店以及仓库地址
            if (addTerminalShopInfoScheduleReq.getShopType().equals(0) && addTerminalShopInfoScheduleReq.getShopType().equals(4)) {
                if (Objects.isNull(addTerminalShopInfoScheduleReq.getWhetherProprietaryTrading())) {
                    throw new BusinessException("400", "终端店是否是自营终端为空");
                }

                if (Objects.isNull(addTerminalShopInfoScheduleReq.getIsImage())) {
                    throw new BusinessException("400", "终端店是否是形象店为空");
                } else {
                    //如果形象店不为空 需要判断形象店门头照是不是为空
                    if (addTerminalShopInfoScheduleReq.getIsImage().equals(1)) {
                        if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getImageHeadPicture())) {
                            throw new BusinessException("400", "终端店形象店门头照为空");
                        }
                    }
                }
           /* if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getReceivingWarehouseAddress())) {
                throw new BusinessException("400", "终端店收货仓库地址为空");
            }*/
            }

            //1：餐饮终端
            if (addTerminalShopInfoScheduleReq.getShopType().equals(1)) {
                if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getFoodBusinessLicense())) {
                    throw new BusinessException("400", "食品经营许可证为空");
                }
            }

            if (2 != addTerminalShopInfoScheduleReq.getShopType()) {

                if (Objects.isNull(addTerminalShopInfoScheduleReq.getWhetherLicense())) {
                    throw new BusinessException("400", "终端店有无营业执照为空");
                } else {
                    //判断有无营业执照 1是有 有的情况下验证营业执照照片和营业执照号
                    if (1 == addTerminalShopInfoScheduleReq.getWhetherLicense()) {
                        if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getLicenseImg())) {
                            throw new BusinessException("400", "终端店营业执照照片为空");
                        }

                        if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getLicenseCode())) {
                            throw new BusinessException("400", "终端店营业执照编码为空");
                        }
                    }
                }

                //如果是餐饮终端 需要验证食品经营许可证
                if (addTerminalShopInfoScheduleReq.getShopType().equals(1)) {
                    if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getFoodBusinessLicense())) {
                        throw new BusinessException("400", "终端食品经营许可证为空");
                    }
                }
            }
        }
    }


    /**
     * @description: 校验激活信息
     * @author: MAX
     * @date: 2023/3/1 9:09
     * @param: [addTerminalShopInfoScheduleReq]
     * @return: void
     **/
    public void verifyActivateParam(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq) {
        verifyBaseInfoParam(addTerminalShopInfoScheduleReq, 0);
        //判断合同是不是空
        if (Objects.isNull(addTerminalShopInfoScheduleReq.getPrimaryContract())) {
            throw new BusinessException("400", "会员终端店经销商信息为空");
        } else {
            if (Objects.isNull(addTerminalShopInfoScheduleReq.getPrimaryContract().getContractType())) {
                throw new BusinessException("400", "会员终端店经销商信息的合同类型为空");
            }

            if (Objects.isNull(addTerminalShopInfoScheduleReq.getPrimaryContract().getDealerCode())) {
                throw new BusinessException("400", "会员终端店经销商信息的经销商编码为空");
            }

            if (Objects.isNull(addTerminalShopInfoScheduleReq.getPrimaryContract().getContractCode())) {
                throw new BusinessException("400", "会员终端店经销商信息的合同编码为空");
            }
            //20230313阮经理让禁用该校验
            //校验合同是否有效，没有合同的按无效处理
            /*Boolean check = terminalShopContractCommonService.checkShopContractByDealerCodeAndContractCode(
                    addTerminalShopInfoScheduleReq.getPrimaryContract().getDealerCode(),
                    addTerminalShopInfoScheduleReq.getPrimaryContract().getContractCode()
            );

            if (!check) {
                throw new BusinessException("经销商合同无效");
            }*/
        }
        //团购终端不判断营业执照等信息
        if (2 != addTerminalShopInfoScheduleReq.getShopType()) {

//            if (Objects.isNull(addTerminalShopInfoScheduleReq.getWhetherLicense())) {
//                throw new BusinessException("400", "终端店有无营业执照为空");
//            } else {
//                //判断有无营业执照 1是有 有的情况下验证营业执照照片和营业执照号
//                if (1 == addTerminalShopInfoScheduleReq.getWhetherLicense()) {
//                    if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getLicenseImg())) {
//                        throw new BusinessException("400", "终端店营业执照照片为空");
//                    }
//
//                    if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getLicenseCode())) {
//                        throw new BusinessException("400", "终端店营业执照编码为空");
//                    }
//                }
//            }
//
//            //如果是餐饮终端 需要验证食品经营许可证
//            if (addTerminalShopInfoScheduleReq.getShopType().equals(1)) {
//                if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getFoodBusinessLicense())) {
//                    throw new BusinessException("400", "终端食品经营许可证为空");
//                }
//            }

            if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getContactPhone())) {
                throw new BusinessException("400", "终端店联系人手机号为空");
            }

            if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getContactName())) {
                throw new BusinessException("400", "终端店联系人姓名为空");
            }

            if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getContactLevel())) {
                throw new BusinessException("400", "终端店联系人级别为空");
            }

//            if (Objects.isNull(addTerminalShopInfoScheduleReq.getReceivingPaymentType())) {
//                throw new BusinessException("400", "终端店收款方式为空");
//            }
//
//            if (Objects.isNull(addTerminalShopInfoScheduleReq.getReceivingPaymentName())) {
//                throw new BusinessException("400", "终端店收款姓名为空");
//            }
//
//            if (Objects.isNull(addTerminalShopInfoScheduleReq.getReceivingPaymentAccount())) {
//                throw new BusinessException("400", "终端店收款账号为空");
//            }
//
//            if (Objects.isNull(addTerminalShopInfoScheduleReq.getReceivingPaymentBank())) {
//                throw new BusinessException("400", "终端店开户行为空");
//            }
//
//            if (Objects.isNull(addTerminalShopInfoScheduleReq.getReceivingPaymentAccountPicture())) {
//                throw new BusinessException("400", "终端店收款账号图片为空");
//            }


            Integer isHighMember = addTerminalShopInfoScheduleReq.getIsHighMember();
            if (Objects.isNull(isHighMember) || isHighMember == 0) {
                //判断协议
                if (CollectionUtils.isEmpty(addTerminalShopInfoScheduleReq.getProtocolList())) {
                    throw new BusinessException("400", "终端店终端协议为空");
                } else {
                    addTerminalShopInfoScheduleReq.getProtocolList().forEach(item -> {
                        if (Objects.nonNull(item.getProtocolType())) {
                            if (Objects.isNull(item.getProtocolProperty())) {
                                throw new BusinessException("400", "协议类型为空");
                            }
                            if (Objects.isNull(item.getProductType())) {
                                throw new BusinessException("400", "协议产品为空");
                            }
                            if (StringUtils.isBlank(item.getLevelCode())) {
                                throw new BusinessException("400", "终端等级为空");
                            }
                            if (Objects.isNull(item.getProductProtocolConfigId())) {
                                throw new BusinessException("400", "协议产品配置为空");
                            }
                        }
                    });
                }
            }
        }
    }

    /**
     * @description: 校验激活信息
     * @author: MAX
     * @date: 2023/3/1 9:09
     * @param: [addTerminalShopInfoScheduleReq]
     * @return: void
     **/
    public void verifyActivateNewProtocolParam(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq) {
        verifyBaseInfoParam(addTerminalShopInfoScheduleReq, 0);
        if (Optional.ofNullable(addTerminalShopInfoScheduleReq.getShopType())
                .map(shopType -> terminalShopService.isHotelTerminalByShopType(shopType))
                .orElse(false) ||
                Optional.ofNullable(addTerminalShopInfoScheduleReq.getTerminalShopId())
                        .map(terminalShopCommonService::isHotelTerminalByTerminalShopId)
                        .orElse(false)) {
            if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getContactPhone())) {
                throw new BusinessException("400", "终端店联系人手机号为空");
            }

            if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getContactName())) {
                throw new BusinessException("400", "终端店联系人姓名为空");
            }
            //判断合同是不是空
            if (Objects.isNull(addTerminalShopInfoScheduleReq.getPrimaryContract())) {
                throw new BusinessException("400", "会员终端店经销商信息为空");
            } else {
                if (Objects.isNull(addTerminalShopInfoScheduleReq.getPrimaryContract().getDealerCode())) {
                    throw new BusinessException("400", "会员终端店经销商信息的经销商编码为空");
                }

                if (Objects.isNull(addTerminalShopInfoScheduleReq.getPrimaryContract().getContractCode())) {
                    throw new BusinessException("400", "会员终端店经销商信息的合同编码为空");
                }
            }
        } else {
            //判断合同是不是空
            if (Objects.isNull(addTerminalShopInfoScheduleReq.getPrimaryContract())) {
                throw new BusinessException("400", "会员终端店经销商信息为空");
            } else {
                if (Objects.isNull(addTerminalShopInfoScheduleReq.getPrimaryContract().getContractType())) {
                    throw new BusinessException("400", "会员终端店经销商信息的合同类型为空");
                }

                if (Objects.isNull(addTerminalShopInfoScheduleReq.getPrimaryContract().getDealerCode())) {
                    throw new BusinessException("400", "会员终端店经销商信息的经销商编码为空");
                }

                if (Objects.isNull(addTerminalShopInfoScheduleReq.getPrimaryContract().getContractCode())) {
                    throw new BusinessException("400", "会员终端店经销商信息的合同编码为空");
                }
                //20230313阮经理让禁用该校验
                //校验合同是否有效，没有合同的按无效处理
            /*Boolean check = terminalShopContractCommonService.checkShopContractByDealerCodeAndContractCode(
                    addTerminalShopInfoScheduleReq.getPrimaryContract().getDealerCode(),
                    addTerminalShopInfoScheduleReq.getPrimaryContract().getContractCode()
            );

            if (!check) {
                throw new BusinessException("经销商合同无效");
            }*/
            }
            //团购终端不判断营业执照等信息
            if (2 != addTerminalShopInfoScheduleReq.getShopType()) {

//            if (Objects.isNull(addTerminalShopInfoScheduleReq.getWhetherLicense())) {
//                throw new BusinessException("400", "终端店有无营业执照为空");
//            } else {
//                //判断有无营业执照 1是有 有的情况下验证营业执照照片和营业执照号
//                if (1 == addTerminalShopInfoScheduleReq.getWhetherLicense()) {
//                    if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getLicenseImg())) {
//                        throw new BusinessException("400", "终端店营业执照照片为空");
//                    }
//
//                    if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getLicenseCode())) {
//                        throw new BusinessException("400", "终端店营业执照编码为空");
//                    }
//                }
//            }
//
//            //如果是餐饮终端 需要验证食品经营许可证
//            if (addTerminalShopInfoScheduleReq.getShopType().equals(1)) {
//                if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getFoodBusinessLicense())) {
//                    throw new BusinessException("400", "终端食品经营许可证为空");
//                }
//            }

                if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getContactPhone())) {
                    throw new BusinessException("400", "终端店联系人手机号为空");
                }

                if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getContactName())) {
                    throw new BusinessException("400", "终端店联系人姓名为空");
                }

                if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getContactLevel())) {
                    throw new BusinessException("400", "终端店联系人级别为空");
                }

//            if (Objects.isNull(addTerminalShopInfoScheduleReq.getReceivingPaymentType())) {
//                throw new BusinessException("400", "终端店收款方式为空");
//            }
//
//            if (Objects.isNull(addTerminalShopInfoScheduleReq.getReceivingPaymentName())) {
//                throw new BusinessException("400", "终端店收款姓名为空");
//            }
//
//            if (Objects.isNull(addTerminalShopInfoScheduleReq.getReceivingPaymentAccount())) {
//                throw new BusinessException("400", "终端店收款账号为空");
//            }
//
//            if (Objects.isNull(addTerminalShopInfoScheduleReq.getReceivingPaymentBank())) {
//                throw new BusinessException("400", "终端店开户行为空");
//            }
//
//            if (Objects.isNull(addTerminalShopInfoScheduleReq.getReceivingPaymentAccountPicture())) {
//                throw new BusinessException("400", "终端店收款账号图片为空");
//            }


                // 新的协议需求中，采集终端时不绑定协议
                //Integer isHighMember = addTerminalShopInfoScheduleReq.getIsHighMember();
                //if (Objects.isNull(isHighMember) || isHighMember == 0) {
                //    //判断协议
                //    if (CollectionUtils.isEmpty(addTerminalShopInfoScheduleReq.getProtocolList())) {
                //        throw new BusinessException("400", "终端店终端协议为空");
                //    } else {
                //        addTerminalShopInfoScheduleReq.getProtocolList().forEach(item -> {
                //            if (Objects.nonNull(item.getProtocolType())) {
                //                if (Objects.isNull(item.getProtocolProperty())) {
                //                    throw new BusinessException("400", "协议类型为空");
                //                }
                //                if (Objects.isNull(item.getProductType())) {
                //                    throw new BusinessException("400", "协议产品为空");
                //                }
                //                if (StringUtils.isBlank(item.getLevelCode())) {
                //                    throw new BusinessException("400", "终端等级为空");
                //                }
                //                if (Objects.isNull(item.getProductProtocolConfigId())) {
                //                    throw new BusinessException("400", "协议产品配置为空");
                //                }
                //            }
                //        });
                //    }
                //}
            }
        }

    }

    /**
     * 校验查重接口 判断手机号和终端店名是否重复
     */
    public void verifyExist(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq) {

        if (Optional.ofNullable(addTerminalShopInfoScheduleReq.getShopType())
                .map(shopType -> terminalShopService.isHotelTerminalByShopType(shopType))
                .orElse(false) ||
                Optional.ofNullable(addTerminalShopInfoScheduleReq.getTerminalShopId())
                        .map(terminalShopCommonService::isHotelTerminalByTerminalShopId)
                        .orElse(false)) {
            //需要验证负责人手机号是不是已经注册过合伙人之类的号
            if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getLeaderPhone())) {
                if (shopUserDao.selectAccountStatusByPhone(addTerminalShopInfoScheduleReq.getLeaderPhone()) > 0) {
                    throw new BusinessException("400", "该负责人的手机号已经注册过除终端以外其他类型账户");
                }
            }
        } else {
            if (terminalShopDao.selectCount(new QueryWrapper<TerminalShopModel>()
                    .eq("shop_name", addTerminalShopInfoScheduleReq.getShopName())
                    .eq("is_delete", 0)
                    .ne("id", (addTerminalShopInfoScheduleReq.getTerminalShopId() == null ? 0 :
                            addTerminalShopInfoScheduleReq.getTerminalShopId()))) > 0) {
                throw new BusinessException("400", "该终端店名称已经存在,建议名称后增加街道信息");
            }

            if (terminalShopInfoScheduleDao.selectCount(new QueryWrapper<TerminalShopInfoScheduleModel>()
                    .eq("shop_name", addTerminalShopInfoScheduleReq.getShopName())
                    .eq("is_delete", 0)
                    .ne("terminal_shop_id", (addTerminalShopInfoScheduleReq.getTerminalShopId() == null ? 0 :
                            addTerminalShopInfoScheduleReq.getTerminalShopId()))) > 0) {
                throw new BusinessException("400", "该终端店名称已经存在,建议名称后增加街道信息");
            }

            //终端名称重复校验
            if (Objects.isNull(addTerminalShopInfoScheduleReq.getMemberShopId())) {
                if (shopDao.countByShopName(addTerminalShopInfoScheduleReq.getShopName()) > 0) {
                    throw new BusinessException("400", "联盟该终端店名称已经存在,建议名称后增加街道信息");
                }
            } else {
                if (shopDao.countByShopNameByShopId(addTerminalShopInfoScheduleReq.getShopName(), addTerminalShopInfoScheduleReq.getMemberShopId()) > 0) {
                    throw new BusinessException("400", "联盟该终端店名称已经存在,建议名称后增加街道信息");
                }
            }

            //需要验证负责人手机号是不是已经注册过合伙人之类的号
            if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getLeaderPhone())) {
                if (shopUserDao.selectAccountStatusByPhone(addTerminalShopInfoScheduleReq.getLeaderPhone()) > 0) {
                    throw new BusinessException("400", "该负责人的手机号已经注册过除终端以外其他类型账户");
                }
            }
        }

    }

    /**
     * @description: 校验营业执照是否存在且为真
     * @param: [addTerminalShopInfoScheduleReq]
     * @return: void
     **/
    @Override
    public void verifyLicense(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq) {
        if (addTerminalShopInfoScheduleReq.getWhetherLicense().equals(1)) {
            //判断营业执照是否存在
            if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getLicenseCode())) {
                throw new BusinessException("400", "需上传营业执照同时营业执照为真");
            }
            if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getLicenseImg())) {
                throw new BusinessException("400", "需上传营业执照同时营业执照为真");
            }
            // 当前营业执照是否为真
            if (addTerminalShopInfoScheduleReq.getIsLicenseSuccess().equals(0)) {
                throw new BusinessException("400", "需上传营业执照同时营业执照为真");
            }
        } else if (addTerminalShopInfoScheduleReq.getWhetherLicense().equals(0)) {
            throw new BusinessException("400", "需上传营业执照同时营业执照为真");
        }
    }

    public void verifyLicenseCodeAndShopType(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq) {
        List<TerminalShopModel> list = terminalShopDao.selectHotelTerminalExistListByShopType(addTerminalShopInfoScheduleReq.getTerminalShopId(), addTerminalShopInfoScheduleReq.getLicenseCode(), addTerminalShopInfoScheduleReq.getShopType());
        if (list != null && list.size() > 0) {
            // 取出list中的shopType，根据TerminalShopTypeEnum枚举对应的值，拼接成字符串
            String shopTypeStr = list.stream().map(TerminalShopModel::getShopType).map(shopType -> TerminalShopTypeEnum.getNameByType(shopType)).collect(Collectors.joining("，"));
            throw new BusinessException("400", "该营业执照已存在终端类型【" + shopTypeStr + "】");
        }
    }

    public void verifyLisenceAndContractCode(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq, String reqName) {
        if (reqName.equals("Hotel")) {
            List<TerminalShopModel> list = terminalShopDao.selectHotelTerminalExistListByContractCode(addTerminalShopInfoScheduleReq.getTerminalShopId(), addTerminalShopInfoScheduleReq.getLicenseCode(), addTerminalShopInfoScheduleReq.getPrimaryContract().getContractCode());
            if (list != null && list.size() > 0) {
                throw new BusinessException("400", "该营业执照下已有激活或审核中的终端绑定该合同编号");
            }
        } else {
            List<TerminalShopModel> list = terminalShopDao.selectNotHotelTerminalExistListByContractCode(addTerminalShopInfoScheduleReq.getTerminalShopId(), addTerminalShopInfoScheduleReq.getLicenseCode(), addTerminalShopInfoScheduleReq.getPrimaryContract().getContractCode());
            if (list != null && list.size() > 0) {
                throw new BusinessException("400", "该营业执照下已有激活或审核中的终端绑定该合同编号");
            }
        }

    }


    @Override
    public void verifyLisenceAndContractType(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq) {
        // 终端类型=烟酒终端/企业终端时，同一个营业执照已存在启用+审核中/激活+未合并的终端（含餐饮、非餐饮）时，只能创建一个相同终端类型，不同合同类型的终端
        List<TerminalShopResp> list = terminalShopDao.selectTerminalExistListByContractType(addTerminalShopInfoScheduleReq.getTerminalShopId(), addTerminalShopInfoScheduleReq.getLicenseCode(), addTerminalShopInfoScheduleReq.getPrimaryContract().getContractType());
        if (list != null && list.size() > 0) {
            if (Optional.ofNullable(addTerminalShopInfoScheduleReq.getShopType())
                    .map(shopType -> terminalShopService.isHotelTerminalByShopType(shopType))
                    .orElse(false) ||
                    Optional.ofNullable(addTerminalShopInfoScheduleReq.getTerminalShopId())
                            .map(terminalShopCommonService::isHotelTerminalByTerminalShopId)
                            .orElse(false)) {
                // 遍历list，如果list中有与addTerminalShopInfoScheduleReq中的终端类型不一致的终端，则抛出异常
                for (TerminalShopResp terminalShopModel : list) {
                    if (!terminalShopModel.getShopType().equals(addTerminalShopInfoScheduleReq.getShopType())) {
                        throw new BusinessException("400", "相同营业执照下终端类型不一致");
                    } else {
                        throw new BusinessException("400", "该营业执照下不能绑定相同的合同类型");
                    }
                }
            } else {
                // 遍历list，如果list中有与addTerminalShopInfoScheduleReq中的终端类型不一致的终端，则抛出异常
                for (TerminalShopResp terminalShopModel : list) {
                    if (!terminalShopModel.getShopType().equals(addTerminalShopInfoScheduleReq.getShopType())) {
                        throw new BusinessException("400", "相同营业执照下终端类型不一致");
                    }
//                    else {
//                        throw new BusinessException("400", "该营业执照下不能绑定相同的合同类型");
//                    }
                }
                // 筛选出list中status为1,terminalStatus为0的数据,取出终端名称,以逗号隔开
                String activatedAndEnabledTerminalNames = list.stream().filter(terminalShopModel -> terminalShopModel.getStatus().equals(1) && terminalShopModel.getTerminalStatus().equals(0)).map(TerminalShopResp::getShopName).collect(Collectors.joining(","));
                if (StringUtils.isNotBlank(activatedAndEnabledTerminalNames)) {
                    throw new BusinessException("400", "该营业执照下存在已激活启用【" + activatedAndEnabledTerminalNames + "】终端");
                }
                // 筛选出list中status为2,terminalStatus为0或为空的数据,取出终端名称,以逗号隔开
                String activatingAndEnabledTerminalNames = list.stream().filter(terminalShopModel -> terminalShopModel.getStatus().equals(2) && (terminalShopModel.getTerminalStatus().equals(0) || terminalShopModel.getTerminalStatus() == null)).map(TerminalShopResp::getShopName).collect(Collectors.joining(","));
                if (StringUtils.isNotBlank(activatingAndEnabledTerminalNames)) {
                    throw new BusinessException("400", "该营业执照下存在审核中【" + activatingAndEnabledTerminalNames + "】终端");
                }
                // 筛选出list中status为1,terminalStatus为1的数据,取出终端名称,以逗号隔开
                String activatingAndDisabledTerminalNames = list.stream().filter(terminalShopModel -> terminalShopModel.getStatus().equals(1) && terminalShopModel.getTerminalStatus().equals(1)).map(TerminalShopResp::getShopName).collect(Collectors.joining(","));
                if (StringUtils.isNotBlank(activatingAndDisabledTerminalNames)) {
                    throw new BusinessException("400", "该营业执照下存在已激活禁用【" + activatingAndDisabledTerminalNames + "】终端,请启用原终端");
                }
                throw new BusinessException("400", "该营业执照下不能绑定相同的合同类型");
            }

        }
//        List<ContractTypeCategoryDetailModel> contractTypelist = terminalShopContractDao.selectContractExistListByContractType(addTerminalShopInfoScheduleReq.getPrimaryContract().getContractType());
//        if (contractTypelist != null && contractTypelist.size() > 0) {
//            // 根据contractTypelist获取对应的合同类型list
//            List<Integer> contractTypes = contractTypelist.stream().map(ContractTypeCategoryDetailModel::getContractType).collect(Collectors.toList());
//            List<TerminalShopModel> list = terminalShopContractDao.selectContractsByLicenseCode(addTerminalShopInfoScheduleReq.getLicenseCode(), contractTypes);
//            if (list != null && list.size() > 0) {
//                throw new BusinessException("400", "该营业执照已存在该合同类型");
//            }
//        }
    }

    @Override
    public String handlePhysicalCode(TerminalShopModel terminalShopModel) {
        String physicalCode = null;
        // 根据营业执照号,终端类型查询t_terminal_shop_physical_code_relation表中的数据
        // 一 根据license_code和shop_type查t_terminal_shop_physical_code_relation表list
        // 二 根据查出的list中的deputyCode查询t_terminal_shop对应的list,筛选出未合并/主店的终端列表
        // 三 如果list为空，生成一个新的physical_code,如果不为空,取出t_terminal_shop对应终端的数据
        // 四 根据t_terminal_shop对应终端的数据的deputyCode查询第一条注释中list对应的第一条数据,取出t_terminal_shop_physical_code_relation表中对应的physicalCode
        List<TerminalShopPhysicalCodeRelationModel> terminalShopPhysicalCodeRelationModelList = terminalShopPhysicalCodeRelationDao.selectList(new QueryWrapper<TerminalShopPhysicalCodeRelationModel>()
                .eq("license_code", terminalShopModel.getLicenseCode())
                .eq("shop_type", terminalShopModel.getShopType())
                .eq("is_delete", 0));
        if(CollectionUtils.isNotEmpty(terminalShopPhysicalCodeRelationModelList)){
            // 根据查出的terminalShopPhysicalCodeRelationModelList中的deputyCode查询t_terminal_shop对应的list,筛选出未合并/主店的终端列表
            List<String> deputyCodes = terminalShopPhysicalCodeRelationModelList.stream()
                    .map(TerminalShopPhysicalCodeRelationModel::getDeputyCode)
                    .collect(Collectors.toList());
            QueryWrapper<TerminalShopModel> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("deputy_code", deputyCodes);
            queryWrapper.eq("is_delete", 0);
            queryWrapper.in("merge_type", 0, 1);
            List<TerminalShopModel> terminalShopModelList = terminalShopDao.selectList(queryWrapper);
            // 如果list为空，生成一个新的physical_code,如果不为空,取出t_terminal_shop对应终端的数据
            if(CollectionUtils.isNotEmpty(terminalShopModelList)){
                // 根据t_terminal_shop对应终端的数据的deputyCode查询第一条注释中list对应的第一条数据,取出t_terminal_shop_physical_code_relation表中对应的physicalCode
                String deputyCode = terminalShopModelList.get(0).getDeputyCode();
                TerminalShopPhysicalCodeRelationModel terminalShopPhysicalCodeRelationModel = terminalShopPhysicalCodeRelationModelList.stream()
                                .filter(item -> item.getDeputyCode().equals(deputyCode))
                                .collect(Collectors.toList()).get(0);
                physicalCode = terminalShopPhysicalCodeRelationModel.getPhysicalCode();
            }else{
                // 生成一个新的physical_code
                String physicalCodeNew = terminalShopService.makeSamePhysicalTerminalCode();
                physicalCode = insertTerminalShopPhysicalCode(physicalCodeNew);
            }
        } else {
            // 生成一个新的physical_code
            String physicalCodeNew = terminalShopService.makeSamePhysicalTerminalCode();
            physicalCode = insertTerminalShopPhysicalCode(physicalCodeNew);

        }
        return physicalCode;
    };
    private String insertTerminalShopPhysicalCode(String physicalCode) {
        LocalDateTime nowTime = LocalDateTime.now();
        TerminalShopPhysicalCodeModel terminalShopPhysicalCodeModel = new TerminalShopPhysicalCodeModel();
        terminalShopPhysicalCodeModel.setCreateUser(userContext.getTerminalModel().getId());
        terminalShopPhysicalCodeModel.setCreateTime(nowTime);
        terminalShopPhysicalCodeModel.setPhysicalCode(physicalCode);
        terminalShopPhysicalCodeModel.setIsDelete(0);
        terminalShopPhysicalCodeModel.setUpdateUser(userContext.getTerminalModel().getId());
        terminalShopPhysicalCodeModel.setUpdateTime(nowTime);
        terminalShopPhysicalCodeDao.insert(terminalShopPhysicalCodeModel);
        return terminalShopPhysicalCodeModel.getPhysicalCode();
    }

    @Override
    public void setSamePhysicalCode(TerminalShopModel terminalShopModel, Integer isInsertOrUpdate) {
        if (terminalShopModel.getShopType().equals(TerminalShopTypeEnum.channel_terminal.getType())
                || terminalShopModel.getShopType().equals(TerminalShopTypeEnum.enterprise_terminal.getType())
                || terminalShopModel.getShopType().equals(TerminalShopTypeEnum.CHAIN_CATERING_HOTEL_TERMINAL.getType())
                || terminalShopModel.getShopType().equals(TerminalShopTypeEnum.FEATURED_CATERING_HOTEL_TERMINAL.getType())
                || terminalShopModel.getShopType().equals(TerminalShopTypeEnum.BUSINESS_CATERING_HOTEL_TERMINAL.getType())
                || terminalShopModel.getShopType().equals(TerminalShopTypeEnum.BANQUET_CATERING_HOTEL_TERMINAL.getType())
        ) {
            // 将该营业执照对应的，该营业执照是否存在【已激活】即审核通过,未合并/主店的终端查询出来
            // 使用该终端的实体，处理物理终端编码
            String physicalCode = handlePhysicalCode(terminalShopModel);
            TerminalShopPhysicalCodeRelationModel terminalShopPhysicalCodeRelationModel = new TerminalShopPhysicalCodeRelationModel();
            terminalShopPhysicalCodeRelationModel.setCreateUser(userContext.getTerminalModel().getId());
            terminalShopPhysicalCodeRelationModel.setLicenseCode(terminalShopModel.getLicenseCode());
            terminalShopPhysicalCodeRelationModel.setPhysicalCode(physicalCode);
            terminalShopPhysicalCodeRelationModel.setDeputyCode(terminalShopModel.getDeputyCode());
            terminalShopPhysicalCodeRelationModel.setIsDelete(0);
            terminalShopPhysicalCodeRelationModel.setUpdateUser(userContext.getTerminalModel().getId());
            terminalShopPhysicalCodeRelationModel.setShopType(terminalShopModel.getShopType());
            terminalShopPhysicalCodeRelationService.insertTerminalShopPhysicalCodeRelation(terminalShopPhysicalCodeRelationModel);
            if(isInsertOrUpdate.equals(1)) {
                // 如果是主店
                if(ObjectUtil.isNotEmpty(terminalShopModel.getMergeType()) && terminalShopModel.getMergeType().equals(1)) {
                    List<TerminalShopCommonModel> branchTerminalShopList = terminalShopCommonDao.selectList(new LambdaQueryWrapper<TerminalShopCommonModel>()
                                    .eq(TerminalShopCommonModel::getMergeId, terminalShopModel.getId())
                            //.eq(TerminalShopCommonModel::getIsDelete, DeleteFlagEnum.NOT_DELETE.getKey())
                    );
                // 遍历branchTerminalShopList，将branchTerminalShopList中的终端的物理终端编码设置为主店的物理终端编码
                    for (TerminalShopCommonModel branchTerminalShop : branchTerminalShopList) {
                        TerminalShopPhysicalCodeRelationModel branchTerminalShopPhysicalCodeRelationModel = new TerminalShopPhysicalCodeRelationModel();
                        branchTerminalShopPhysicalCodeRelationModel.setCreateUser(userContext.getTerminalModel().getId());
                        branchTerminalShopPhysicalCodeRelationModel.setLicenseCode(branchTerminalShop.getLicenseCode());
                        branchTerminalShopPhysicalCodeRelationModel.setPhysicalCode(physicalCode);
                        branchTerminalShopPhysicalCodeRelationModel.setDeputyCode(branchTerminalShop.getDeputyCode());
                        branchTerminalShopPhysicalCodeRelationModel.setIsDelete(0);
                        branchTerminalShopPhysicalCodeRelationModel.setUpdateUser(userContext.getTerminalModel().getId());
                        branchTerminalShopPhysicalCodeRelationModel.setShopType(branchTerminalShop.getShopType());
                        terminalShopPhysicalCodeRelationService.insertTerminalShopPhysicalCodeRelation(branchTerminalShopPhysicalCodeRelationModel);
                    }
                }
            }
        }
    }

    public void verifyLisenceAndContractTypeByHotel(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq) {
        List<TerminalShopResp> list = terminalShopDao.selectTerminalExistListByContractTypeByHotel(addTerminalShopInfoScheduleReq.getTerminalShopId(), addTerminalShopInfoScheduleReq.getLicenseCode(), addTerminalShopInfoScheduleReq.getPrimaryContract().getContractType());
        if (list != null && list.size() > 0) {
            // 遍历list，如果list中有与addTerminalShopInfoScheduleReq中的终端类型不一致的终端，则抛出异常
            for (TerminalShopResp terminalShopModel : list) {
                if (!terminalShopModel.getShopType().equals(addTerminalShopInfoScheduleReq.getShopType())) {
                    throw new BusinessException("400", "相同营业执照下终端类型不一致");
                }
//                else {
//                    throw new BusinessException("400", "该营业执照下不能绑定相同的合同类型");
//                }
            }
            // 筛选出list中status为1,terminalStatus为0的数据,取出终端名称,以逗号隔开
            String activatedAndEnabledTerminalNames = list.stream().filter(terminalShopModel -> terminalShopModel.getStatus().equals(1) && terminalShopModel.getTerminalStatus().equals(0)).map(TerminalShopResp::getShopName).collect(Collectors.joining(","));
            if (StringUtils.isNotBlank(activatedAndEnabledTerminalNames)) {
                throw new BusinessException("400", "该营业执照下存在已激活启用【" + activatedAndEnabledTerminalNames + "】终端");
            }
            // 筛选出list中status为2,terminalStatus为0或为空的数据,取出终端名称,以逗号隔开
            String activatingAndEnabledTerminalNames = list.stream().filter(terminalShopModel -> terminalShopModel.getStatus().equals(2) && (terminalShopModel.getTerminalStatus().equals(0) || terminalShopModel.getTerminalStatus() == null)).map(TerminalShopResp::getShopName).collect(Collectors.joining(","));
            if (StringUtils.isNotBlank(activatingAndEnabledTerminalNames)) {
                throw new BusinessException("400", "该营业执照下存在审核中【" + activatingAndEnabledTerminalNames + "】终端");
            }
            // 筛选出list中status为1,terminalStatus为1的数据,取出终端名称,以逗号隔开
            String activatingAndDisabledTerminalNames = list.stream().filter(terminalShopModel -> terminalShopModel.getStatus().equals(1) && terminalShopModel.getTerminalStatus().equals(1)).map(TerminalShopResp::getShopName).collect(Collectors.joining(","));
            if (StringUtils.isNotBlank(activatingAndDisabledTerminalNames)) {
                throw new BusinessException("400", "该营业执照下存在已激活禁用【" + activatingAndDisabledTerminalNames + "】终端,请启用原终端");
            }

            throw new BusinessException("400", "该营业执照下不能绑定相同的合同类型");
        }
    }

    public void verifyLisenceAndDealerCodeByHotel(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq) {
        List<TerminalShopResp> list = terminalShopDao.selectTerminalExistListByDealerCodeAndHotel(addTerminalShopInfoScheduleReq.getTerminalShopId(), addTerminalShopInfoScheduleReq.getLicenseCode(), addTerminalShopInfoScheduleReq.getPrimaryContract().getDealerCode());
        if (list != null && list.size() > 0) {
            // 遍历list，如果list中有与addTerminalShopInfoScheduleReq中的终端类型不一致的终端，则抛出异常
            for (TerminalShopResp terminalShopModel : list) {
                if (!terminalShopModel.getShopType().equals(addTerminalShopInfoScheduleReq.getShopType())) {
                    throw new BusinessException("400", "相同营业执照下终端类型不一致");
                }
//                else {
//                    throw new BusinessException("400", "该营业执照下不能绑定相同的合同类型");
//                }
            }
            // 筛选出list中status为1,terminalStatus为0的数据,取出终端名称,以逗号隔开
            String activatedAndEnabledTerminalNames = list.stream().filter(terminalShopModel -> terminalShopModel.getStatus().equals(1) && terminalShopModel.getTerminalStatus().equals(0)).map(TerminalShopResp::getShopName).collect(Collectors.joining(","));
            if (StringUtils.isNotBlank(activatedAndEnabledTerminalNames)) {
                throw new BusinessException("400", "该营业执照下存在已激活启用【" + activatedAndEnabledTerminalNames + "】终端");
            }
            // 筛选出list中status为2,terminalStatus为0或为空的数据,取出终端名称,以逗号隔开
            String activatingAndEnabledTerminalNames = list.stream().filter(terminalShopModel -> terminalShopModel.getStatus().equals(2) && (terminalShopModel.getTerminalStatus().equals(0) || terminalShopModel.getTerminalStatus() == null)).map(TerminalShopResp::getShopName).collect(Collectors.joining(","));
            if (StringUtils.isNotBlank(activatingAndEnabledTerminalNames)) {
                throw new BusinessException("400", "该营业执照下存在审核中【" + activatingAndEnabledTerminalNames + "】终端");
            }
            // 筛选出list中status为1,terminalStatus为1的数据,取出终端名称,以逗号隔开
            String activatingAndDisabledTerminalNames = list.stream().filter(terminalShopModel -> terminalShopModel.getStatus().equals(1) && terminalShopModel.getTerminalStatus().equals(1)).map(TerminalShopResp::getShopName).collect(Collectors.joining(","));
            if (StringUtils.isNotBlank(activatingAndDisabledTerminalNames)) {
                throw new BusinessException("400", "该营业执照下存在已激活禁用【" + activatingAndDisabledTerminalNames + "】终端,请启用原终端");
            }

            throw new BusinessException("400", "该营业执照下不能绑定相同的经销商");
        }
    }

    public void verifyLisenceAndDealerCode(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq) {
        List<TerminalShopResp> list = terminalShopDao.selectTerminalExistListByDealerCode(addTerminalShopInfoScheduleReq.getTerminalShopId(), addTerminalShopInfoScheduleReq.getLicenseCode(), addTerminalShopInfoScheduleReq.getPrimaryContract().getDealerCode());
        if (list != null && list.size() > 0) {
            // 遍历list，如果list中有与addTerminalShopInfoScheduleReq中的终端类型不一致的终端，则抛出异常
            for (TerminalShopResp terminalShopModel : list) {
                if (!terminalShopModel.getShopType().equals(addTerminalShopInfoScheduleReq.getShopType())) {
                    throw new BusinessException("400", "相同营业执照下终端类型不一致");
                }
//                else {
//                    throw new BusinessException("400", "该营业执照下不能绑定相同的合同类型");
//                }
            }
            // 筛选出list中status为1,terminalStatus为0的数据,取出终端名称,以逗号隔开
            String activatedAndEnabledTerminalNames = list.stream().filter(terminalShopModel -> terminalShopModel.getStatus().equals(1) && terminalShopModel.getTerminalStatus().equals(0)).map(TerminalShopResp::getShopName).collect(Collectors.joining(","));
            if (StringUtils.isNotBlank(activatedAndEnabledTerminalNames)) {
                throw new BusinessException("400", "该营业执照下存在已激活启用【" + activatedAndEnabledTerminalNames + "】终端");
            }
            // 筛选出list中status为2,terminalStatus为0或为空的数据,取出终端名称,以逗号隔开
            String activatingAndEnabledTerminalNames = list.stream().filter(terminalShopModel -> terminalShopModel.getStatus().equals(2) && (terminalShopModel.getTerminalStatus().equals(0) || terminalShopModel.getTerminalStatus() == null)).map(TerminalShopResp::getShopName).collect(Collectors.joining(","));
            if (StringUtils.isNotBlank(activatingAndEnabledTerminalNames)) {
                throw new BusinessException("400", "该营业执照下存在审核中【" + activatingAndEnabledTerminalNames + "】终端");
            }
            // 筛选出list中status为1,terminalStatus为1的数据,取出终端名称,以逗号隔开
            String activatingAndDisabledTerminalNames = list.stream().filter(terminalShopModel -> terminalShopModel.getStatus().equals(1) && terminalShopModel.getTerminalStatus().equals(1)).map(TerminalShopResp::getShopName).collect(Collectors.joining(","));
            if (StringUtils.isNotBlank(activatingAndDisabledTerminalNames)) {
                throw new BusinessException("400", "该营业执照下存在已激活禁用【" + activatingAndDisabledTerminalNames + "】终端,请启用原终端");
            }

            throw new BusinessException("400", "该营业执照下不能绑定相同的经销商");
        }
    }

    public void verifyLisenceByHotelTerminal(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq) {
        List<TerminalShopModel> list = terminalShopDao.selectHotelTerminalExistByLicense(addTerminalShopInfoScheduleReq.getTerminalShopId(), addTerminalShopInfoScheduleReq.getLicenseCode());
        if (list != null && list.size() > 0) {
            throw new BusinessException("400", "该营业执照已存在餐饮渠道终端");
        }
    }
    public void verifyExistTerminalShopByLisence(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq, String tag) {
        if (tag.equals("notHotel")) {
            List<TerminalShopModel> list = terminalShopDao.selectNotHotelTerminalExistByLicense(addTerminalShopInfoScheduleReq.getTerminalShopId(), addTerminalShopInfoScheduleReq.getLicenseCode(), addTerminalShopInfoScheduleReq.getShopType());
            if (list != null && list.size() > 0) {
//                throw new BusinessException("400", "相同营业执照下终端类型不一致");
                // 取出list中终端类型,以逗号隔开
                String shopTypeStr = list.stream().map(TerminalShopModel::getShopType).map(shopType -> TerminalShopTypeEnum.getNameByType(shopType)).collect(Collectors.joining("，"));
                throw new BusinessException("400", "该营业执照已存在【" + shopTypeStr + "】");
            }
        } else {
            List<TerminalShopModel> list = terminalShopDao.selectIsHotelTerminalExistByLicense(addTerminalShopInfoScheduleReq.getTerminalShopId(), addTerminalShopInfoScheduleReq.getLicenseCode(), addTerminalShopInfoScheduleReq.getShopType());
            if (list != null && list.size() > 0) {
//                throw new BusinessException("400", "相同营业执照下终端类型不一致");
                // 取出list中终端类型,以逗号隔开
                String shopTypeStr = list.stream().map(TerminalShopModel::getShopType).map(shopType -> TerminalShopTypeEnum.getNameByType(shopType)).collect(Collectors.joining("，"));
                throw new BusinessException("400", "该营业执照已存在【" + shopTypeStr + "】");
            }
        }

    }

    /**
     * @Description: 处理主副编码   副编码是唯一的并提供其他系统使用
     */
    public void disposeMainCodeAndDeputyCode(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq) {


        if (StringUtils.isBlank(addTerminalShopInfoScheduleReq.getLicenseCode())) {
            //如果为空 说明没有营业执照 不去数据库查询
            //副编码生成
            String deputyCode = makeTerminalCode();
            //赋值主编码
            //转换实体类
            addTerminalShopInfoScheduleReq.setMainCode(deputyCode);
            addTerminalShopInfoScheduleReq.setDeputyCode(deputyCode);
            return;
        }

        //查询实体类
        TerminalShopModel selectTerminalData = terminalShopDao.selectById(addTerminalShopInfoScheduleReq.getTerminalShopId());

        //如果营业执照为空 不允许生成编码
        if (Objects.nonNull(selectTerminalData)) {
            //看看主编码或者副编码是否为空
            if (StringUtils.isBlank(selectTerminalData.getMainCode()) || StringUtils.isBlank(selectTerminalData.getDeputyCode())) {
                //根据营业执照编号去查询有没有这个主编码
                String mainCode = shopDao.selectTerminalCodeByLicenseCode(addTerminalShopInfoScheduleReq.getLicenseCode());

                //副编码生成
                String deputyCode = makeTerminalCode();

                //如果主编码为空 说明营业执照没有被用过 用副编码
                if (StringUtils.isBlank(mainCode)) {
                    mainCode = deputyCode;
                }
                //转换实体类
                addTerminalShopInfoScheduleReq.setMainCode(mainCode);
                addTerminalShopInfoScheduleReq.setDeputyCode(deputyCode);
            } else {
                //不等于空就用已经生成的
                addTerminalShopInfoScheduleReq.setMainCode(selectTerminalData.getMainCode());
                addTerminalShopInfoScheduleReq.setDeputyCode(selectTerminalData.getDeputyCode());
            }
        } else {
            String deputyCode = makeTerminalCode();
            addTerminalShopInfoScheduleReq.setMainCode(deputyCode);
            addTerminalShopInfoScheduleReq.setDeputyCode(deputyCode);
        }
    }


    /**
     * 获取不重复的终端编号
     */
    private String makeTerminalCode() {
        while (true) {
            String terminalCode = RandomUtils.getTerminalCode();
            if (terminalShopDao.checkTerminalCode(terminalCode) == 0 && terminalShopDao.checkTerminalGtCode(terminalCode) == 0) {
                return terminalCode;
            }
        }
    }

    private TerminalShopInfoScheduleModel getScheduleModel(TerminalShopInfoScheduleModel oldScheduleModel, AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq) {
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getTerminalShopId())) {
            oldScheduleModel.setTerminalShopId(addTerminalShopInfoScheduleReq.getTerminalShopId());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getShopName())) {
            oldScheduleModel.setShopName(addTerminalShopInfoScheduleReq.getShopName());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getLeaderName())) {
            oldScheduleModel.setLeaderName(addTerminalShopInfoScheduleReq.getLeaderName());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getLeaderPhone())) {
            oldScheduleModel.setLeaderPhone(addTerminalShopInfoScheduleReq.getLeaderPhone());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getTag())) {
            oldScheduleModel.setTag(addTerminalShopInfoScheduleReq.getTag());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getHeadImg())) {
            oldScheduleModel.setHeadImg(addTerminalShopInfoScheduleReq.getHeadImg());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getRemark())) {
            oldScheduleModel.setRemark(addTerminalShopInfoScheduleReq.getRemark());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getProvince())) {
            oldScheduleModel.setProvince(addTerminalShopInfoScheduleReq.getProvince());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getCity())) {
            oldScheduleModel.setCity(addTerminalShopInfoScheduleReq.getCity());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getDistrict())) {
            oldScheduleModel.setDistrict(addTerminalShopInfoScheduleReq.getDistrict());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getAddress())) {
            oldScheduleModel.setAddress(addTerminalShopInfoScheduleReq.getAddress());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getLongitude())) {
            oldScheduleModel.setLongitude(addTerminalShopInfoScheduleReq.getLongitude());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getLatitude())) {
            oldScheduleModel.setLatitude(addTerminalShopInfoScheduleReq.getLatitude());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getType())) {
            oldScheduleModel.setType(addTerminalShopInfoScheduleReq.getType());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getIsImage())) {
            oldScheduleModel.setIsImage(addTerminalShopInfoScheduleReq.getIsImage());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getImageHeadPicture())) {
            oldScheduleModel.setImageHeadPicture(addTerminalShopInfoScheduleReq.getImageHeadPicture());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getKeeperName())) {
            oldScheduleModel.setKeeperName(addTerminalShopInfoScheduleReq.getKeeperName());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getKeeperPhone())) {
            oldScheduleModel.setKeeperPhone(addTerminalShopInfoScheduleReq.getKeeperPhone());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getShopArea())) {
            oldScheduleModel.setShopArea(addTerminalShopInfoScheduleReq.getShopArea());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getWhetherLicense())) {
            oldScheduleModel.setWhetherLicense(addTerminalShopInfoScheduleReq.getWhetherLicense());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getLicenseImg())) {
            oldScheduleModel.setLicenseImg(addTerminalShopInfoScheduleReq.getLicenseImg());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getLicenseCode())) {
            oldScheduleModel.setLicenseCode(addTerminalShopInfoScheduleReq.getLicenseCode());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getStatus())) {
            oldScheduleModel.setStatus(addTerminalShopInfoScheduleReq.getStatus());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getIsDelete())) {
            oldScheduleModel.setIsDelete(addTerminalShopInfoScheduleReq.getIsDelete());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getCreateTime())) {
            oldScheduleModel.setCreateTime(addTerminalShopInfoScheduleReq.getCreateTime());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getCreateUser())) {
            oldScheduleModel.setCreateUser(addTerminalShopInfoScheduleReq.getCreateUser());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getUpdateTime())) {
            oldScheduleModel.setUpdateTime(addTerminalShopInfoScheduleReq.getUpdateTime());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getCompanyId())) {
            oldScheduleModel.setCompanyId(addTerminalShopInfoScheduleReq.getCompanyId());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getMemberShopId())) {
            oldScheduleModel.setMemberShopId(addTerminalShopInfoScheduleReq.getMemberShopId());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getMainCode())) {
            oldScheduleModel.setMainCode(addTerminalShopInfoScheduleReq.getMainCode());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getLicenseCode())) {
            oldScheduleModel.setLicenseCode(addTerminalShopInfoScheduleReq.getLicenseCode());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getDeputyCode())) {
            oldScheduleModel.setDeputyCode(addTerminalShopInfoScheduleReq.getDeputyCode());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getTerminalDivideId())) {
            oldScheduleModel.setTerminalDivideId(addTerminalShopInfoScheduleReq.getTerminalDivideId());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getShopType())) {
            oldScheduleModel.setShopType(addTerminalShopInfoScheduleReq.getShopType());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getEnterpriseName())) {
            oldScheduleModel.setEnterpriseName(addTerminalShopInfoScheduleReq.getEnterpriseName());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getFoodBusinessLicense())) {
            oldScheduleModel.setFoodBusinessLicense(addTerminalShopInfoScheduleReq.getFoodBusinessLicense());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getWhetherProprietaryTrading())) {
            oldScheduleModel.setWhetherProprietaryTrading(addTerminalShopInfoScheduleReq.getWhetherProprietaryTrading());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getReceivingWarehouseProvince())) {
            oldScheduleModel.setReceivingWarehouseProvince(addTerminalShopInfoScheduleReq.getReceivingWarehouseProvince());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getReceivingWarehouseCity())) {
            oldScheduleModel.setReceivingWarehouseCity(addTerminalShopInfoScheduleReq.getReceivingWarehouseCity());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getReceivingWarehouseDistrict())) {
            oldScheduleModel.setReceivingWarehouseDistrict(addTerminalShopInfoScheduleReq.getReceivingWarehouseDistrict());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getReceivingWarehouseAddress())) {
            oldScheduleModel.setReceivingWarehouseAddress(addTerminalShopInfoScheduleReq.getReceivingWarehouseAddress());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getLevelCode())) {
            oldScheduleModel.setLevelCode(addTerminalShopInfoScheduleReq.getLevelCode());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getContactName())) {
            oldScheduleModel.setContactName(addTerminalShopInfoScheduleReq.getContactName());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getContactLevel())) {
            oldScheduleModel.setContactLevel(addTerminalShopInfoScheduleReq.getContactLevel());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getContactPhone())) {
            oldScheduleModel.setContactPhone(addTerminalShopInfoScheduleReq.getContactPhone());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getReceivingPaymentType())) {
            oldScheduleModel.setReceivingPaymentType(addTerminalShopInfoScheduleReq.getReceivingPaymentType());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getReceivingPaymentName())) {
            oldScheduleModel.setReceivingPaymentName(addTerminalShopInfoScheduleReq.getReceivingPaymentName());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getReceivingPaymentAccount())) {
            oldScheduleModel.setReceivingPaymentAccount(addTerminalShopInfoScheduleReq.getReceivingPaymentAccount());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getReceivingPaymentBank())) {
            oldScheduleModel.setReceivingPaymentBank(addTerminalShopInfoScheduleReq.getReceivingPaymentBank());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getReceivingPaymentAccountPicture())) {
            oldScheduleModel.setReceivingPaymentAccountPicture(addTerminalShopInfoScheduleReq.getReceivingPaymentAccountPicture());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getAuditUserId())) {
            oldScheduleModel.setAuditUserId(addTerminalShopInfoScheduleReq.getAuditUserId());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getAuditResult())) {
            oldScheduleModel.setAuditResult(addTerminalShopInfoScheduleReq.getAuditResult());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getGtAuditResult())) {
            oldScheduleModel.setGtAuditResult(addTerminalShopInfoScheduleReq.getGtAuditResult());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getShopAgreement())) {
            oldScheduleModel.setShopAgreement(addTerminalShopInfoScheduleReq.getShopArea());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getGender())) {
            oldScheduleModel.setGender(addTerminalShopInfoScheduleReq.getGender());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getBirthday())) {
            oldScheduleModel.setBirthday(addTerminalShopInfoScheduleReq.getBirthday());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getAge())) {
            oldScheduleModel.setAge(addTerminalShopInfoScheduleReq.getAge());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getWorkUnit())) {
            oldScheduleModel.setWorkUnit(addTerminalShopInfoScheduleReq.getWorkUnit());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getPosition())) {
            oldScheduleModel.setPosition(addTerminalShopInfoScheduleReq.getPosition());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getPersonalPreference())) {
            oldScheduleModel.setPersonalPreference(addTerminalShopInfoScheduleReq.getPersonalPreference());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getDealerContactType())) {
            oldScheduleModel.setDealerContactType(addTerminalShopInfoScheduleReq.getDealerContactType());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getFirstBuyGoodsName())) {
            oldScheduleModel.setFirstBuyGoodsName(addTerminalShopInfoScheduleReq.getFirstBuyGoodsName());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getFirstBuyGoodsNumber())) {
            oldScheduleModel.setFirstBuyGoodsNumber(addTerminalShopInfoScheduleReq.getFirstBuyGoodsNumber());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getFirstScore())) {
            oldScheduleModel.setFirstScore(addTerminalShopInfoScheduleReq.getFirstScore());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getRegistrationFormPicture())) {
            oldScheduleModel.setRegistrationFormPicture(addTerminalShopInfoScheduleReq.getRegistrationFormPicture());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getNetworkPointEstablishReason())) {
            oldScheduleModel.setNetworkPointEstablishReason(addTerminalShopInfoScheduleReq.getNetworkPointEstablishReason());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getDistributorId())) {
            oldScheduleModel.setDistributorId(addTerminalShopInfoScheduleReq.getDistributorId());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getCopartnerId())) {
            oldScheduleModel.setCopartnerId(addTerminalShopInfoScheduleReq.getCopartnerId());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getCompanyPerson())) {
            oldScheduleModel.setCompanyPerson(addTerminalShopInfoScheduleReq.getCompanyPerson());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getCompanyName())) {
            oldScheduleModel.setCompanyName(addTerminalShopInfoScheduleReq.getCompanyName());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getLicenseMessage())) {
            oldScheduleModel.setLicenseMessage(addTerminalShopInfoScheduleReq.getLicenseMessage());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getLicenseResult())) {
            oldScheduleModel.setLicenseResult(addTerminalShopInfoScheduleReq.getLicenseResult());
        }
        return oldScheduleModel;
    }


    private TerminalShopModel getMainModel(TerminalShopModel oldMainModel, AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq) {
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getShopName())) {
            oldMainModel.setShopName(addTerminalShopInfoScheduleReq.getShopName());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getLeaderName())) {
            oldMainModel.setLeaderName(addTerminalShopInfoScheduleReq.getLeaderName());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getLeaderPhone())) {
            oldMainModel.setLeaderPhone(addTerminalShopInfoScheduleReq.getLeaderPhone());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getTag())) {
            oldMainModel.setTag(addTerminalShopInfoScheduleReq.getTag());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getHeadImg())) {
            oldMainModel.setHeadImg(addTerminalShopInfoScheduleReq.getHeadImg());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getRemark())) {
            oldMainModel.setRemark(addTerminalShopInfoScheduleReq.getRemark());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getProvince())) {
            oldMainModel.setProvince(addTerminalShopInfoScheduleReq.getProvince());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getCity())) {
            oldMainModel.setCity(addTerminalShopInfoScheduleReq.getCity());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getDistrict())) {
            oldMainModel.setDistrict(addTerminalShopInfoScheduleReq.getDistrict());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getAddress())) {
            oldMainModel.setAddress(addTerminalShopInfoScheduleReq.getAddress());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getLongitude())) {
            oldMainModel.setLongitude(addTerminalShopInfoScheduleReq.getLongitude());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getLatitude())) {
            oldMainModel.setLatitude(addTerminalShopInfoScheduleReq.getLatitude());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getType())) {
            oldMainModel.setType(addTerminalShopInfoScheduleReq.getType());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getIsImage())) {
            oldMainModel.setIsImage(addTerminalShopInfoScheduleReq.getIsImage());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getImageHeadPicture())) {
            oldMainModel.setImageHeadPicture(addTerminalShopInfoScheduleReq.getImageHeadPicture());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getKeeperName())) {
            oldMainModel.setKeeperName(addTerminalShopInfoScheduleReq.getKeeperName());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getKeeperPhone())) {
            oldMainModel.setKeeperPhone(addTerminalShopInfoScheduleReq.getKeeperPhone());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getShopArea())) {
            oldMainModel.setShopArea(addTerminalShopInfoScheduleReq.getShopArea());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getWhetherLicense())) {
            oldMainModel.setWhetherLicense(addTerminalShopInfoScheduleReq.getWhetherLicense());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getLicenseImg())) {
            oldMainModel.setLicenseImg(addTerminalShopInfoScheduleReq.getLicenseImg());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getLicenseCode())) {
            oldMainModel.setLicenseCode(addTerminalShopInfoScheduleReq.getLicenseCode());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getStatus())) {
            oldMainModel.setStatus(addTerminalShopInfoScheduleReq.getStatus());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getIsDelete())) {
            oldMainModel.setIsDelete(addTerminalShopInfoScheduleReq.getIsDelete());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getCreateTime())) {
            oldMainModel.setCreateTime(addTerminalShopInfoScheduleReq.getCreateTime());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getCreateUser())) {
            oldMainModel.setCreateUser(addTerminalShopInfoScheduleReq.getCreateUser());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getUpdateTime())) {
            oldMainModel.setUpdateTime(addTerminalShopInfoScheduleReq.getUpdateTime());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getCompanyId())) {
            oldMainModel.setCompanyId(addTerminalShopInfoScheduleReq.getCompanyId());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getMemberShopId())) {
            oldMainModel.setMemberShopId(addTerminalShopInfoScheduleReq.getMemberShopId());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getMainCode())) {
            oldMainModel.setMainCode(addTerminalShopInfoScheduleReq.getMainCode());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getLicenseCode())) {
            oldMainModel.setLicenseCode(addTerminalShopInfoScheduleReq.getLicenseCode());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getDeputyCode())) {
            oldMainModel.setDeputyCode(addTerminalShopInfoScheduleReq.getDeputyCode());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getTerminalDivideId())) {
            oldMainModel.setTerminalDivideId(addTerminalShopInfoScheduleReq.getTerminalDivideId());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getShopType())) {
            oldMainModel.setShopType(addTerminalShopInfoScheduleReq.getShopType());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getEnterpriseName())) {
            oldMainModel.setEnterpriseName(addTerminalShopInfoScheduleReq.getEnterpriseName());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getFoodBusinessLicense())) {
            oldMainModel.setFoodBusinessLicense(addTerminalShopInfoScheduleReq.getFoodBusinessLicense());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getWhetherProprietaryTrading())) {
            oldMainModel.setWhetherProprietaryTrading(addTerminalShopInfoScheduleReq.getWhetherProprietaryTrading());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getReceivingWarehouseProvince())) {
            oldMainModel.setReceivingWarehouseProvince(addTerminalShopInfoScheduleReq.getReceivingWarehouseProvince());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getReceivingWarehouseCity())) {
            oldMainModel.setReceivingWarehouseCity(addTerminalShopInfoScheduleReq.getReceivingWarehouseCity());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getReceivingWarehouseDistrict())) {
            oldMainModel.setReceivingWarehouseDistrict(addTerminalShopInfoScheduleReq.getReceivingWarehouseDistrict());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getReceivingWarehouseAddress())) {
            oldMainModel.setReceivingWarehouseAddress(addTerminalShopInfoScheduleReq.getReceivingWarehouseAddress());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getLevelCode())) {
            oldMainModel.setLevelCode(addTerminalShopInfoScheduleReq.getLevelCode());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getContactName())) {
            oldMainModel.setContactName(addTerminalShopInfoScheduleReq.getContactName());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getContactLevel())) {
            oldMainModel.setContactLevel(addTerminalShopInfoScheduleReq.getContactLevel());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getContactPhone())) {
            oldMainModel.setContactPhone(addTerminalShopInfoScheduleReq.getContactPhone());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getReceivingPaymentType())) {
            oldMainModel.setReceivingPaymentType(addTerminalShopInfoScheduleReq.getReceivingPaymentType());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getReceivingPaymentName())) {
            oldMainModel.setReceivingPaymentName(addTerminalShopInfoScheduleReq.getReceivingPaymentName());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getReceivingPaymentAccount())) {
            oldMainModel.setReceivingPaymentAccount(addTerminalShopInfoScheduleReq.getReceivingPaymentAccount());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getReceivingPaymentBank())) {
            oldMainModel.setReceivingPaymentBank(addTerminalShopInfoScheduleReq.getReceivingPaymentBank());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getReceivingPaymentAccountPicture())) {
            oldMainModel.setReceivingPaymentAccountPicture(addTerminalShopInfoScheduleReq.getReceivingPaymentAccountPicture());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getAuditUserId())) {
            oldMainModel.setAuditUserId(addTerminalShopInfoScheduleReq.getAuditUserId());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getAuditResult())) {
            oldMainModel.setAuditResult(addTerminalShopInfoScheduleReq.getAuditResult());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getGtAuditResult())) {
            oldMainModel.setGtAuditResult(addTerminalShopInfoScheduleReq.getGtAuditResult());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getShopAgreement())) {
            oldMainModel.setShopAgreement(addTerminalShopInfoScheduleReq.getShopArea());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getGender())) {
            oldMainModel.setGender(addTerminalShopInfoScheduleReq.getGender());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getBirthday())) {
            oldMainModel.setBirthday(addTerminalShopInfoScheduleReq.getBirthday());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getAge())) {
            oldMainModel.setAge(addTerminalShopInfoScheduleReq.getAge());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getWorkUnit())) {
            oldMainModel.setWorkUnit(addTerminalShopInfoScheduleReq.getWorkUnit());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getPosition())) {
            oldMainModel.setPosition(addTerminalShopInfoScheduleReq.getPosition());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getPersonalPreference())) {
            oldMainModel.setPersonalPreference(addTerminalShopInfoScheduleReq.getPersonalPreference());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getDealerContactType())) {
            oldMainModel.setDealerContactType(addTerminalShopInfoScheduleReq.getDealerContactType());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getFirstBuyGoodsName())) {
            oldMainModel.setFirstBuyGoodsName(addTerminalShopInfoScheduleReq.getFirstBuyGoodsName());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getFirstBuyGoodsNumber())) {
            oldMainModel.setFirstBuyGoodsNumber(addTerminalShopInfoScheduleReq.getFirstBuyGoodsNumber());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getFirstScore())) {
            oldMainModel.setFirstScore(addTerminalShopInfoScheduleReq.getFirstScore());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getRegistrationFormPicture())) {
            oldMainModel.setRegistrationFormPicture(addTerminalShopInfoScheduleReq.getRegistrationFormPicture());
        }
        if (StringUtils.isNotBlank(addTerminalShopInfoScheduleReq.getNetworkPointEstablishReason())) {
            oldMainModel.setNetworkPointEstablishReason(addTerminalShopInfoScheduleReq.getNetworkPointEstablishReason());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getDistributorId())) {
            oldMainModel.setDistributorId(addTerminalShopInfoScheduleReq.getDistributorId());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getCopartnerId())) {
            oldMainModel.setCopartnerId(addTerminalShopInfoScheduleReq.getCopartnerId());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getCompanyPerson())) {
            oldMainModel.setCompanyPerson(addTerminalShopInfoScheduleReq.getCompanyPerson());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getCompanyName())) {
            oldMainModel.setCompanyName(addTerminalShopInfoScheduleReq.getCompanyName());
        }
        if (Objects.nonNull(addTerminalShopInfoScheduleReq.getLicenseMessage())) {
            oldMainModel.setLicenseMessage(addTerminalShopInfoScheduleReq.getLicenseMessage());
        }
        return oldMainModel;
    }

    /**
     * @description: 新增协议
     * @author: MAX
     * @date: 2023/2/28 18:38
     * @param: [protocolList]
     * @return: void
     **/
    private List<TerminalProtocolModel> insertProtocol(List<TerminalProtocolReq> protocolList, TerminalShopModel terminalShopModel, TerminalShopContractReq shopContract) {
        //判断协议是否为空
        List<TerminalProtocolModel> rtnList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(protocolList)) {
            protocolList.forEach(protocol -> {
                if (Objects.nonNull(protocol.getProductProtocolConfigId())) {
                    //经销商编码
                    if (Objects.nonNull(shopContract) && StringUtils.isNotBlank(shopContract.getDealerCode())) {
                        protocol.setDealerCode(shopContract.getDealerCode());
                    }
                    //创建时间
                    protocol.setCreateTime(LocalDateTime.now());
                    //更新时间
                    protocol.setCreateUser(userContext.getTerminalModel().getId());
                    protocol.setCompanyId(terminalShopModel.getCompanyId());
                    //填入采集终端表的主键id
                    protocol.setTerminalShopId(terminalShopModel.getId());
                    //填入联盟终端表的主键id
                    if (Objects.nonNull(terminalShopModel.getMemberShopId())) {
                        protocol.setMemberShopId(terminalShopModel.getMemberShopId().longValue());
                    }
                    protocol.setSourceType(0);
                    Integer status = terminalShopModel.getStatus();
                    if (status == 1) {
                        protocol.setEndStatus(2);
                        protocol.setCheckStatus(1);
                    }

                    terminalProtocolDao.insert(protocol);
                    TerminalProtocolModel protocolModel = new TerminalProtocolModel();
                    BeanUtils.copyProperties(protocol, protocolModel);
                    rtnList.add(protocolModel);
                }
            });
        }
        return rtnList;
    }

    private void insertContract(TerminalShopContractReq shopContract, Integer shopId, Integer memberShopId) {
        //判断合同是否为空
        if (Objects.isNull(shopContract)) {
            return;
        }
        //合同编码不为空
        if (StringUtils.isNotBlank(shopContract.getContractCode())) {
            //把合同信息转换成添加用的类
            TerminalShopContractModel modelData = new TerminalShopContractModel();
            //合同类型
            modelData.setContractType(shopContract.getContractType());
            //经销商编码
            modelData.setDealerCode(shopContract.getDealerCode());
            //合同编码
            modelData.setContractCode(shopContract.getContractCode());
            //合同名称
            modelData.setContractCodeName(shopContract.getContractCodeName());
            //获取当前时间
            Date now = new Date();
            //创建时间
            modelData.setCreateTime(now);
            //更新时间
            modelData.setUpdateTime(now);
            //填入采集终端表的主键id
            modelData.setTerminalShopId(shopId);
            //填入联盟终端表的主键id
            if (Objects.nonNull(memberShopId)) {
                modelData.setMemberShopId(memberShopId);
            }
            //插入数据
            terminalShopContractDao.insert(modelData);
        }
    }

    /**
     * @author: HLQ
     * @Date: 2023/2/24 20:15
     * @Description: 新增修改记录表
     */
    private Integer insertDataLog(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq, TerminalShopModel mainModel, TerminalShopInfoScheduleModel scheduleModel, List<TerminalProtocolReq> protocolList, boolean isInsert) {
        TerminalShopLogResp terminalShopLogRespByShop = getTerminalShopLogRespByShop(addTerminalShopInfoScheduleReq);
        //查找主协议
        if (CollectionUtils.isNotEmpty(protocolList)) {
            TerminalProtocolReq terminalProtocolReq = protocolList.stream().filter(e -> 0 == e.getProtocolType()).findAny().orElse(null);
            TerminalProtocolLogResp terminalProtocolLogResp = null;
            if (!Objects.isNull(terminalProtocolReq)) {
                terminalProtocolLogResp = getTerminalProtocolLogRespByProtocol(terminalProtocolReq);
            }
            terminalShopLogRespByShop.setTerminalProtocolLogResp(terminalProtocolLogResp);
        }
        TerminalDataLogModel terminalDataLogModel = new TerminalDataLogModel();
        terminalDataLogModel.setTerminalShopId(mainModel.getId());
        terminalDataLogModel.setCompanyId(mainModel.getCompanyId());
        terminalDataLogModel.setProtocolId(0);
        terminalDataLogModel.setCreateUser(mainModel.getCreateUser());
        terminalDataLogModel.setNewData(JSON.toJSONString(terminalShopLogRespByShop));
        Integer status = scheduleModel.getStatus();
        if (status == 1) {
            terminalDataLogModel.setApprovalStatus(3);
        } else {
            terminalDataLogModel.setApprovalStatus(1);
        }
        terminalDataLogModel.setScheduleShopId(scheduleModel.getId());
        if (isInsert) {//新增
            terminalDataLogModel.setRawData(null);
            terminalDataLogModel.setCheckType(1);
        } else {//更新，需查询上次的记录
            List<TerminalDataLogModel> terminalDataLogModelList = terminalDataLogDao.selectList(new QueryWrapper<TerminalDataLogModel>()
                    .eq("terminal_shop_id", terminalDataLogModel.getTerminalShopId())
                    .eq("company_id", terminalDataLogModel.getCompanyId())
                    .eq("protocol_id", 0)
                    .orderByDesc("create_time"));
            if (CollectionUtils.isNotEmpty(terminalDataLogModelList)) {
                if (terminalDataLogModelList.stream().anyMatch(item -> item.getApprovalStatus().equals(1))) {
                    throw new BusinessException("400", "终端有审批未完成");
                }
                terminalDataLogModel.setRawData(terminalDataLogModelList.get(0).getNewData());
            }
            terminalDataLogModel.setCheckType(3);
        }
        terminalDataLogService.save(terminalDataLogModel);
        return terminalDataLogModel.getId();
    }

    /**
     * @Description: 采集终端时修改记录表
     */
    private Integer newInsertDataLog(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq, TerminalShopModel mainModel, TerminalShopInfoScheduleModel scheduleModel, List<TerminalProductProtocolRelationResp> terminalProductProtocolRelationList, boolean isInsert) {
        TerminalShopLogResp terminalShopLogRespByShop = getTerminalShopLogRespByShop(addTerminalShopInfoScheduleReq);
        if(Objects.nonNull(terminalProductProtocolRelationList) && terminalProductProtocolRelationList.size() > 0){
            terminalShopLogRespByShop.setTerminalProductProtocolRelationList(terminalProductProtocolRelationList);
        }
        TerminalDataLogModel terminalDataLogModel = new TerminalDataLogModel();
        terminalDataLogModel.setTerminalShopId(mainModel.getId());
        terminalDataLogModel.setCompanyId(mainModel.getCompanyId());
        terminalDataLogModel.setProtocolId(0);
        terminalDataLogModel.setCreateUser(mainModel.getCreateUser());
        terminalDataLogModel.setNewData(JSON.toJSONString(terminalShopLogRespByShop));
        Integer status = scheduleModel.getStatus();
        if (status == 1) {
            terminalDataLogModel.setApprovalStatus(3);
        } else {
            terminalDataLogModel.setApprovalStatus(1);
        }
        terminalDataLogModel.setScheduleShopId(scheduleModel.getId());
        if (isInsert) {//新增
            terminalDataLogModel.setRawData(null);
            terminalDataLogModel.setCheckType(TerminalShopNodeEnum.TERMINAL_COLLECT.getType());
        } else {//更新，需查询上次的记录
            List<TerminalDataLogModel> terminalDataLogModelList = terminalDataLogDao.selectList(new QueryWrapper<TerminalDataLogModel>()
                    .eq("terminal_shop_id", terminalDataLogModel.getTerminalShopId())
                    .eq("company_id", terminalDataLogModel.getCompanyId())
                    .eq("protocol_id", 0)
                    .orderByDesc("create_time"));
            if (CollectionUtils.isNotEmpty(terminalDataLogModelList)) {
                if (terminalDataLogModelList.stream().anyMatch(item -> item.getApprovalStatus().equals(1))) {
                    throw new BusinessException("400", "终端有审批未完成");
                }
                terminalDataLogModel.setRawData(terminalDataLogModelList.get(0).getNewData());
            }
            terminalDataLogModel.setCheckType(TerminalShopNodeEnum.TERMINAL_COLLECT_EDIT.getType());
        }
        terminalDataLogService.save(terminalDataLogModel);
        return terminalDataLogModel.getId();
    }


    /**
     * @author: HLQ
     * @Date: 2023/2/24 15:42
     * @Description: 组装修改记录表中的JSON字段数据
     * terminalShopModel: 终端信息表
     */
    private TerminalShopLogResp getTerminalShopLogRespByShop(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq) {
        TerminalShopLogResp terminalShopLogResp = new TerminalShopLogResp();
        BeanUtils.copyProperties(addTerminalShopInfoScheduleReq, terminalShopLogResp);
        terminalShopLogResp.setAreaName(addTerminalShopInfoScheduleReq.getProvince() + addTerminalShopInfoScheduleReq.getCity() + addTerminalShopInfoScheduleReq.getDistrict());
        terminalShopLogResp.setDealerCode(addTerminalShopInfoScheduleReq.getPrimaryContract().getDealerCode());
        terminalShopLogResp.setContractType(addTerminalShopInfoScheduleReq.getPrimaryContract().getContractType());
        terminalShopLogResp.setReceivingWarehouseAddress(addTerminalShopInfoScheduleReq.getReceivingWarehouseProvince()
                + addTerminalShopInfoScheduleReq.getReceivingWarehouseCity()
                + addTerminalShopInfoScheduleReq.getReceivingWarehouseDistrict() + addTerminalShopInfoScheduleReq.getReceivingWarehouseAddress());
        return terminalShopLogResp;
    }

    /**
     * @author: HLQ
     * @Date: 2023/2/24 16:49
     * @Description: 组装修改记录表中的JSON字段数据
     * terminalProtocolModel: 协议对象
     */
    private TerminalProtocolLogResp getTerminalProtocolLogRespByProtocol(TerminalProtocolReq terminalProtocolReq) {
        TerminalProtocolLogResp terminalProtocolLogResp = new TerminalProtocolLogResp();
        BeanUtils.copyProperties(terminalProtocolReq, terminalProtocolLogResp);
        return terminalProtocolLogResp;
    }

    /**
     * @author: HLQ
     * @Date: 2023/2/23 15:07
     * @Description: 设置客户经理信息
     */
    private void setMangerInfo(Integer createUser, TerminalShopNodeModel terminalShopNodeModel) {
        TerminalAccountManagerModel terminalAccountManagerModel = terminalAccountManagerDao.selectOne(new QueryWrapper<TerminalAccountManagerModel>().eq("id", createUser).last("limit 1"));
        if (Objects.isNull(terminalAccountManagerModel)) {
            throw new BusinessException("400", "创建人用户数据异常，请联系相关人员");
        }
        if (terminalAccountManagerModel.getType() == 0) {//是客户经理
            terminalShopNodeModel.setUpdateUser(terminalAccountManagerModel.getId());
            terminalShopNodeModel.setUpdateName(terminalAccountManagerModel.getName());
            terminalShopNodeModel.setUpdatePhone(terminalAccountManagerModel.getPhone());
        } else {
            TerminalAccountManagerModel accountManagerModel = terminalAccountManagerDao.selectOne(new QueryWrapper<TerminalAccountManagerModel>().eq("id", terminalAccountManagerModel.getParentId()).last("limit 1"));
            if (Objects.isNull(accountManagerModel)) {
                throw new BusinessException("400", "找不到当前数据创建人对应的客户经理数据，请联系相关人员");
            }
            terminalShopNodeModel.setUpdateUser(accountManagerModel.getId());
            terminalShopNodeModel.setUpdateName(accountManagerModel.getName());
            terminalShopNodeModel.setUpdatePhone(accountManagerModel.getPhone());
        }
    }


    /**
     * @author: HLQ
     * @Date: 2023/5/10 14:51
     * @Description: 预备终端预采集推送中台
     */
    private void sendZtYcjData(TerminalShopInfoScheduleModel scheduleModel, Integer isInsert) {
        RequestLog requestLog = new RequestLog();
        long ernterSystemTime = System.currentTimeMillis();
        try {
            //封装请求参数
            JSONObject reqParam = getYbReqParam(scheduleModel, isInsert);

            String token = ztUtils.getXwToken();
            //请求参数打印
            log.info("我是请求同步预备终端预采集数据的实体参数:{}", JSON.toJSONString(reqParam));
            log.info("我是请求同步预备终端预采集数据的token参数:{}", token);

            requestLog.setReqName("预备终端预采集信息:" + scheduleModel.getShopName());
            requestLog.setReqType(8);
            requestLog.setReqUrlPath(sendYbcjTerminalDataUrl);
            requestLog.setCreateDate(new Date());
            requestLog.setReqJson(reqParam.toJSONString());
            requestLog.setReqKey(scheduleModel.getShopName());
            String rtnJson = HttpUtil.createPost(sendYbcjTerminalDataUrl).header("token", token).header("Content-Type", "application/json").body(reqParam.toJSONString()).execute().body();
            requestLog.setResJson(rtnJson);
            if (com.intelliquor.cloud.shop.terminal.util.StringUtils.hasNull(rtnJson)) {
                JSONObject resp = JSONObject.parseObject(rtnJson);
                log.info("[返回参数]同步中台的预备终端预采集---{}", JSON.toJSONString(resp));
                if (resp != null && resp.getJSONObject("resp_data") != null) {
                    JSONObject result = resp.getJSONObject("resp_data").getJSONObject("result");
                    if (null != result && result.get("success").equals("True")) {
                        requestLog.setResCode("0");
                    } else {
                        requestLog.setResCode("-1");
                        requestLog.setResMsg(resp.toJSONString());
                        throw new BusinessException("400", "返回的参数:" + resp.toJSONString());
                    }
                } else {
                    requestLog.setResCode("-1");
                    requestLog.setResMsg(resp.toJSONString());
                    throw new BusinessException("400", "返回的参数:" + resp.toJSONString());
                }
            } else {
                requestLog.setResCode("-1");
                requestLog.setResMsg("没有返回值");
                throw new BusinessException("400", "没有返回值");
            }
        } catch (Exception e) {
            e.printStackTrace();
            requestLog.setResCode("-1");
            requestLog.setResMsg(e.getMessage());
            throw new BusinessException("400", "同步中台的预备终端预采集数据失败:" + e.getMessage());
        } finally {
            long outSystemTime = System.currentTimeMillis();
            String diffTime = DateUtils.longToHMS(outSystemTime - ernterSystemTime);
            requestLog.setReqTime(diffTime);
            requestLogService.insertLog(requestLog);
        }
    }


    /**
     * @author: HLQ
     * @Date: 2023/5/10 14:43
     * @Description: 预备终端预采集推送参数
     */
    private JSONObject getYbReqParam(TerminalShopInfoScheduleModel scheduleModel, Integer isInsert) {
        JSONObject rtnJson = new JSONObject();
        JSONObject json = new JSONObject();
        // storecode	String	Y	终端主编码
        json.put("storecode", scheduleModel.getMainCode());
        //deputy_code	String	Y	终端副编码
        json.put("deputy_code", scheduleModel.getDeputyCode());
        //storename	String	Y	终端名称
        json.put("storename", scheduleModel.getShopName());
        //tag	Number	Y	终端标签	0:终端关注
//        json.put("tag", scheduleModel.getTag() + "");
        //headimg	String	Y	门头照
        json.put("headimg", scheduleModel.getHeadImg());
        //remark String	N	备注
        json.put("remark", com.intelliquor.cloud.shop.terminal.util.StringUtils.hasNull(scheduleModel.getRemark()) ? scheduleModel.getRemark() : "");
        //province	String	Y	省
        json.put("province", scheduleModel.getProvince());
        //city	String	Y	市
        json.put("city", scheduleModel.getCity());
        //district	String	Y	区
        json.put("district", scheduleModel.getDistrict());
        //address	String	Y	详细地址
        json.put("address", scheduleModel.getAddress());
        //longitude	String	Y	经度
        json.put("longitude", scheduleModel.getLongitude() + "");
        //latitude	String	Y	纬度
        json.put("latitude", scheduleModel.getLatitude() + "");
        //store_type	String	Y	终端类型 	1-烟酒店、2-专卖店、3-餐饮店、4-商超 5-渠道终端 6-企业终端 7-餐饮终端 8-连锁终端 9-团购终端  10-渠道终端会员 11-连锁终端会员 12-非会员虚拟终端
        int shopType = scheduleModel.getShopType().intValue();
        String storeType = terminalShopCommonService.generateTerminalTypeByTerminalType(shopType);
        json.put("store_type", storeType);
        // 新终端类型 0:渠道终端 1:餐饮终端 2:团购终端 3:企业终端 4:连锁终端 5会员终端
        // json.put("shop_type", shopType + "");

        //status	状态	0：预激活 1：已激活 2:：预采集
        json.put("status", "2");
        // option			操作状态	1：新增 2：编辑
        json.put("option", isInsert + "");

        rtnJson.put("gt_store", json);
        return rtnJson;
    }
}
