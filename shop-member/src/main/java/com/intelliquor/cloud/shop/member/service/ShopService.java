package com.intelliquor.cloud.shop.member.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.intelliquor.cloud.shop.common.account.dto.AccountScoreDTO;
import com.intelliquor.cloud.shop.common.account.dto.MemberScoreAccountRespDTO;
import com.intelliquor.cloud.shop.common.account.service.IAccountRemoteService;
import com.intelliquor.cloud.shop.common.dao.*;
import com.intelliquor.cloud.shop.common.entity.FileItem;
import com.intelliquor.cloud.shop.common.enums.*;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.model.*;
import com.intelliquor.cloud.shop.common.service.ITerminalShopDisableRecordCommonService;
import com.intelliquor.cloud.shop.common.utils.ExcelTools;
import com.intelliquor.cloud.shop.common.utils.ObjectUtil;
import com.intelliquor.cloud.shop.common.utils.TimeUtilis;
import com.intelliquor.cloud.shop.member.model.InvitationModel;
import com.intelliquor.cloud.shop.member.model.ShopForExportModel;
import com.intelliquor.cloud.shop.member.model.resp.ShopNumVO;
import com.intelliquor.cloud.shop.system.dao.CloudDealerInfoDao;
import com.intelliquor.cloud.shop.system.dao.RewardCycleBalanceDao;
import com.intelliquor.cloud.shop.common.model.LinkageRewardModel;
import com.intelliquor.cloud.shop.system.model.OrderDownloadCenterModel;
import com.intelliquor.cloud.shop.system.model.VirtualAmountModel;
import com.intelliquor.cloud.shop.system.service.CloudDealerInfoService;
import com.intelliquor.cloud.shop.system.service.OrderDownloadCenterService;
import com.intelliquor.cloud.shop.system.service.gt.ShopDealerOrderService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述：终端管理 服务实现层
 *
 * <AUTHOR>
 * @date 2019-06-24
 */
@Service
public class ShopService {

    @Autowired
    private ShopDao shopDao;

    @Autowired
    private ShopUserService userService;

    @Autowired
    private InvitationService invitationService;

    @Autowired
    private OrderDownloadCenterService downloadCenterService;

    @Autowired
    private CloudDealerInfoService cloudDealerInfoService;

    @Autowired
    private CloudDealerInfoDao cloudDealerInfoDao;

    @Autowired
    private RewardCycleBalanceDao rewardCycleBalanceDao;

    @Resource
    private TerminalRewardRecordDao terminalRewardRecordDao;


    @Autowired
    private TerminalShopCommonDao terminalShopCommonDao;


    @Autowired
    private TerminalShopContractCommonDao terminalShopContractCommonDao;

    @Resource
    private ShopDealerOrderService shopDealerOrderService;

    @Resource
    private ITerminalShopDisableRecordCommonService terminalShopDisableRecordService;

    @Resource
    private TerminalShopNodeDao terminalShopNodeDao;

    @Autowired
    private ShopUserService shopUserService;

    @Autowired
    private ActivityRewardRecordDao activityRewardRecordDao;

    @Autowired
    private IAccountRemoteService accountRemoteService;

    /**
     * 查询数据
     *
     * @return
     */
    public List<ShopModel> selectList(Map<String, Object> searchMap) {
        return shopDao.selectList(searchMap);
    }

    /**
     * 查询数据
     *
     * @return
     */
    public List<ShopModel> selectListPermission(Map<String, Object> searchMap) {
        return shopDao.selectListPermission(searchMap);
    }

    /**
     * 查询数据（导出）
     *
     * @return
     */
    public List<ShopModel> selectListForExprot(Map<String, Object> searchMap) {
        return shopDao.selectListForExprot(searchMap);
    }


    /**
     * 查询认证数据信息
     *
     * @param searchMap
     * @return
     */
    public List<ShopModel> selectOauthList(Map<String, Object> searchMap) {
        searchMap.put("oauthList", "1");
        return shopDao.selectOauthList(searchMap);
    }

    /**
     * 新增数据
     *
     * @param model
     */
    public void insert(ShopModel model) {
        model.setStatus(0);
        model.setOauthStatus(0);
        shopDao.insert(model);
    }

    /**
     * 更新数据
     *
     * @param model
     */
    public void update(ShopModel model) {
        model.setUpdateTime(new Date());
        shopDao.update(model);
    }

    /**
     * 设置默认终端店
     *
     * @param shopId
     * @param openId
     */
    public void setDefault(Integer shopId, String openId) {
        // 全部设置成非默认
        ShopModel noDefault = new ShopModel();
        noDefault.setOpenId(openId);
        noDefault.setIsDefault(0);
        shopDao.setDefault(noDefault);
        // 设置默认终端
        ShopModel yesDefault = new ShopModel();
        yesDefault.setId(shopId);
        yesDefault.setIsDefault(1);
        shopDao.setDefault(yesDefault);
    }


    /**
     * 删除数据
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(Integer id) {
        shopDao.delete(id);
        //删除关联的人员
        userService.deleteByShopId(id);
    }

    /**
     * 根据ID查询数据
     *
     * @param id
     */
    public ShopModel getById(Integer id) {
        return shopDao.getById(id);
    }

    /**
     * 假的测试用
     *
     * @return
     */
    public List<OrgModel> selectOrgList() {
        return shopDao.selectOrgList();
    }

    /**
     * 创建终端和主账号
     *
     * @param shop
     */
    @Transactional(rollbackFor = Exception.class)
    public ShopUserModel createShopInfo(ShopModel shop, String openId, Integer roleId) {
        //保存店铺
        shop.setStatus(0);
        shop.setOauthStatus(-1);
        if (StringUtils.isBlank(shop.getProvince())) {
            shop.setProvince(null);
        }
        if (StringUtils.isBlank(shop.getCity())) {
            shop.setCity(null);
        }
        if (StringUtils.isBlank(shop.getDistrict())) {
            shop.setDistrict(null);
        }
        if (StringUtils.isBlank(shop.getTownship())) {
            shop.setTownship(null);
        }
        shopDao.insert(shop);
        //保存用户
        ShopUserModel user = new ShopUserModel();
        user.setName(shop.getLinkman());
        user.setPhone(shop.getLinkphone());
        user.setStatus(0);
        user.setPrimaryAccount(1);
        user.setShopId(shop.getId());
        user.setScore(0);
        user.setAmount(BigDecimal.valueOf(0));
        user.setPhoto(shop.getPhoto());
        user.setOpenId(openId);
        user.setNote(shop.getNode());
        user.setRoleId(roleId);
        user.setUnionId(shop.getUnionId());
        userService.create(user);
        //更新邀请码
        InvitationModel invitationModel = new InvitationModel();
        invitationModel.setInvitationCode(shop.getInvitationCode());
        invitationModel.setCustomerId(shop.getId());
        invitationService.updateInvitationCode(invitationModel);
        return user;
    }

    /**
     * 根据ID查询
     *
     * @param phone
     * @return
     */
    public ShopModel getByPhone(String phone) {
        return shopDao.getByPhone(phone);
    }

    /**
     * 根据办事处和商品查询终端信息
     *
     * @param goodsCode
     * @param orgName
     * @return
     */
    public List<ShopModel> selectShopByGoodsCodeAndOrgName(String goodsCode, String orgName) {
        return shopDao.selectShopByGoodsCodeAndOrgName(goodsCode, orgName);
    }

    /**
     * 根据openid查询终端列表
     *
     * @param openid
     * @return
     */
    public List<ShopModel> selectShopByOpenid(String openid) {
        return shopDao.selectShopByOpenid(openid);
    }

    /**
     * 终端导出
     *
     * @param searchMap
     * @param shopModel
     * @throws IOException
     */
    @Async
    public void terminalExportTask(Map<String, Object> searchMap, ShopForExportModel shopModel) throws IOException {
        System.out.println("开始执行");

        // 1、添加下载中心数据
        OrderDownloadCenterModel downloadCenterModel = this.addTerminalDownloadCenter(shopModel);

        // 2、上传文件
        String fileUrl = this.exportOrderFile(searchMap);

        // 3、修改下载中心状态
        downloadCenterModel.setFilePath(fileUrl);
        downloadCenterService.updOrderDownloadCenter(downloadCenterModel);

        System.out.println("结束执行");
    }


    /**
     * 添加下载中心数据
     *
     * @param shopModel
     * @return
     */
    public OrderDownloadCenterModel addTerminalDownloadCenter(ShopForExportModel shopModel) {
        OrderDownloadCenterModel downloadCenterModel = shopModel.getDownloadCenterModel();

        downloadCenterModel.setType(OrderTypeEnum.TERMINALDATA.getValue().shortValue());

        downloadCenterService.addOrderDownloadCenter(downloadCenterModel);
        return downloadCenterModel;
    }

    /**
     * 创建文件并上传至阿里云OSS
     *
     * @param searchMap
     * @return
     * @throws IOException
     */
    public String exportOrderFile(Map<String, Object> searchMap) throws IOException {
        List<ShopModel> shopModels = this.selectListForExprot(searchMap);

//        String fileName = (new StringBuffer()).append("ZDKH").append(TimeUtilis.getCurrentFormatTime("yyyyMMddHHmmss")).append(".xls").toString();
//        Workbook workbook = ExcelUtil.returnExcel(shopModels, fileName, "终端客户数据", "sheet1", ShopModel.class);
//
//        return downloadCenterService.getFileUrl(fileName, workbook);

        FileItem fileItem = ExcelTools.exportByFile(shopModels, 1);
        String fileName = (new StringBuffer()).append("ZDKH").append(TimeUtilis.getCurrentFormatTime("yyyyMMddHHmmss")).append(".xls").toString();
        return downloadCenterService.getFileUrl(fileName, fileItem.getContent());
    }

    /**
     * 查询门店的认证的数据
     *
     * @param model
     * @return
     */
    public ShopModel getAuthentication(ShopModel model) {
        return shopDao.getAuthentication(model);

    }


    /**
     * 根据ID查询数据
     *
     * @param id
     */
    public ShopModel getShopInfo(Integer id) {
        ShopModel shopInfo = shopDao.getShopInfo(id);
        if (ObjectUtil.isNotEmpty(shopInfo)) {
            // 获取自己的t_cloud_dealer_info的信息
            Map<String, Object> search = new HashMap<>();
            search.put("dealerCode", shopInfo.getDealerCode());
            search.put("sortCode", "id");
            search.put("sortRole", "desc");
            List<com.intelliquor.cloud.shop.system.model.CloudDealerInfoModel> dealerInfoList = cloudDealerInfoDao.selectList(search);
            if (dealerInfoList.isEmpty()) {
                throw new BusinessException("未找到联盟对应的经销商信息");
            }
            if (dealerInfoList.size() > 1) {
                throw new BusinessException("经销商编码重复");
            }
            com.intelliquor.cloud.shop.system.model.CloudDealerInfoModel mydealer = dealerInfoList.get(0);
            shopInfo.setDealerId(mydealer.getId().intValue());
            shopInfo.setDealerCode(mydealer.getDealerCode());
            shopInfo.setDealerName(mydealer.getDealerName());
            shopInfo.setDealerLinkman(mydealer.getLinkman());
            shopInfo.setDealerPhone(mydealer.getPhone());
            shopInfo.setDealerAddress(mydealer.getAddress());

            // 获取上级经销商数据
            Map<String, Object> supplierSearchMap = new HashMap<>();
            supplierSearchMap.put("companyId", shopInfo.getCompanyId());
            supplierSearchMap.put("dealerId", mydealer.getId());
            List<com.intelliquor.cloud.shop.system.model.CloudDealerInfoModel> supplierList = cloudDealerInfoService.selectSupplierList(supplierSearchMap);
            // 上级经销商列表
            List<CloudDealerInfoModel> cloudDealerInfoModels = new ArrayList<>();
            // 先把自己的经销商账号加入到上级列表
            CloudDealerInfoModel selfDealer = new CloudDealerInfoModel();
            selfDealer.setId(mydealer.getId());
            selfDealer.setDealerCode(mydealer.getDealerCode());
            selfDealer.setDealerName(mydealer.getDealerName());
            selfDealer.setLinkman(mydealer.getLinkman());
            selfDealer.setPhone(mydealer.getPhone());
            cloudDealerInfoModels.add(selfDealer);
            // 处理其他的上级信息
            if (ObjectUtil.isNotEmpty(supplierList)) {
                // 上级经销商列表
                for (com.intelliquor.cloud.shop.system.model.CloudDealerInfoModel supplierInfo : supplierList) {
                    CloudDealerInfoModel dealerInfo = new CloudDealerInfoModel();
                    dealerInfo.setId(supplierInfo.getId());
                    dealerInfo.setDealerCode(supplierInfo.getDealerCode());
                    dealerInfo.setDealerName(supplierInfo.getDealerName());
                    dealerInfo.setLinkman(supplierInfo.getLinkman());
                    dealerInfo.setPhone(supplierInfo.getPhone());
                    cloudDealerInfoModels.add(dealerInfo);
                }
            }
            shopInfo.setSupplierDealerList(cloudDealerInfoModels);
        }

        return shopInfo;
    }

    /**
     * 根据终端店id查询虚拟货款
     * 【积分】此分业务确定不用改
     */
    public VirtualAmountModel selectVirtualAmount(Integer shopId) {
        //创建返回类
        VirtualAmountModel resultData = new VirtualAmountModel();

        //先查询账户锁拥有的虚拟货款
        ShopModel shopModel = shopDao.getById(shopId);

        //赋值当前可用的虚拟货款 2022-11-22新增懐酒三个钱包功能 所以要把钱加起来
        //会员钱包
        BigDecimal replenishStockVirtualAmount = shopModel.getReplenishStockVirtualAmount();
        //零售钱包
        BigDecimal retailRewardVirtualAmount = shopModel.getRetailRewardVirtualAmount();
        //年返钱包
        //BigDecimal yearReturnVirtualAmount = shopModel.getYearReturnVirtualAmount();
        //统计返回
        // resultData.setUsableVirtualAmount(replenishStockVirtualAmount.add(retailRewardVirtualAmount));
        resultData.setUsableVirtualAmount(shopModel.getVirtualAmount());

        //赋值当前待到账的虚拟货款
        resultData.setAwaitVirtualAmount(shopModel.getAwaitVirtualAmount());

        //查询累计获得积分
        BigDecimal totalInScore = terminalRewardRecordDao.selectInScoreByShopId(shopId.longValue());

        resultData.setTotalInScore(totalInScore == null ? BigDecimal.ZERO : totalInScore);

        //查询累计使用积分
        BigDecimal totalOutScore = terminalRewardRecordDao.selectOutScoreByShopId(shopId.longValue());

        resultData.setTotalOutScore(totalOutScore == null ? BigDecimal.ZERO : totalOutScore);

        return resultData;
    }

    /**
     * 根据终端店id查询虚拟货款
     */
    public List<VirtualAmountModel> selectVirtualAmount(Integer shopId, String sku) {
        //创建返回类
        List<VirtualAmountModel> result = new ArrayList<>();


        // 【积分】查询SKU对应账户的积分
        List<MemberScoreAccountRespDTO> memberScoreAccountRespDTOList = accountRemoteService.querySubAccountScoreBySku(shopId.longValue(), sku);
        if(CollectionUtils.isEmpty(memberScoreAccountRespDTOList)){
            throw new BusinessException("账户异常");
        }
        for(MemberScoreAccountRespDTO dto : memberScoreAccountRespDTOList){
            VirtualAmountModel resultData = new VirtualAmountModel();
            resultData.setUsableVirtualAmount(dto.getAvailableScore().setScale(2, RoundingMode.HALF_UP));
            resultData.setSubAccountId(dto.getId().toString());
            resultData.setAccountTypeName(dto.getAccountTypeName());
            result.add(resultData);
        }
        return result;
    }

    public ShopNumVO getShopNum(Integer userId) {
        ShopNumVO vo = new ShopNumVO();
        vo.setShopNum(0);
        vo.setDisabledNum(0);
        /*// 查询对应的合同和营业执照编码
        TerminalShopContractCommonModel contractCommonModel = terminalShopContractCommonDao.getShopContractByMemberShopId(shopId);
        if(Objects.nonNull(contractCommonModel)){
            String contractCode = contractCommonModel.getContractCode();
            TerminalShopCommonModel terminalShopCommonModel = terminalShopCommonDao.selectById(contractCommonModel.getTerminalShopId());
            if(Objects.nonNull(terminalShopCommonModel)){
                String licenseCode = terminalShopCommonModel.getLicenseCode();
                List<ShopModel> shopModelList = shopDao.getShopNumList(contractCode, licenseCode);
                if(!CollectionUtils.isEmpty(shopModelList)){
                    vo.setShopNum(shopModelList.size());
                    List<ShopModel> disabledShopList = shopModelList.stream().filter(e -> e.getStatus() == 1).collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(disabledShopList)){
                        vo.setDisabledNum(disabledShopList.size());
                    }
                }
            }
        }*/
        List<ShopModel> shopModelList = shopDao.getShopNumOneList(userId);
        if (!CollectionUtils.isEmpty(shopModelList)) {
            vo.setShopNum(shopModelList.size());
            List<ShopModel> disabledShopList = shopModelList.stream().filter(e -> e.getStatus() == 1).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(disabledShopList)) {
                vo.setDisabledNum(disabledShopList.size());
            }
        }
        return vo;
    }

    /**
     * 禁用联盟终端
     *
     * @param memberShopId 店铺ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void disableMemberShop(Long memberShopId, ShopUserModel shopUserModel) {
        //查询终端信息
        TerminalShopCommonModel terminalShop = terminalShopCommonDao.selectTerminalModelByMemberId(memberShopId.intValue());
        if (Objects.isNull(terminalShop)) {
            throw new BusinessException("终端不存在");
        }
        //获取联盟信息
        ShopModel shopInfo = shopDao.getShopInfo(memberShopId.intValue());
        if (MemberShopStatusEnum.STATUS_DISABLE.getStatus().equals(shopInfo.getStatus())) {
            throw new BusinessException("该终端已经禁用");
        }
        if (MemberShopStatusEnum.STATUS_CHECK.getStatus().equals(shopInfo.getStatus())) {
            throw new BusinessException("该终端审核中,无法禁用");
        }

        //查询是否存在未完成订单
        Boolean finishStatus = shopDealerOrderService.getExistOrderFinish(terminalShop.getMemberShopId().longValue());
        if (Boolean.TRUE.equals(finishStatus)) {
            throw new BusinessException("该终端存在未完成订单,无法禁用");
        }
        //记录操作日志
        TerminalShopDisableRecordCommonModel terminalShopDisableRecord = new TerminalShopDisableRecordCommonModel();
        terminalShopDisableRecord.setTerminalShopId(terminalShop.getId().longValue());
        terminalShopDisableRecord.setMemberShopId(memberShopId);
        terminalShopDisableRecord.setTerminalStatus(TerminalShopStatusEnum.STATUS_DISABLE.getStatus());
        terminalShopDisableRecord.setCreateUserId(shopUserModel.getId().longValue());
        terminalShopDisableRecord.setCreateUserName(shopUserModel.getName());
        terminalShopDisableRecord.setCreateUserPhone(shopUserModel.getPhone());
        terminalShopDisableRecord.setCheckStatus(0);
        terminalShopDisableRecord.setCreateTime(new Date());
        terminalShopDisableRecord.setUpdateTime(new Date());
        terminalShopDisableRecord.setRemark("禁用终端【" + terminalShop.getShopName() + "】," +
                "操作人【" + shopUserModel.getName() + "】," +
                "审核人【" + shopUserModel.getPhone() + "】");
        terminalShopDisableRecordService.save(terminalShopDisableRecord);

        //添加审批的节点数据
        TerminalShopNodeModel terminalShopNodeModel = TerminalShopNodeModel.builder()
                .terminalShopId(terminalShop.getId())
                .shopId(memberShopId.intValue())
                .companyId(terminalShop.getCompanyId())
                .nodeName(TerminalShopNodeEnum.TERMINAL_DISABLE.getName())
                .nodeLevel("0")
                .nodeType(TerminalShopNodeEnum.TERMINAL_DISABLE.getType())
                .isBack("0")
                .nodeStatus("0")
                .createTime(new Date())
                .nodeSourceId(terminalShopDisableRecord.getId().intValue())
                .build();
        // 1 保存审批记录
        terminalShopNodeDao.insert(terminalShopNodeModel);

        //禁用终端
        //非客户经理需要审核
        shopDao.updateMemberShopStatus(memberShopId, MemberShopStatusEnum.STATUS_CHECK.getStatus());
    }

    public VirtualAmountModel selectTotalVirtualAmount(ShopUserModel shopUserModel) {
        //创建返回类
        VirtualAmountModel resultData = new VirtualAmountModel();
        ShopModel shopModel = shopDao.getById(shopUserModel.getShopId());
        //非终端统计
        if (!shopModel.getAccountType().equals(5)) {
            return selectVirtualAmount(shopUserModel.getShopId());
        }
        //查询所有手机号的终端店
        List<ShopUserModel> userList = shopUserService.selectShopByShopUserRelationNew2(shopUserModel.getPhone(), shopUserModel.getCompanyId());
        if (CollectionUtils.isEmpty(userList)) {
            resultData.setUsableVirtualAmount(BigDecimal.ZERO);
            resultData.setAwaitVirtualAmount(BigDecimal.ZERO);
            resultData.setTotalInScore(BigDecimal.ZERO);
            resultData.setTotalOutScore(BigDecimal.ZERO);
        }
        List<Integer> shopIdList = userList.stream().filter(s -> s.getAccountType().equals(5)).map(ShopUserModel::getShopId).collect(Collectors.toList());
        List<ShopModel> shopModelList = shopDao.getByIds(shopIdList);
        //赋值当前可用的虚拟货款 2022-11-22新增懐酒三个钱包功能 所以要把钱加起来
        resultData.setUsableVirtualAmount(shopModelList.stream().map(ShopModel::getVirtualAmount).reduce(BigDecimal.ZERO, BigDecimal::add));

        //赋值当前待到账的虚拟货款
        resultData.setAwaitVirtualAmount(shopModelList.stream().map(ShopModel::getAwaitVirtualAmount).reduce(BigDecimal.ZERO, BigDecimal::add));

        //查询累计获得积分
        BigDecimal totalInScore = terminalRewardRecordDao.selectInScoreByShopIds(shopIdList);

        resultData.setTotalInScore(totalInScore == null ? BigDecimal.ZERO : totalInScore);

        //查询累计使用积分
        BigDecimal totalOutScore = terminalRewardRecordDao.selectOutScoreByShopIds(shopIdList);

        resultData.setTotalOutScore(totalOutScore == null ? BigDecimal.ZERO : totalOutScore);

        return resultData;

    }

    public void enableMemberShop(Long memberShopId, ShopUserModel shopUserModel) {
        //查询终端信息
        TerminalShopCommonModel terminalShop = terminalShopCommonDao.selectTerminalModelByMemberId(memberShopId.intValue());
        if (Objects.isNull(terminalShop)) {
            throw new BusinessException("终端不存在");
        }
        //校验终端基本信息完整性
        //校验终端合同信息完整性
        if (!verifyBaseInfoParam(terminalShop) || !verifyContractInfoParam(terminalShop)) {
            throw new BusinessException("当前门店信息不完整，请先补填其资料");
        }
        //获取联盟信息
        ShopModel shopInfo = shopDao.getShopInfo(memberShopId.intValue());
        if (MemberShopStatusEnum.STATUS_ENABLE.getStatus().equals(shopInfo.getStatus())) {
            throw new BusinessException("该终端已经启用");
        }
        if (MemberShopStatusEnum.STATUS_CHECK.getStatus().equals(shopInfo.getStatus())) {
            throw new BusinessException("该终端审核中,无法启用");
        }
        //记录操作日志
        TerminalShopDisableRecordCommonModel terminalShopDisableRecord = new TerminalShopDisableRecordCommonModel();
        terminalShopDisableRecord.setTerminalShopId(terminalShop.getId().longValue());
        terminalShopDisableRecord.setMemberShopId(memberShopId);
        terminalShopDisableRecord.setTerminalStatus(TerminalShopStatusEnum.STATUS_ENABLE.getStatus());
        terminalShopDisableRecord.setCreateUserId(shopUserModel.getId().longValue());
        terminalShopDisableRecord.setCreateUserName(shopUserModel.getName());
        terminalShopDisableRecord.setCreateUserPhone(shopUserModel.getPhone());
        terminalShopDisableRecord.setCheckStatus(0);
        terminalShopDisableRecord.setCreateTime(new Date());
        terminalShopDisableRecord.setUpdateTime(new Date());
        terminalShopDisableRecord.setRemark("启用终端【" + terminalShop.getShopName() + "】," +
                "操作人【" + shopUserModel.getName() + "】," +
                "审核人【" + shopUserModel.getPhone() + "】");
        terminalShopDisableRecordService.save(terminalShopDisableRecord);

        //添加审批的节点数据
        TerminalShopNodeModel terminalShopNodeModel = TerminalShopNodeModel.builder()
                .terminalShopId(terminalShop.getId())
                .shopId(memberShopId.intValue())
                .companyId(terminalShop.getCompanyId())
                .nodeName(TerminalShopNodeEnum.TERMINAL_ENABLE.getName())
                .nodeLevel("0")
                .nodeType(TerminalShopNodeEnum.TERMINAL_ENABLE.getType())
                .isBack("0")
                .nodeStatus("0")
                .createTime(new Date())
                .nodeSourceId(terminalShopDisableRecord.getId().intValue())
                .build();
        // 1 保存审批记录
        terminalShopNodeDao.insert(terminalShopNodeModel);

        //启用终端
        //非客户经理需要审核
        shopDao.updateMemberShopStatus(memberShopId, MemberShopStatusEnum.STATUS_CHECK.getStatus());
    }


    /**
     * 校验终端基本信息
     *
     * @param terminalShop 终端信息
     * @return Boolean
     */
    public Boolean verifyBaseInfoParam(TerminalShopCommonModel terminalShop) {

        if (null == terminalShop.getShopType()) {
            return false;
        }

        if (StringUtils.isBlank(terminalShop.getProvince())) {
            return false;
        }

        if (StringUtils.isBlank(terminalShop.getCity())) {
            return false;
        }

        if (StringUtils.isBlank(terminalShop.getAddress())) {
            return false;
        }
        if (null == terminalShop.getLongitude()) {
            return false;
        }

        if (null == terminalShop.getLatitude()) {
            return false;
        }

        if (StringUtils.isBlank(terminalShop.getShopName())) {
            return false;
        }


        if (StringUtils.isBlank(terminalShop.getLeaderName())) {
            return false;
        }

        if (StringUtils.isBlank(terminalShop.getLeaderPhone())) {
            return false;
        }

        //团购终端不判断营业执照等信息
        if (!terminalShop.getShopType().equals(2)) {
            if (StringUtils.isBlank(terminalShop.getHeadImg())) {
                return false;
            }
        }
        //渠道和连锁需要验证自营和形象店以及仓库地址
        if (terminalShop.getShopType().equals(0) && terminalShop.getShopType().equals(4)) {
            if (Objects.isNull(terminalShop.getWhetherProprietaryTrading())) {
                return false;
            }

            if (Objects.isNull(terminalShop.getIsImage())) {
                return false;
            } else {
                //如果形象店不为空 需要判断形象店门头照是不是为空
                if (terminalShop.getIsImage().equals(1)) {
                    if (StringUtils.isBlank(terminalShop.getImageHeadPicture())) {
                        return false;
                    }
                }
            }
        }

        //1：餐饮终端
        if (terminalShop.getShopType().equals(1)) {
            if (StringUtils.isBlank(terminalShop.getFoodBusinessLicense())) {
                return false;
            }
        }

        if (2 != terminalShop.getShopType()) {

            if (StringUtils.isBlank(terminalShop.getContactPhone())) {
                return false;
            }

            if (StringUtils.isBlank(terminalShop.getContactName())) {
                return false;
            }

            if (StringUtils.isBlank(terminalShop.getContactLevel())) {
                return false;
            }

            if (Objects.isNull(terminalShop.getWhetherLicense())) {
                return false;
            } else {
                //判断有无营业执照 1是有 有的情况下验证营业执照照片和营业执照号
                if (1 == terminalShop.getWhetherLicense()) {
                    if (StringUtils.isBlank(terminalShop.getLicenseImg())) {
                        return false;
                    }

                    if (StringUtils.isBlank(terminalShop.getLicenseCode())) {
                        return false;
                    }
                }
            }

            //如果是餐饮终端 需要验证食品经营许可证
            if (terminalShop.getShopType().equals(1)) {
                if (StringUtils.isBlank(terminalShop.getFoodBusinessLicense())) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 校验终端合同信息
     *
     * @param terminalShop
     * @return
     */
    public Boolean verifyContractInfoParam(TerminalShopCommonModel terminalShop) {
        TerminalShopContractCommonModel terminalShopContractResp = terminalShopContractCommonDao.getShopContractByMemberShopId(terminalShop.getMemberShopId());
        //判断合同是不是空
        if (Objects.isNull(terminalShopContractResp)) {
            return false;
        } else {
            if (Objects.isNull(terminalShopContractResp.getContractType())) {
                return false;
            }
            if (StringUtils.isBlank(terminalShopContractResp.getDealerCode())) {
                return false;
            }

            if (StringUtils.isBlank(terminalShopContractResp.getContractCode())) {
                return false;
            }
        }
        return true;
    }

    public ShopModel getParentInfoByShopId(Integer shopId) {
        return shopDao.getParentInfoByShopId(shopId);
    }

    public LinkageRewardModel selectLinkageRewardStatistics(String shopIds,Integer userId) {
        //创建返回类
        LinkageRewardModel resultData = new LinkageRewardModel();
        List<Integer> shopIdList = Arrays.asList(shopIds.split(",")).stream().map(Integer::parseInt).collect(Collectors.toList());
        //获取已发奖励
        LambdaQueryWrapper<ActivityRewardRecordModel> rewardRecordModelLambdaQueryWrapper = new LambdaQueryWrapper<>();
        rewardRecordModelLambdaQueryWrapper.in(ActivityRewardRecordModel::getShopId,shopIdList);
        rewardRecordModelLambdaQueryWrapper.eq(ActivityRewardRecordModel::getStatus,1);
        rewardRecordModelLambdaQueryWrapper.eq(ActivityRewardRecordModel::getRewardType, RewardType.SEND_MONY_TYPE.getCode());
        rewardRecordModelLambdaQueryWrapper.eq(ActivityRewardRecordModel::getIsDelete,0);
        rewardRecordModelLambdaQueryWrapper.eq(ActivityRewardRecordModel::getSendStatus,SendStatus.SUCCESS_TYPE.getCode());
        List<ActivityRewardRecordModel> activityRewardRecordModels = activityRewardRecordDao.selectList(rewardRecordModelLambdaQueryWrapper);
        // 已发奖励求和
        BigDecimal rewardMoney = activityRewardRecordModels.stream().map(ActivityRewardRecordModel::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        resultData.setTotalRewardMoney(rewardMoney);

        //获取实物奖励列表
        List<InKindRewardModel> inKindRewardModels = activityRewardRecordDao.inKindRewarList(shopIdList);
        resultData.setInKindRewardModelList(inKindRewardModels);

        return resultData;
    }

    public List<ShopModel> selectShopList(Map<String, Object> searchMap) {
        return shopDao.selectShopList(searchMap);
    }
}
