diff --git a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/constant/RedisConstant.java b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/constant/RedisConstant.java
index ab1fa41723..55e2300c6d 100644
--- a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/constant/RedisConstant.java
+++ b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/constant/RedisConstant.java
@@ -158,4 +158,9 @@ public class RedisConstant {
      */
     public static final String OPS_SALES_REWARD_KEY = "ops:sales:reward:lock:%s";
 
+    /**
+     * 批量申诉防重复提交锁
+     */
+    public static final String BATCH_APPEAL_LOCK_KEY = "fence:batch:appeal:lock:user:%s";
+
 }
diff --git a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/dao/FenceViolationOfAgreementAppealDao.java b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/dao/FenceViolationOfAgreementAppealDao.java
index a5d805b3aa..ee9e68e21b 100644
--- a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/dao/FenceViolationOfAgreementAppealDao.java
+++ b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/dao/FenceViolationOfAgreementAppealDao.java
@@ -1,6 +1,7 @@
 package com.intelliquor.cloud.shop.common.dao;
 
 import com.baomidou.mybatisplus.core.mapper.BaseMapper;
+import com.intelliquor.cloud.shop.common.basequery.ExtBaseMapper;
 import com.intelliquor.cloud.shop.common.model.FenceViolationOfAgreementAppealModel;
 import com.intelliquor.cloud.shop.common.model.resp.FenceViolationOfAgreementAppealModelResp;
 import org.apache.ibatis.annotations.Param;
@@ -16,7 +17,7 @@ import java.util.Map;
  * <AUTHOR>
  * @since 2024-03-07
  */
-public interface FenceViolationOfAgreementAppealDao extends BaseMapper<FenceViolationOfAgreementAppealModel> {
+public interface FenceViolationOfAgreementAppealDao extends ExtBaseMapper<FenceViolationOfAgreementAppealModel> {
 
     List<FenceViolationOfAgreementAppealModelResp> appealList(FenceViolationOfAgreementAppealModel req);
 
diff --git a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/req/BatchAppealServiceReq.java b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/req/BatchAppealServiceReq.java
new file mode 100644
index 0000000000..ff139e83ba
--- /dev/null
+++ b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/req/BatchAppealServiceReq.java
@@ -0,0 +1,60 @@
+package com.intelliquor.cloud.shop.common.model.req;
+
+import lombok.AllArgsConstructor;
+import lombok.Builder;
+import lombok.Getter;
+import lombok.NoArgsConstructor;
+
+import java.io.Serializable;
+import java.util.List;
+
+/**
+ * 批量申诉Service层请求参数
+ *
+ * <AUTHOR>
+ * @since 2024-01-01
+ */
+@Getter
+@Builder
+@AllArgsConstructor
+@NoArgsConstructor
+public class BatchAppealServiceReq implements Serializable {
+
+    private static final long serialVersionUID = 1L;
+
+    /**
+     * 违约记录ID列表
+     */
+    private List<Integer> violationOfAgreementInfoIds;
+
+    /**
+     * 申诉说明
+     */
+    private String reason;
+
+    /**
+     * 申诉附件URL
+     */
+    private String appealFileUrl;
+
+    /**
+     * 创建人ID
+     */
+    private Integer createUser;
+
+    /**
+     * 创建人姓名
+     */
+    private String createUserName;
+
+    /**
+     * 公司ID
+     */
+    private Integer companyId;
+
+    /**
+     * 申诉人电话
+     */
+    private String appealUserTel;
+
+}
\ No newline at end of file
diff --git a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/req/BatchFenceViolationAppealReq.java b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/req/BatchFenceViolationAppealReq.java
new file mode 100644
index 0000000000..1f3440e560
--- /dev/null
+++ b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/req/BatchFenceViolationAppealReq.java
@@ -0,0 +1,46 @@
+package com.intelliquor.cloud.shop.common.model.req;
+
+import lombok.Getter;
+import lombok.Builder;
+import lombok.AllArgsConstructor;
+import lombok.NoArgsConstructor;
+
+import javax.validation.constraints.NotBlank;
+import javax.validation.constraints.NotEmpty;
+import javax.validation.constraints.Size;
+import java.io.Serializable;
+import java.util.List;
+
+/**
+ * 批量违约申诉请求DTO
+ *
+ * <AUTHOR>
+ * @since 2024-01-01
+ */
+@Getter
+@Builder
+@AllArgsConstructor
+@NoArgsConstructor
+public class BatchFenceViolationAppealReq implements Serializable {
+
+    private static final long serialVersionUID = 1L;
+
+    /**
+     * 违约记录ID列表
+     */
+    @NotEmpty(message = "违约记录ID列表不能为空")
+    @Size(min = 1, max = 100, message = "批量申诉数量必须在1-100之间")
+    private List<Integer> violationOfAgreementInfoIds;
+
+    /**
+     * 申诉说明
+     */
+    @NotBlank(message = "申诉说明不能为空")
+    private String reason;
+
+    /**
+     * 申诉附件URL（JSON格式）
+     */
+    @NotBlank(message = "申诉附件不能为空")
+    private String appealFileUrl;
+}
\ No newline at end of file
diff --git a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/req/FenceViolateOpenBottleAreaRecordReq.java b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/req/FenceViolateOpenBottleAreaRecordReq.java
index cb3e419947..4691b41213 100644
--- a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/req/FenceViolateOpenBottleAreaRecordReq.java
+++ b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/req/FenceViolateOpenBottleAreaRecordReq.java
@@ -27,6 +27,11 @@ public class FenceViolateOpenBottleAreaRecordReq {
      */
     private String qrCode;
 
+    /**
+     * 箱码
+     */
+    private String boxCode;
+
     /**
      * 开瓶开始时间
      */
diff --git a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/resp/BatchFenceViolationAppealResp.java b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/resp/BatchFenceViolationAppealResp.java
new file mode 100644
index 0000000000..8434d90e13
--- /dev/null
+++ b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/resp/BatchFenceViolationAppealResp.java
@@ -0,0 +1,51 @@
+package com.intelliquor.cloud.shop.common.model.resp;
+
+import lombok.Getter;
+import lombok.AllArgsConstructor;
+import lombok.Builder;
+import lombok.NoArgsConstructor;
+
+import java.io.Serializable;
+import java.util.List;
+
+/**
+ * 批量违约申诉响应DTO
+ *
+ * <AUTHOR>
+ * @since 2024-01-01
+ */
+@Getter
+@AllArgsConstructor
+public class BatchFenceViolationAppealResp implements Serializable {
+
+    private static final long serialVersionUID = 1L;
+
+    /**
+     * 申诉成功的违约记录ID列表
+     */
+    private List<Integer> successIds;
+
+    /**
+     * 申诉失败的违约记录详情
+     */
+    private List<FailedAppealItem> failedItems;
+
+    /**
+     * 申诉失败详情
+     */
+    @Getter
+    @Builder
+    @AllArgsConstructor
+    @NoArgsConstructor
+    public static class FailedAppealItem {
+        /**
+         * 违约记录ID
+         */
+        private Integer violationOfAgreementInfoId;
+
+        /**
+         * 失败原因
+         */
+        private String failureReason;
+    }
+}
\ No newline at end of file
diff --git a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/BatchAppealTransactionalService.java b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/BatchAppealTransactionalService.java
new file mode 100644
index 0000000000..54945e3191
--- /dev/null
+++ b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/BatchAppealTransactionalService.java
@@ -0,0 +1,21 @@
+package com.intelliquor.cloud.shop.common.service;
+
+import com.intelliquor.cloud.shop.common.model.FenceViolationOfAgreementAppealModel;
+
+import java.util.List;
+
+/**
+ * 批量申诉事务处理服务，仅负责持久化层面的批量更新与插入。
+ * 该接口方法应保证事务原子性，避免部分成功导致的数据不一致。
+ */
+public interface BatchAppealTransactionalService {
+
+    /**
+     * 执行批量申诉持久化操作。
+     *
+     * @param violationOfAgreementInfoIds 待更新申诉状态的违约记录ID列表
+     * @param appealsToInsert   待批量插入的申诉实体集合
+     */
+    void persistBatchAppealOperations(List<Integer> violationOfAgreementInfoIds,
+                                      List<FenceViolationOfAgreementAppealModel> appealsToInsert);
+}
diff --git a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/FenceViolateOpenBottleAreaRecordService.java b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/FenceViolateOpenBottleAreaRecordService.java
index fedde00ff2..05bff0b9aa 100644
--- a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/FenceViolateOpenBottleAreaRecordService.java
+++ b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/FenceViolateOpenBottleAreaRecordService.java
@@ -3,7 +3,9 @@ package com.intelliquor.cloud.shop.common.service;
 import com.baomidou.mybatisplus.extension.service.IService;
 import com.intelliquor.cloud.shop.common.model.FenceViolateOpenBottleAreaRecordModel;
 import com.intelliquor.cloud.shop.common.model.FenceViolationOfAgreementAppealModel;
+import com.intelliquor.cloud.shop.common.model.req.BatchAppealServiceReq;
 import com.intelliquor.cloud.shop.common.model.req.FenceViolateOpenBottleAreaRecordReq;
+import com.intelliquor.cloud.shop.common.model.resp.BatchFenceViolationAppealResp;
 import com.intelliquor.cloud.shop.common.model.resp.FenceViolateOpenBottleAreaRecordResp;
 
 import java.util.List;
@@ -33,6 +35,14 @@ public interface FenceViolateOpenBottleAreaRecordService extends IService<FenceV
      */
     void addAppeal(FenceViolationOfAgreementAppealModel fenceViolationOfAgreementAppealModel);
 
+    /**
+     * 批量发起申诉
+     *
+     * @param batchAppealReq 批量申诉参数
+     * @return 批量申诉结果
+     */
+    BatchFenceViolationAppealResp batchAddAppeal(BatchAppealServiceReq batchAppealReq);
+
     /**
      * 处理异地类型数据
      */
diff --git a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/impl/BatchAppealTransactionalServiceImpl.java b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/impl/BatchAppealTransactionalServiceImpl.java
new file mode 100644
index 0000000000..2982cc7047
--- /dev/null
+++ b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/impl/BatchAppealTransactionalServiceImpl.java
@@ -0,0 +1,61 @@
+package com.intelliquor.cloud.shop.common.service.impl;
+
+import com.baomidou.mybatisplus.core.toolkit.Wrappers;
+import com.intelliquor.cloud.shop.common.dao.FenceViolateOpenBottleAreaRecordDao;
+import com.intelliquor.cloud.shop.common.dao.FenceViolationOfAgreementAppealDao;
+import com.intelliquor.cloud.shop.common.exception.BusinessException;
+import com.intelliquor.cloud.shop.common.model.FenceViolateOpenBottleAreaRecordModel;
+import com.intelliquor.cloud.shop.common.model.FenceViolationOfAgreementAppealModel;
+import com.intelliquor.cloud.shop.common.service.BatchAppealTransactionalService;
+import lombok.RequiredArgsConstructor;
+import lombok.extern.slf4j.Slf4j;
+import org.springframework.stereotype.Service;
+import org.springframework.transaction.annotation.Transactional;
+
+import java.util.Date;
+import java.util.List;
+
+/**
+ * 批量申诉事务性服务实现，只处理数据库持久化逻辑。
+ */
+@Slf4j
+@Service
+@RequiredArgsConstructor
+public class BatchAppealTransactionalServiceImpl implements BatchAppealTransactionalService {
+
+    private final FenceViolateOpenBottleAreaRecordDao fenceViolateOpenBottleAreaRecordDao;
+    private final FenceViolationOfAgreementAppealDao fenceViolationOfAgreementAppealDao;
+
+    @Override
+    @Transactional(rollbackFor = Exception.class)
+    public void persistBatchAppealOperations(List<Integer> violationOfAgreementInfoIds, List<FenceViolationOfAgreementAppealModel> appealsToInsert) {
+        log.info("[BatchAppealTx] 开始处理申诉事务, ids={}, insertSize={}", violationOfAgreementInfoIds.size(), appealsToInsert.size());
+
+        // 1. 更新异地违约记录的申诉状态
+        if (!violationOfAgreementInfoIds.isEmpty()) {
+            int updateCount = fenceViolateOpenBottleAreaRecordDao.update(null,
+                    Wrappers.<FenceViolateOpenBottleAreaRecordModel>lambdaUpdate()
+                            .set(FenceViolateOpenBottleAreaRecordModel::getAppealStatus, 1)
+                            .set(FenceViolateOpenBottleAreaRecordModel::getUpdateTime, new Date())
+                            .in(FenceViolateOpenBottleAreaRecordModel::getId, violationOfAgreementInfoIds));
+
+            if (updateCount != violationOfAgreementInfoIds.size()) {
+                log.error("[BatchAppealTx] 批量更新申诉状态失败, ids={}", violationOfAgreementInfoIds);
+                throw new BusinessException("批量更新违约记录申诉状态失败");
+            }
+            log.debug("[BatchAppealTx] 更新申诉状态完成, rows={}", updateCount);
+        }
+
+        // 2. 批量插入申诉记录
+        if (!appealsToInsert.isEmpty()) {
+            int insertCount = fenceViolationOfAgreementAppealDao.batchInsert(appealsToInsert);
+            if (insertCount != appealsToInsert.size()) {
+                log.error("[BatchAppealTx] 批量插入记录数量不一致 expect={} actual={}", appealsToInsert.size(), insertCount);
+                throw new BusinessException("批量插入申诉记录失败");
+            }
+            log.debug("[BatchAppealTx] 插入申诉记录完成 rows={}", insertCount);
+        }
+
+        log.info("[BatchAppealTx] 申诉事务处理完成");
+    }
+}
diff --git a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/impl/FenceViolateOpenBottleAreaRecordServiceImpl.java b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/impl/FenceViolateOpenBottleAreaRecordServiceImpl.java
index 6354e5b47d..b0727ca3e8 100644
--- a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/impl/FenceViolateOpenBottleAreaRecordServiceImpl.java
+++ b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/impl/FenceViolateOpenBottleAreaRecordServiceImpl.java
@@ -13,18 +13,23 @@ import com.intelliquor.cloud.shop.common.dao.*;
 import com.intelliquor.cloud.shop.common.enums.*;
 import com.intelliquor.cloud.shop.common.exception.BusinessException;
 import com.intelliquor.cloud.shop.common.model.*;
+import com.intelliquor.cloud.shop.common.model.req.BatchAppealServiceReq;
 import com.intelliquor.cloud.shop.common.model.req.FenceViolateOpenBottleAreaRecordReq;
 import com.intelliquor.cloud.shop.common.model.resp.ActivityExceptionComplaintConfigResp;
+import com.intelliquor.cloud.shop.common.model.resp.BatchFenceViolationAppealResp;
 import com.intelliquor.cloud.shop.common.model.resp.FenceViolateOpenBottleAreaRecordResp;
 import com.intelliquor.cloud.shop.common.model.resp.RewardResp;
 import com.intelliquor.cloud.shop.common.service.ActivityRewardExceptionRecordService;
+import com.intelliquor.cloud.shop.common.service.BatchAppealTransactionalService;
 import com.intelliquor.cloud.shop.common.service.FenceViolateOpenBottleAreaRecordService;
 import com.intelliquor.cloud.shop.common.service.IDealerContractRelCommonService;
-import com.intelliquor.cloud.shop.common.service.TerminalBrokerTaskRatioConfigCommonService;
+import lombok.Getter;
 import lombok.RequiredArgsConstructor;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.lang3.StringUtils;
+import org.springframework.beans.BeanUtils;
 import org.springframework.data.redis.core.RedisTemplate;
+import org.springframework.data.redis.core.script.DefaultRedisScript;
 import org.springframework.stereotype.Service;
 import org.springframework.transaction.annotation.Transactional;
 
@@ -89,6 +94,9 @@ public class FenceViolateOpenBottleAreaRecordServiceImpl extends ServiceImpl<Fen
     @Resource
     private IDealerContractRelCommonService dealerContractRelCommonService;
 
+    @Resource
+    private BatchAppealTransactionalService batchAppealTransactionalService;
+
     @Override
     public List<FenceViolateOpenBottleAreaRecordResp> getPageList(int page, int limit, FenceViolateOpenBottleAreaRecordReq fenceViolateOpenBottleAreaRecordReq) {
         PageHelper.startPage(page, limit);
@@ -572,6 +580,313 @@ public class FenceViolateOpenBottleAreaRecordServiceImpl extends ServiceImpl<Fen
         fenceViolationOfAgreementAppealDao.insert(model);
     }
 
+    @Override
+    public BatchFenceViolationAppealResp batchAddAppeal(BatchAppealServiceReq batchAppealReq) {
+        log.info("批量申诉开始, 违约记录ID列表：{}, 申诉人：{}", batchAppealReq.getViolationOfAgreementInfoIds(), batchAppealReq.getCreateUserName());
+
+        // 获取分布式锁，防止连点重复提交
+        String lockKey = String.format(RedisConstant.BATCH_APPEAL_LOCK_KEY, batchAppealReq.getCreateUser());
+        // 使用随机 token 作为锁 value，防止误删其它请求持有的锁
+        String lockToken = UUID.randomUUID().toString();
+        boolean lockAcquired = false;
+
+        try {
+            // 1. 尝试获取分布式锁，避免同一用户重复提交
+            log.info("尝试获取批量申诉分布式锁，lockKey={}", lockKey);
+            lockAcquired = Boolean.TRUE.equals(
+                redisTemplate.opsForValue().setIfAbsent(lockKey, lockToken, RedisConstant.REDIS_TIME_OUT_TEN, TimeUnit.MINUTES));
+            log.info("批量申诉获取分布式锁结果={}, lockKey={}", lockAcquired, lockKey);
+            if (!lockAcquired) {
+                log.warn("批量申诉获取分布式锁失败，用户[{}]可能正在进行申诉操作", batchAppealReq.getCreateUserName());
+                throw new BusinessException("您的申诉正在处理中，请勿重复提交");
+            }
+            log.info("成功获取批量申诉分布式锁");
+
+            // 2. 基础参数校验
+            validateBatchAppealRequest(batchAppealReq);
+
+            // 3. 批量获取申诉所需的所有先决条件数据
+            AppealPrerequisites prerequisites = fetchAppealPrerequisites(batchAppealReq.getViolationOfAgreementInfoIds());
+
+            // 4. 逐条处理和校验申诉，分离出成功和失败的记录
+            ValidationResult validationResult = processIndividualAppeals(batchAppealReq, prerequisites);
+
+            // 5. 如果有业务验证失败的记录，记录详细信息
+            if (!validationResult.getFailedItems().isEmpty()) {
+                log.warn("批量申诉预检查发现失败项，数量：{}，详情：{}",
+                    validationResult.getFailedItems().size(),
+                    validationResult.getFailedItems().stream()
+                        .map(item -> String.format("ID[%d]:%s", item.getViolationOfAgreementInfoId(), item.getFailureReason()))
+                        .collect(Collectors.joining(", ")));
+            }
+
+            // 6. 对验证通过的记录执行数据库操作
+            if (!validationResult.getSuccessIds().isEmpty()) {
+                log.info("开始执行数据库操作，预期成功数量：{}", validationResult.getSuccessIds().size());
+                performDatabaseOperations(validationResult);
+            }
+
+            // 7. 构建并返回最终响应
+            log.info("批量申诉完成, 最终成功数量：{}, 最终失败数量：{}",
+                    validationResult.getSuccessIds().size(), validationResult.getFailedItems().size());
+
+            return new BatchFenceViolationAppealResp(validationResult.getSuccessIds(), validationResult.getFailedItems());
+
+        } catch (BusinessException e) {
+            log.error("批量申诉业务异常：{}", e.getMessage());
+            throw e;
+        } catch (Exception e) {
+            log.error("批量申诉发生未预期异常：{}", e.getMessage(), e);
+            throw new BusinessException("系统繁忙，请稍后重试");
+        } finally {
+            // 释放分布式锁
+            if (lockAcquired) {
+                try {
+                    // Lua 脚本：只有当 value 与 token 一致时才删除，确保原子操作
+                    String unlockScript = "if redis.call('get', KEYS[1]) == ARGV[1] then " +
+                            "return redis.call('del', KEYS[1]) else return 0 end";
+                    Long result = redisTemplate.execute(
+                            new DefaultRedisScript<>(unlockScript, Long.class),
+                            java.util.Collections.singletonList(lockKey), lockToken);
+                    log.debug("释放批量申诉分布式锁结果={}, lockKey={}", result, lockKey);
+                } catch (Exception e) {
+                    log.warn("释放批量申诉分布式锁失败, lockKey={}", lockKey, e);
+                }
+            }
+        }
+    }
+
+    /**
+     * 校验批量申诉的基础请求参数
+     */
+    private void validateBatchAppealRequest(BatchAppealServiceReq req) {
+        if (CollectionUtils.isEmpty(req.getViolationOfAgreementInfoIds())) {
+            throw new BusinessException("违约记录ID列表不能为空");
+        }
+        if (req.getViolationOfAgreementInfoIds().size() > 100) {
+            throw new BusinessException("批量申诉数量不能超过100条");
+        }
+        if (StringUtils.isBlank(req.getReason())) {
+            throw new BusinessException("申诉说明不能为空");
+        }
+        if (StringUtils.isBlank(req.getAppealFileUrl())) {
+            throw new BusinessException("申诉附件不能为空");
+        }
+    }
+
+    /**
+     * 批量获取申诉所需的所有数据，以提高效率
+     * @param violationIds 违约记录ID列表
+     * @return 包含所有预查询数据的封装对象
+     */
+    private AppealPrerequisites fetchAppealPrerequisites(List<Integer> violationIds) {
+        // 批量查询违约记录
+        List<FenceViolateOpenBottleAreaRecordModel> fenceRecords = lambdaQuery()
+                .in(FenceViolateOpenBottleAreaRecordModel::getId, violationIds)
+                .list();
+        Map<Integer, FenceViolateOpenBottleAreaRecordModel> fenceRecordMap = fenceRecords.stream()
+                .collect(Collectors.toMap(record -> record.getId().intValue(), record -> record));
+
+        // 批量查询现有申诉记录
+        List<FenceViolationOfAgreementAppealModel> existingAppeals = fenceViolationOfAgreementAppealDao.selectList(
+                Wrappers.<FenceViolationOfAgreementAppealModel>lambdaQuery()
+                        .in(FenceViolationOfAgreementAppealModel::getViolationOfAgreementInfoId, violationIds)
+                        .eq(FenceViolationOfAgreementAppealModel::getIsDelete, IsDelete.DELETE_FALSE.getCode())
+        );
+        Set<Integer> existingAppealViolationIds = existingAppeals.stream()
+                .map(FenceViolationOfAgreementAppealModel::getViolationOfAgreementInfoId)
+                .collect(Collectors.toSet());
+
+        // 批量查询经销商申诉权限配置
+        Set<String> dealerCodes = fenceRecords.stream()
+                .map(FenceViolateOpenBottleAreaRecordModel::getViolateDealerCode)
+                .filter(StringUtils::isNotBlank)
+                .collect(Collectors.toSet());
+        Map<String, ActivityExceptionComplaintConfigResp> dealerConfigMap = new HashMap<>();
+        if (!dealerCodes.isEmpty()) {
+            List<ActivityExceptionComplaintConfigModel> configs = activityExceptionComplaintConfigDao.selectList(
+                    Wrappers.<ActivityExceptionComplaintConfigModel>lambdaQuery()
+                            .in(ActivityExceptionComplaintConfigModel::getDealerCode, dealerCodes)
+                            .eq(ActivityExceptionComplaintConfigModel::getDeleteStatus, 0)
+            );
+            dealerConfigMap = configs.stream().collect(Collectors.toMap(
+                    ActivityExceptionComplaintConfigModel::getDealerCode,
+                    config -> {
+                        ActivityExceptionComplaintConfigResp resp = new ActivityExceptionComplaintConfigResp();
+                        BeanUtils.copyProperties(config, resp);
+                        return resp;
+                    }
+            ));
+        }
+
+        return new AppealPrerequisites(fenceRecordMap, existingAppealViolationIds, dealerConfigMap);
+    }
+
+    /**
+     * 遍历并处理每条申诉，进行业务校验并分类
+     * @param batchAppealReq 原始请求
+     * @param prerequisites 预查询数据
+     * @return 包含成功和失败列表的验证结果
+     */
+    private ValidationResult processIndividualAppeals(BatchAppealServiceReq batchAppealReq, AppealPrerequisites prerequisites) {
+        ValidationResult result = new ValidationResult();
+        Date currentTime = new Date();
+
+        for (Integer violationId : batchAppealReq.getViolationOfAgreementInfoIds()) {
+            FenceViolateOpenBottleAreaRecordModel fenceRecord = prerequisites.getFenceRecordMap().get(violationId);
+            String failureReason = getFailureReason(violationId, fenceRecord, prerequisites);
+
+            if (failureReason != null) {
+                result.addFailure(violationId, failureReason);
+                log.debug("违约记录[{}]验证失败：{}", violationId, failureReason);
+            } else {
+                result.addSuccess(violationId, createAppealModel(batchAppealReq, fenceRecord, currentTime));
+                log.debug("违约记录[{}]验证通过", violationId);
+            }
+        }
+
+        log.info("业务验证完成，通过：{}条，失败：{}条", result.getSuccessIds().size(), result.getFailedItems().size());
+        return result;
+    }
+
+    /**
+     * 对单条申诉记录进行有效性检查
+     */
+    private String getFailureReason(Integer violationId, FenceViolateOpenBottleAreaRecordModel fenceRecord, AppealPrerequisites prerequisites) {
+        if (Objects.isNull(fenceRecord)) {
+            return "未查询到违约记录";
+        }
+        if (!Objects.equals(fenceRecord.getAppealStatus(), 0)) {
+            return "违约记录已处理";
+        }
+        if (prerequisites.getExistingAppealViolationIds().contains(violationId)) {
+            return "该记录已有申诉";
+        }
+        ActivityExceptionComplaintConfigResp config = prerequisites.getDealerConfigMap().get(fenceRecord.getViolateDealerCode());
+        if (Objects.isNull(config)) {
+            return "经销商未配置申诉权限";
+        }
+        if (!Objects.equals(config.getUseStatus(), 0)) {
+            return "经销商申诉权限已停用";
+        }
+        return null; // 验证通过
+    }
+
+    /**
+     * 创建待插入的申诉实体对象
+     */
+    private FenceViolationOfAgreementAppealModel createAppealModel(BatchAppealServiceReq req, FenceViolateOpenBottleAreaRecordModel record, Date time) {
+        FenceViolationOfAgreementAppealModel appeal = new FenceViolationOfAgreementAppealModel();
+        appeal.setViolationOfAgreementInfoId(record.getId().intValue());
+        appeal.setReason(req.getReason());
+        appeal.setAppealFileUrl(req.getAppealFileUrl());
+        appeal.setCreateTime(time);
+        appeal.setCreateUser(req.getCreateUser());
+        appeal.setCreateUserName(req.getCreateUserName());
+        appeal.setCompanyId(req.getCompanyId());
+        appeal.setAppealUserTel(req.getAppealUserTel());
+        appeal.setIsDelete(IsDelete.DELETE_FALSE.getCode());
+        appeal.setStatus(0); // 申诉中
+        appeal.setAppealUser(req.getCreateUser());
+        appeal.setAppealUserName(req.getCreateUserName());
+        return appeal;
+    }
+
+    /**
+     * 执行数据库批量操作
+     */
+    private void performDatabaseOperations(ValidationResult validationResult) {
+        if (validationResult.getSuccessIds().isEmpty()) {
+            log.info("没有需要执行的数据库操作");
+            return;
+        }
+
+        try {
+            log.info("开始执行数据库事务操作，涉及记录数量：{}", validationResult.getSuccessIds().size());
+            // 通过注入的自身代理调用事务方法
+            batchAppealTransactionalService.persistBatchAppealOperations(
+                    validationResult.getSuccessIds(),
+                    validationResult.getAppealsToInsert()
+            );
+            log.info("数据库事务操作成功完成");
+        } catch (Exception e) {
+            log.error("批量申诉数据库操作失败，错误类型：{}，错误消息：{}", e.getClass().getSimpleName(), e.getMessage(), e);
+
+            // 分析异常类型，提供更精确的错误信息
+            String detailedErrorMsg = analyzeExceptionType(e);
+
+            // 如果事务失败，将所有成功的记录移至失败列表
+            validationResult.moveAllSuccessToFailed("数据库操作失败：" + detailedErrorMsg);
+
+            log.warn("由于数据库操作失败，已将{}条原本验证成功的记录标记为失败", validationResult.getFailedItems().size());
+        }
+    }
+
+    /**
+     * 分析异常类型，返回更精确的错误描述
+     */
+    private String analyzeExceptionType(Exception e) {
+        String errorMsg = e.getMessage();
+        String exceptionType = e.getClass().getSimpleName();
+
+        if (errorMsg != null) {
+            if (errorMsg.contains("Duplicate entry") || errorMsg.contains("duplicate key")) {
+                return "数据重复冲突，可能存在并发操作";
+            } else if (errorMsg.contains("Lock wait timeout") || errorMsg.contains("Deadlock")) {
+                return "数据库锁等待超时，请稍后重试";
+            } else if (errorMsg.contains("Connection") || errorMsg.contains("timeout")) {
+                return "数据库连接异常";
+            } else if (errorMsg.contains("constraint") || errorMsg.contains("foreign key")) {
+                return "数据约束违反";
+            }
+        }
+
+        // 记录具体的异常类型，方便后续分析
+        log.error("未分类的数据库异常类型：{}，消息：{}", exceptionType, errorMsg);
+        return String.format("系统异常(%s)", exceptionType);
+    }
+
+    /**
+     * 封装申诉所需先决数据的内部类
+     */
+    @Getter
+    @RequiredArgsConstructor
+    private static class AppealPrerequisites {
+        private final Map<Integer, FenceViolateOpenBottleAreaRecordModel> fenceRecordMap;
+        private final Set<Integer> existingAppealViolationIds;
+        private final Map<String, ActivityExceptionComplaintConfigResp> dealerConfigMap;
+
+    }
+
+    /**
+     * 封装验证结果的内部类
+     */
+    @Getter
+    @RequiredArgsConstructor
+    private static class ValidationResult {
+        private final List<Integer> successIds = new ArrayList<>();
+        private final List<BatchFenceViolationAppealResp.FailedAppealItem> failedItems = new ArrayList<>();
+        private final List<FenceViolationOfAgreementAppealModel> appealsToInsert = new ArrayList<>();
+
+        void addSuccess(Integer violationId, FenceViolationOfAgreementAppealModel appealModel) {
+            successIds.add(violationId);
+            appealsToInsert.add(appealModel);
+        }
+
+        void addFailure(Integer violationId, String reason) {
+            failedItems.add(new BatchFenceViolationAppealResp.FailedAppealItem(violationId, reason));
+        }
+
+        void moveAllSuccessToFailed(String reason) {
+            for (Integer id : successIds) {
+                failedItems.add(new BatchFenceViolationAppealResp.FailedAppealItem(id, reason));
+            }
+            successIds.clear();
+            appealsToInsert.clear();
+        }
+
+    }
+
     @Override
     public void dealFenceTypeData() {
         //1. 查询出 类型为异地 且异地类型为空的数据
@@ -842,7 +1157,7 @@ public class FenceViolateOpenBottleAreaRecordServiceImpl extends ServiceImpl<Fen
         if (CollectionUtils.isEmpty(exceptionRecords)) {
             return;
         }
-        
+
         exceptionRecords.forEach(record -> {
             record.setFenceStatus(1);
             record.setRemark(org.apache.commons.lang3.StringUtils.abbreviate("开瓶违约：" + errorMsg, 1900));
@@ -850,7 +1165,3 @@ public class FenceViolateOpenBottleAreaRecordServiceImpl extends ServiceImpl<Fen
         });
     }
 }
-
-
-
-
diff --git a/shop-common/src/main/resources/mapper/FenceViolateOpenBottleAreaRecordDao.xml b/shop-common/src/main/resources/mapper/FenceViolateOpenBottleAreaRecordDao.xml
index a146e28590..5406311804 100644
--- a/shop-common/src/main/resources/mapper/FenceViolateOpenBottleAreaRecordDao.xml
+++ b/shop-common/src/main/resources/mapper/FenceViolateOpenBottleAreaRecordDao.xml
@@ -39,6 +39,9 @@
         <if test="qrCode != null and qrCode != ''">
             AND a.qr_code = #{qrCode}
         </if>
+        <if test="boxCode != null and boxCode != ''">
+            AND a.box_code = #{boxCode}
+        </if>
         <if test="fenceType != null">
             AND a.fence_type = #{fenceType}
         </if>
diff --git a/shop-job/src/main/java/com/intelliquor/cloud/shop/job/model/req/TerminalVisitRecordReq.java b/shop-job/src/main/java/com/intelliquor/cloud/shop/job/model/req/TerminalVisitRecordReq.java
index 0acd55abfc..bf8d6d6f46 100644
--- a/shop-job/src/main/java/com/intelliquor/cloud/shop/job/model/req/TerminalVisitRecordReq.java
+++ b/shop-job/src/main/java/com/intelliquor/cloud/shop/job/model/req/TerminalVisitRecordReq.java
@@ -124,5 +124,18 @@ public class TerminalVisitRecordReq {
      */
     private Integer isMember;
 
+    /**
+     * 所属运营中心(0国标运营中心1高端酒运营中心)
+     */
+    private Integer deptCode;
+
+    /**
+     * 审核状态筛选条件
+     * null-全部（默认值）
+     * 1-未审核（已提交待审核）
+     * 2-合格（审核通过）
+     * 3-不合格（审核不通过）
+     */
+    private Integer auditStatus;
 
 }
diff --git a/shop-job/src/main/resources/mapper/TerminalVisitRecordMapper.xml b/shop-job/src/main/resources/mapper/TerminalVisitRecordMapper.xml
index b2137cdecf..f4cefaa2f3 100644
--- a/shop-job/src/main/resources/mapper/TerminalVisitRecordMapper.xml
+++ b/shop-job/src/main/resources/mapper/TerminalVisitRecordMapper.xml
@@ -558,6 +558,9 @@
         <if test="checkList == 1">
             and r.status >= 1
         </if>
+        <if test="auditStatus != null">
+            and r.status = #{auditStatus}
+        </if>
         order by r.id desc
     </select>
 
diff --git a/shop-terminal/src/main/java/com/intelliquor/cloud/shop/terminal/controller/applet/ViolateOpenBottleAreaRecordAppletController.java b/shop-terminal/src/main/java/com/intelliquor/cloud/shop/terminal/controller/applet/ViolateOpenBottleAreaRecordAppletController.java
index feff48c71f..ad47aee403 100644
--- a/shop-terminal/src/main/java/com/intelliquor/cloud/shop/terminal/controller/applet/ViolateOpenBottleAreaRecordAppletController.java
+++ b/shop-terminal/src/main/java/com/intelliquor/cloud/shop/terminal/controller/applet/ViolateOpenBottleAreaRecordAppletController.java
@@ -5,15 +5,21 @@ import com.github.pagehelper.PageInfo;
 import com.intelliquor.cloud.shop.common.exception.BusinessException;
 import com.intelliquor.cloud.shop.common.exception.RestResponse;
 import com.intelliquor.cloud.shop.common.model.FenceViolationOfAgreementAppealModel;
+import com.intelliquor.cloud.shop.common.model.TerminalAccountManagerModel;
 import com.intelliquor.cloud.shop.common.model.UserContext;
+import com.intelliquor.cloud.shop.common.model.req.BatchAppealServiceReq;
+import com.intelliquor.cloud.shop.common.model.req.BatchFenceViolationAppealReq;
 import com.intelliquor.cloud.shop.common.model.req.FenceViolateOpenBottleAreaRecordReq;
+import com.intelliquor.cloud.shop.common.model.resp.BatchFenceViolationAppealResp;
 import com.intelliquor.cloud.shop.common.model.resp.FenceViolateOpenBottleAreaRecordResp;
 import com.intelliquor.cloud.shop.common.service.FenceViolateOpenBottleAreaRecordService;
 import com.intelliquor.cloud.shop.terminal.service.IFenceViolateOpenBottleAreaRecordService;
 import lombok.RequiredArgsConstructor;
+import lombok.extern.slf4j.Slf4j;
 import org.springframework.web.bind.annotation.*;
 
 import javax.annotation.Resource;
+import javax.validation.Valid;
 import java.util.List;
 import java.util.Objects;
 
@@ -22,6 +28,7 @@ import java.util.Objects;
  *
  * @module 国台领袖会
  */
+@Slf4j
 @RestController
 @RequiredArgsConstructor
 @RequestMapping("/terminalApplet/violateOpenBottleAreaRecord")
@@ -53,6 +60,8 @@ public class ViolateOpenBottleAreaRecordAppletController {
         try {
             // 小程序端获取违约记录 过滤掉无需处理的白名单数据
             fenceViolateOpenBottleAreaRecordReq.setShowDealStatus("1");
+            // 小程序端不使用经销商名称搜索，由前端控制改为箱码搜索（SZYXPT-2138）
+            fenceViolateOpenBottleAreaRecordReq.setViolateDealerName(null);
             List<FenceViolateOpenBottleAreaRecordResp> list = iFenceViolateOpenBottleAreaRecordService.getTerminalPageList(page, limit, id, type, dealerIdOrShopId, fenceViolateOpenBottleAreaRecordReq);
             PageInfo<FenceViolateOpenBottleAreaRecordResp> pageInfo = new PageInfo<>(list);
             return RestResponse.success("查询成功", pageInfo);
@@ -93,4 +102,59 @@ public class ViolateOpenBottleAreaRecordAppletController {
         fenceViolateOpenBottleAreaRecordService.addAppeal(fenceViolationOfAgreementAppealModel);
         return RestResponse.success("申诉成功");
     }
+
+    /**
+     * 批量发起申诉
+     *
+     * @param request 批量申诉请求
+     * @return Response
+     */
+    @PostMapping("batchAddAppeal")
+    public RestResponse<BatchFenceViolationAppealResp> batchAddAppeal(@RequestBody @Valid BatchFenceViolationAppealReq request) {
+        try {
+            // 构建Service层请求参数
+            BatchAppealServiceReq serviceReq = buildBatchAppealServiceReq(request);
+            
+            // 调用批量申诉服务
+            BatchFenceViolationAppealResp response = fenceViolateOpenBottleAreaRecordService.batchAddAppeal(serviceReq);
+            
+            // 根据结果返回相应消息
+            return buildBatchAppealResponse(response);
+        } catch (Exception e) {
+            // 这个catch块现在只用于捕获未预料到的系统级异常，例如数据库连接失败
+            log.error("批量申诉时发生未知系统错误: {}", e.getMessage(), e);
+            return RestResponse.error("批量申诉失败，系统繁忙，请稍后重试");
+        }
+    }
+
+    /**
+     * 构建批量申诉Service层请求参数
+     */
+    private BatchAppealServiceReq buildBatchAppealServiceReq(BatchFenceViolationAppealReq request) {
+        // 获取当前用户信息（只调用一次）
+        TerminalAccountManagerModel terminalModel = userContext.getTerminalModel();
+        
+        return BatchAppealServiceReq.builder()
+                .violationOfAgreementInfoIds(request.getViolationOfAgreementInfoIds())
+                .reason(request.getReason())
+                .appealFileUrl(request.getAppealFileUrl())
+                .createUser(terminalModel.getId())
+                .createUserName(terminalModel.getName())
+                .companyId(terminalModel.getCompanyId())
+                .appealUserTel(terminalModel.getPhone())
+                .build();
+    }
+
+    /**
+     * 构建批量申诉响应结果
+     */
+    private RestResponse<BatchFenceViolationAppealResp> buildBatchAppealResponse(BatchFenceViolationAppealResp response) {
+        if (response.getFailedItems().isEmpty()) {
+            return RestResponse.success("批量申诉全部成功", response);
+        } else if (response.getSuccessIds().isEmpty()) {
+            return RestResponse.success("批量申诉全部失败", response);
+        } else {
+            return RestResponse.success("批量申诉部分成功", response);
+        }
+    }
 }
diff --git a/shop-terminal/src/main/java/com/intelliquor/cloud/shop/terminal/model/req/TerminalVisitRecordReq.java b/shop-terminal/src/main/java/com/intelliquor/cloud/shop/terminal/model/req/TerminalVisitRecordReq.java
index b64d6b42ac..3efce773b8 100644
--- a/shop-terminal/src/main/java/com/intelliquor/cloud/shop/terminal/model/req/TerminalVisitRecordReq.java
+++ b/shop-terminal/src/main/java/com/intelliquor/cloud/shop/terminal/model/req/TerminalVisitRecordReq.java
@@ -129,4 +129,13 @@ public class TerminalVisitRecordReq {
      * 所属运营中心(0国标运营中心1高端酒运营中心)
      */
     private Integer deptCode;
+
+    /**
+     * 审核状态筛选条件
+     * null-全部（默认值）
+     * 1-未审核（已提交待审核）
+     * 2-合格（审核通过）
+     * 3-不合格（审核不通过）
+     */
+    private Integer auditStatus;
 }
diff --git a/shop-terminal/src/main/resources/mapper/TerminalVisitRecordMapper.xml b/shop-terminal/src/main/resources/mapper/TerminalVisitRecordMapper.xml
index 4dc5748154..4630d4d6df 100644
--- a/shop-terminal/src/main/resources/mapper/TerminalVisitRecordMapper.xml
+++ b/shop-terminal/src/main/resources/mapper/TerminalVisitRecordMapper.xml
@@ -577,6 +577,9 @@
         <if test="checkList == 1">
             and r.status >= 1
         </if>
+        <if test="auditStatus != null">
+            and r.status = #{auditStatus}
+        </if>
         order by r.id desc
     </select>
 
