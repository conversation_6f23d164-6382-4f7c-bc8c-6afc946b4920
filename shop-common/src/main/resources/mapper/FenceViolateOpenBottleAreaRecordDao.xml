<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.intelliquor.cloud.shop.common.dao.FenceViolateOpenBottleAreaRecordDao">
    <select id="selectPageList"
            parameterType="com.intelliquor.cloud.shop.common.model.req.FenceViolateOpenBottleAreaRecordReq"
            resultType="com.intelliquor.cloud.shop.common.model.resp.FenceViolateOpenBottleAreaRecordResp">
        SELECT a.*,
        violate.id as violateDealerId,
        violate.dealer_name as violateDealerName,
        infringed.id as InfringedDealerId,
        infringed.dealer_name as InfringedDealerName
        FROM t_fence_violate_open_bottle_area_record a
        LEFT JOIN
        t_cloud_dealer_info violate
        ON
        violate.dealer_code = a.violate_dealer_code
        LEFT JOIN
        t_cloud_dealer_info infringed
        ON
        infringed.dealer_code = a.Infringed_dealer_code
        LEFT JOIN t_activity_reward_record b ON a.id = b.origin_id
        WHERE a.is_delete = 0
        AND violate.account_type IN (1, 2, 6)
        AND b.event_type = 231
        AND ((b.sys_state = 3 and b.integral <![CDATA[<>]]> 0 )
                 OR (b.integral = 0 and (a.open_bottle_reward_flag = 0 OR a.sale_reward_deduct_flag = 1
                                             OR (a.open_bottle_reward_flag = 1 AND a.open_bottle_reward_rate <![CDATA[<]]> 1))))
        <if test="violateDealerName != null and violateDealerName != ''">
            AND violate.dealer_name LIKE CONCAT('%', #{violateDealerName}, '%')
        </if>
        <if test="violateDealerCode != null and violateDealerCode != ''">
            AND a.violate_dealer_code = #{violateDealerCode}
        </if>
        <if test="goodsName != null and goodsName != ''">
            AND a.goods_name LIKE CONCAT('%', #{goodsName}, '%')
        </if>
        <if test="qrCode != null and qrCode != ''">
            AND a.qr_code = #{qrCode}
        </if>
        <if test="boxCode != null and boxCode != ''">
            AND a.box_code = #{boxCode}
        </if>
        <if test="fenceType != null">
            AND a.fence_type = #{fenceType}
        </if>
        <if test="drawArea != null and drawArea != ''">
            AND a.draw_area LIKE CONCAT('%', #{drawArea}, '%')
        </if>
        <if test="drawStartTime != null and drawStartTime != ''">
            AND a.create_time &gt;= #{drawStartTime}
        </if>
        <if test="drawEndTime != null and drawEndTime != ''">
            AND a.create_time &lt;= #{drawEndTime}
        </if>
        <if test="startId != null">
            AND a.id &lt; #{startId}
        </if>
        <if test="id != null">
            AND a.id = #{id}
        </if>
        <if test='showDealStatus != null and showDealStatus == "1"'>
            AND a.deal_status != 3
        </if>
        <if test="province != null and province != ''">
            AND a.province = #{province}
        </if>
        <if test="city != null and city != ''">
            AND a.city = #{city}
        </if>
        <if test="district != null and district != ''">
            AND a.district = #{district}
        </if>
        <if test="dealerCodeList != null and dealerCodeList.size() != 0">
            AND a.violate_dealer_code IN
            <foreach collection="dealerCodeList" item="dealerCode" index="index" open="(" close=")" separator=",">
                #{dealerCode}
            </foreach>
        </if>
        <if test="appealStatus != null">
            AND a.appeal_status = #{appealStatus}
        </if>
        ORDER BY a.create_time DESC
        <if test="maxLimit != null">
            limit #{maxLimit}
        </if>
    </select>

    <select id="selectUnprocessedFenceAreaData"
            resultType="com.intelliquor.cloud.shop.common.model.ActivityRewardExceptionRecordModel">
        SELECT t1.*
        FROM t_activity_reward_exception_record AS t1
        WHERE t1.activity_type = 2
          AND t1.exception_type = 9
          AND t1.fence_status = 0
          AND t1.is_delete = 0
          AND fence_type is not NULL
          AND IFNULL(JSON_UNQUOTE(extend_data -> '$.rewardSubType'), '21') = '21'
          AND scan_time >= '2024-07-09 22:25:47'
          AND t1.origin_id IN
        <foreach collection="list" item="originId" index="index" open="(" close=")" separator=",">
            #{originId}
        </foreach>
    </select>

    <select id="selectDealScanIdList" resultType="java.lang.Long">
        SELECT
            DISTINCT origin_id
        FROM
            t_activity_reward_exception_record
        WHERE
            activity_type = 2
          AND exception_type = 9
          AND fence_status = 0
          AND is_delete = 0
          AND fence_type is not NULL
          AND IFNULL(JSON_UNQUOTE(extend_data -> '$.rewardSubType'), '21') = '21'
          AND scan_time >= '2024-07-09 22:25:47'
          AND create_time >= '2024-07-09 22:25:47'
    </select>


    <select id="selectNoDealFenceType" resultType="com.intelliquor.cloud.shop.common.model.ActivityRewardExceptionRecordModel">
        SELECT
            er.* ,sc.draw_region_name,sc.province,sc.city
        FROM
            t_activity_reward_exception_record  er
                INNER JOIN t_consumer_scan_detail sc on sc.id = er.origin_id
        WHERE
            activity_type = 2
          AND exception_type = 9
          AND fence_type IS NULL
          AND send_msg like '%经销商授权区域%'
        ORDER BY
            id ASC
            LIMIT 1000
    </select>
</mapper>
