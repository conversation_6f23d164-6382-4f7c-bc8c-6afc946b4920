package com.intelliquor.cloud.shop.common.constant;

/**
 * <AUTHOR>
 * @PackageName com.intelliquor.cloud.shop.common.constant
 * @ClassName RedisConstant
 * @description: TODO
 * @datetime 2023年 06月 01日 15:16
 * @version: 1.0
 */
public class RedisConstant {

    public static final String REDIS_DELIVERY_ORDER_NO_QRCODE = "order:oneDelivery:orderNo:qrcode:%s"; //同一个订单同一个码的情况

    public static final String REDIS_ONE_DELIVERY_ORDER_NO = "order:oneDelivery:orderNo:%s"; //订单的一键收货订单判断KEY
    public static final String REDIS_ONE_DELIVERY_QRCODE = "order:oneDelivery:qrcode:%s"; //订单的一键收货扫的码判断KEY

    public static final String REDIS_ONE_DELIVERY_OA_QRCODE = "order:oneDelivery:oa:%s"; //订单的一键代收OA判断KEY


    public static final String REDIS_SCAN_DELIVERY_ORDER_NO = "order:scanDelivery:orderNo:%s"; //订单的扫码收货订单判断KEY
    public static final String REDIS_SCAN_DELIVERY_QRCODE = "order:scanDelivery:qrcode:%s"; //订单的扫码收货扫的码判断KEY

    public static final String REDIS_ADD_UPDATE_PROTOCOL = "protocol:addUpdate:terminalShopId:%s"; //协议的新增和更新

    public static final String REDIS_SCAN_OUTNO_DELIVERY_ORDER_NO = "order:scanOutnoDelivery:orderNo:%s"; // 出库单的扫码收货订单判断KEY
    public static final String REDIS_SCAN_OUTNO_DELIVERY_QRCODE = "order:scanOutnoDelivery:qrcode:%s"; //出库单的扫码收货扫的码判断KEY
    public static final String REDIS_SCAN_OUTNO_ORDER_NO_DELIVERY_QTY = "order:scanOutnoDelivery:qty:%s"; // 出库单对应的扫码收货收货数量判断KEY
    public static final String REDIS_SCAN_OUTNO_ORDER_NO_SEND_QTY = "order:scanOutnoDelivery:qty:%s"; // 出库单发货数量判断KEY

    public static final String REDIS_AWARD_DEALER_TERMINAL_SCAN = "award:dealerTerminalScan:scanId:%s";// 收货后对应的经销商奖励判断key

    public static final String REDIS_AWARD_TERMINAL_TERMINAL_SCAN = "award:terminalTerminalScan:scanId:%s";// 收货后对应的终端奖励判断key

    public static final String REDIS_DIVIDER_TERMINAL_KEY = "TERMINAL:DIVIDER:%s:%s"; // 划分终端  终端id  被接收人Id
    public static final String REDIS_DISPLAY_KEY = "TERMINAL:DISPLAY:%s"; // 陈列拍照redis key
    public static final String REDIS_TERMINAL_WITHDRAWAL_RECORD_KEY = "TERMINAL:WITHDRAWAL:RECORD:%s"; // 联盟积分提现KEY
    public static final String REDIS_TERMINAL_WITHDRAWAL_APPROVAL_KEY = "TERMINAL:WITHDRAWAL:APPROVAL:%s"; // 联盟积分提现审核KEY



    public static final String REDIS_TERMINAL_CODEOUTTIME_KEY = "TERMINAL:CODEOUTTIME:%s"; // 记录码第一次发货时间的key
    public static final String REDIS_TERMINAL_CODEOUTTIME_BEAN_KEY = "TERMINAL:CODEOUTTIME:BEAN:%s"; // 记录码第一次发货时间的key



    public static final String TERMINAL_SHOP_NODE_REDIS_KEY = "T_TERMINAL_SHOP_NODE:NODE:%s"; //客户经理审批信息判断KEY

    public static final String REDIS_COMPUTE_OPEN_BOTTLE_REWARD_ID_KEY = "COMPUTE:OPEN:BOTTLE:REWARD:ID:%s"; //开瓶奖励判断重复判断KEY
    public static final String REDIS_COMPUTE_OPEN_BOTTLE_REWARD_CODE_KEY = "COMPUTE:OPEN:BOTTLE:REWARD:CODE:%s"; //开瓶奖励判断重复判断KEY

    public static final String REDIS_COMPUTE_ZDDX_REWARD_ID_KEY = "COMPUTE:ZDDX:REWARD:ORDER_NO:%s:QRCODE:%s"; //终端动销奖励判断重复判断KEY
    public static final String REDIS_COMPUTE_FXSDX_REWARD_ID_KEY = "COMPUTE:FXFDX:REWARD:ORDER_NO:%s:QRCODE:%s"; //分销商动销奖励判断重复判断KEY
    public static final String REDIS_COMPUTE_JXSDX_REWARD_ID_KEY = "COMPUTE:JXFDX:REWARD:ORDER_NO:%s:QRCODE:%s:ID:%s"; //经销商动销奖励判断重复判断KEY


    public static final String REDIS_DISPLAY_RESUL_SEND_SCORE_KEY = "T_DISPLAY_RESULT:SEND:SCORE:%s"; //放发陈列奖励判断重复判断KEY

    public static final String REDIS_DISPLAY_RESUL_BATCH_SEND_KEY = "T_DISPLAY_RESULT:BATCH_SEND";

    public static final String REDIS_DISPLAY_RESUL_BATCH_SEND_NUM_KEY = "T_DISPLAY_RESULT:BATCH_SEND_NUM";



    public static final String REDIS_CHECK_SCORE_KEY = "T_CHECK:SEND:CHECK_SCORE:%s";
    public static final String REDIS_CHECK_REWARD_TOTAL_SCORE_KEY = "T_CHECK_REWARD_TOTAL:CHECK_TOTAL:%s:%s";
    public static final String REDIS_CHECK_REWARD_TOTAL_ID_SCORE_KEY = "T_CHECK_REWARD_TOTAL:CHECK_TOTAL_ID:%s:%s";

    public static final String REDIS_CHECK_REWARD_TOTAL_RETURN_SCORE_KEY = "T_CHECK_REWARD_TOTAL:RETURN_CHECK_TOTAL:%s:%s";
    public static final String REDIS_CHECK_REWARD_TOTAL_RETURN_ID_SCORE_KEY = "T_CHECK_REWARD_TOTAL:RETURN_CHECK_TOTAL_ID:%s:%s";
    public static final String REDIS_CHECK_SALE_REWARD_RETURN_KEY = "T_CHECK_SALE_REWARD:RETURN_CHECK_TOTAL:%s";

    public static final String REDIS_CHECK_SALE_REWARD_KEY = "T_CHECK_SALE_REWARD:CHECK_TOTAL:%s";

    public static final String REDIS_CHECK_OPEN_BOTTLE_REWARD_KEY = "T_CHECK_OPEN_BOTTLE_REWARD:CHECK_TOTAL:%s";


    public static final String REDIS_SY_TOKEN_PHONE_AGENTID = "SY:TOKEN:PHONE:%s:AGENTID:%s"; //获取溯源token放到redis

    public static final String REDIS_TERMINAL_INTEGRAL_KEY = "TERMINAL:INTEGRAL:%s"; // 终端积分

    public static final String  SEND_REDIS_LOCK_KEY = "terminal_red_envelope_reward_send_task";// 终端发红包

    public static final String  QUERY_REDIS_LOCK_KEY = "terminal_red_envelope_reward_query_task";//微信查询终端发红包批次

    public static final String  TERMINAL_RED_ENVELOPE_SUCCESS = "terminal_red_envelope_success:%s";//微信红包发送成功记录

    public static final String  TERMINAL_RED_ENVELOPE_FAIL = "terminal_red_envelope_reward_send_fail";//微信红包发送失败

    public static final String  TERMINAL_RED_ENVELOPE_CHAO = "terminal_red_envelope_reward_send_chao";//微信红包发送超额提醒

    public static final String  TERMINAL_RED_ENVELOPE_SUCCESS_QRCODE = "terminal_red_envelope_success:qrcode:%s";//微信红包按盖内码发送成功记录


    public static final String  ACTIVITY_REWARD_RECORD_SALE_DEALER_KEY = "T_ACTIVITY_REWARD_RECORD:DEALER:%s";//经销商动销奖励积分发到以前表时的重复判断key
    public static final String  ACTIVITY_REWARD_RECORD_SALE_DISTRIBUTOR_KEY = "T_ACTIVITY_REWARD_RECORD:DISTRIBUTOR:%s";//分销商动销奖励积分发到以前表时的重复判断key
    public static final String  ACTIVITY_REWARD_RECORD_SALE_TERMINAL_KEY = "T_ACTIVITY_REWARD_RECORD:TERMINAL:%s";//终端动销奖励积分发到以前表时的重复判断key
    public static final String  ACTIVITY_REWARD_RECORD_BOTTLE_KEY = "T_ACTIVITY_REWARD_RECORD:BOTTLE:%s";//开瓶奖励积分发到旧表时的重复判断key

    public static final String ACTIVITY_REWARD_RECORD_FENCE_BOTTLE_KEY = "T_ACTIVITY_REWARD_RECORD:FENCE:BOTTLE:%s";//开瓶奖励积分发到旧表时的重复判断key
    public static final String REDIS_DEALER_CONTRACT_REL_HANDLE_DATA_KEY = "T_DEALER_CONTRACT_REL:HANDLE_DATA"; //处理经销商授权区域数据的KEY

    public static final String REDIS_ADD_UPDATE_PROTOCOL_NEW = "protocol:addUpdateNew:terminalShopId:%s"; //协议的新增和更新


    public static final String REDIS_TERMINAL_WITHDRAWAL_ACCOUNT_KEY = "TERMINAL:WITHDRAWAL:ACCOUNT:%s"; // 联盟积分提现审核账户KEY


    /**
     * 空闲状态
     */
    public final static String IDLE_STATUS = "idle";

    /**
     * 繁忙状态
     */
    public final static String BUSY_STATUS = "busy";

    public final static long REDIS_TIME_OUT_ONE = 1;
    public final static long REDIS_TIME_OUT_FIVE = 5;
    public final static long REDIS_TIME_OUT_TEN = 10;

    public static final String DISTRIBUTORS_REDIS_REWARD_NUM = "redis:activity:distributors:reward:num:%s:%s";

    public static final String DEALER_REDIS_REWARD_NUM = "redis:activity:dealer:reward:num:%s:%s";

    public static final String TERMINAL_REDIS_REWARD_NUM = "redis:activity:terminal:reward:num:%s:%s";

    public static final String ORDER_QUASH_CODE_KEY = "redis:order:quash:code:%s";

    /**
     * 开瓶奖励白名单处理
     */
    public static final String FENCE_BOTTLE_WHITE = "redis:activity:fence:bottle:white:%s";

    /**
     * 监察自主处罚
     */
    public static final String FENCE_DEALER_SELF_PUNISH_KEY = "fence:dealer:self:punish:%s";

    /**
     * 异地违约申诉审批处理
     */
    public static final String FENCE_APPEAL_VERIFY_KEY = "fence:appeal:verify:%s";

    /**
     * 异地违约申诉审批处理(运维)
     */
    public static final String OPS_FENCE_KEY = "ops:fence:%s";

    /**
     * 开瓶奖励处理(运维)
     */
    public static final String OPS_OPEN_BOTTLE_REWARD_KEY = "ops:open:bottle:reward:%s";

    /**
     * 动销奖励重新发放锁
     */
    public static final String OPS_SALES_REWARD_KEY = "ops:sales:reward:lock:%s";

    /**
     * 批量申诉防重复提交锁
     */
    public static final String BATCH_APPEAL_LOCK_KEY = "fence:batch:appeal:lock:user:%s";

}
