package com.intelliquor.cloud.shop.common.model.resp;

import lombok.Getter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 批量违约申诉响应DTO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Getter
@AllArgsConstructor
public class BatchFenceViolationAppealResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申诉成功的违约记录ID列表
     */
    private List<Integer> successIds;

    /**
     * 申诉失败的违约记录详情
     */
    private List<FailedAppealItem> failedItems;

    /**
     * 申诉失败详情
     */
    @Getter
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FailedAppealItem {
        /**
         * 违约记录ID
         */
        private Integer violationOfAgreementInfoId;

        /**
         * 失败原因
         */
        private String failureReason;
    }
}