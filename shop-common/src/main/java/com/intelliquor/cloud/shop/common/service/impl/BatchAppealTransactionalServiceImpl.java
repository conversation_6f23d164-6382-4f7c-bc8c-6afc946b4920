package com.intelliquor.cloud.shop.common.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.intelliquor.cloud.shop.common.dao.FenceViolateOpenBottleAreaRecordDao;
import com.intelliquor.cloud.shop.common.dao.FenceViolationOfAgreementAppealDao;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.model.FenceViolateOpenBottleAreaRecordModel;
import com.intelliquor.cloud.shop.common.model.FenceViolationOfAgreementAppealModel;
import com.intelliquor.cloud.shop.common.service.BatchAppealTransactionalService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 批量申诉事务性服务实现，只处理数据库持久化逻辑。
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BatchAppealTransactionalServiceImpl implements BatchAppealTransactionalService {

    private final FenceViolateOpenBottleAreaRecordDao fenceViolateOpenBottleAreaRecordDao;
    private final FenceViolationOfAgreementAppealDao fenceViolationOfAgreementAppealDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void persistBatchAppealOperations(List<Integer> violationOfAgreementInfoIds, List<FenceViolationOfAgreementAppealModel> appealsToInsert) {
        log.info("[BatchAppealTx] 开始处理申诉事务, ids={}, insertSize={}", violationOfAgreementInfoIds.size(), appealsToInsert.size());

        // 1. 更新异地违约记录的申诉状态
        if (!violationOfAgreementInfoIds.isEmpty()) {
            int updateCount = fenceViolateOpenBottleAreaRecordDao.update(null,
                    Wrappers.<FenceViolateOpenBottleAreaRecordModel>lambdaUpdate()
                            .set(FenceViolateOpenBottleAreaRecordModel::getAppealStatus, 1)
                            .set(FenceViolateOpenBottleAreaRecordModel::getUpdateTime, new Date())
                            .in(FenceViolateOpenBottleAreaRecordModel::getId, violationOfAgreementInfoIds));

            if (updateCount != violationOfAgreementInfoIds.size()) {
                log.error("[BatchAppealTx] 批量更新申诉状态失败, ids={}", violationOfAgreementInfoIds);
                throw new BusinessException("批量更新违约记录申诉状态失败");
            }
            log.debug("[BatchAppealTx] 更新申诉状态完成, rows={}", updateCount);
        }

        // 2. 批量插入申诉记录
        if (!appealsToInsert.isEmpty()) {
            int insertCount = fenceViolationOfAgreementAppealDao.batchInsert(appealsToInsert);
            if (insertCount != appealsToInsert.size()) {
                log.error("[BatchAppealTx] 批量插入记录数量不一致 expect={} actual={}", appealsToInsert.size(), insertCount);
                throw new BusinessException("批量插入申诉记录失败");
            }
            log.debug("[BatchAppealTx] 插入申诉记录完成 rows={}", insertCount);
        }

        log.info("[BatchAppealTx] 申诉事务处理完成");
    }
}
