package com.intelliquor.cloud.shop.common.model.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 批量申诉Service层请求参数
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchAppealServiceReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 违约记录ID列表
     */
    private List<Integer> violationOfAgreementInfoIds;

    /**
     * 申诉说明
     */
    private String reason;

    /**
     * 申诉附件URL
     */
    private String appealFileUrl;

    /**
     * 创建人ID
     */
    private Integer createUser;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 公司ID
     */
    private Integer companyId;

    /**
     * 申诉人电话
     */
    private String appealUserTel;

}