package com.intelliquor.cloud.shop.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.intelliquor.cloud.shop.common.model.FenceViolateOpenBottleAreaRecordModel;
import com.intelliquor.cloud.shop.common.model.FenceViolationOfAgreementAppealModel;
import com.intelliquor.cloud.shop.common.model.req.BatchAppealServiceReq;
import com.intelliquor.cloud.shop.common.model.req.FenceViolateOpenBottleAreaRecordReq;
import com.intelliquor.cloud.shop.common.model.resp.BatchFenceViolationAppealResp;
import com.intelliquor.cloud.shop.common.model.resp.FenceViolateOpenBottleAreaRecordResp;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_fence_violate_open_bottle_area_record(异地开瓶违约记录表)】的数据库操作Service
 * @createDate 2024-02-27 16:10:53
 */
public interface FenceViolateOpenBottleAreaRecordService extends IService<FenceViolateOpenBottleAreaRecordModel> {

    List<FenceViolateOpenBottleAreaRecordResp> getPageList(int page, int limit, FenceViolateOpenBottleAreaRecordReq fenceViolateOpenBottleAreaRecordReq);

    FenceViolateOpenBottleAreaRecordResp queryById(Integer id);

    /**
     * 处理开瓶违约数据
     */
    void createFenceAreaData();

    FenceViolateOpenBottleAreaRecordResp queryAppealViolateOpenBottleAreaRecordById(Integer id);

    /**
     * 发起申诉
     *
     * @param id 违约记录id
     */
    void addAppeal(FenceViolationOfAgreementAppealModel fenceViolationOfAgreementAppealModel);

    /**
     * 批量发起申诉
     *
     * @param batchAppealReq 批量申诉参数
     * @return 批量申诉结果
     */
    BatchFenceViolationAppealResp batchAddAppeal(BatchAppealServiceReq batchAppealReq);

    /**
     * 处理异地类型数据
     */
    void dealFenceTypeData();
}
