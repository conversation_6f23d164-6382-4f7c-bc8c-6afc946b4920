package com.intelliquor.cloud.shop.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.intelliquor.cloud.shop.common.basequery.ExtBaseMapper;
import com.intelliquor.cloud.shop.common.model.FenceViolationOfAgreementAppealModel;
import com.intelliquor.cloud.shop.common.model.resp.FenceViolationOfAgreementAppealModelResp;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 违约申诉 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-07
 */
public interface FenceViolationOfAgreementAppealDao extends ExtBaseMapper<FenceViolationOfAgreementAppealModel> {

    List<FenceViolationOfAgreementAppealModelResp> appealList(FenceViolationOfAgreementAppealModel req);

    List<FenceViolationOfAgreementAppealModelResp> exportData(Map<String, Object> search);

    FenceViolationOfAgreementAppealModel selectByFenceId(@Param("FenceId") Integer fenceId);
}
