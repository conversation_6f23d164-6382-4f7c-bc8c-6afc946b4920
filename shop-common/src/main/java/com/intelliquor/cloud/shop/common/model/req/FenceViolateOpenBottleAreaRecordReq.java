package com.intelliquor.cloud.shop.common.model.req;

import lombok.Data;

import java.util.List;

@Data
public class FenceViolateOpenBottleAreaRecordReq {

    /**
     * 违约经销商名称
     */
    private String violateDealerName;

    /**
     * 违约经销商编码
     */
    private String violateDealerCode;

    /**
     * 产品名称
     */
    private String goodsName;

    /**
     * 盖内码
     */
    private String qrCode;

    /**
     * 箱码
     */
    private String boxCode;

    /**
     * 开瓶开始时间
     */
    private String drawStartTime;

    /**
     * 开瓶结束时间
     */
    private String drawEndTime;

    // 是否展示无需处理的  1是不展示 空或者0是展示
    private String showDealStatus;


    /**
     * 扫码区域
     */
    private String drawArea;

    private Integer maxLimit;

    private Integer startId;

    private Integer id;

    private String province;

    private String city;

    private String district;

    /**
     * 经销商编码列表
     */
    private List<String> dealerCodeList;

    /**
     * 申诉状态 0待申诉 1申诉中 2申诉成功 3申诉失败
     */
    private Integer appealStatus;

    /**
     * 异地类型 0是省外 1是省内异地
     */
    private Integer fenceType;


}
