package com.intelliquor.cloud.shop.common.model.req;

import lombok.Getter;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 批量违约申诉请求DTO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchFenceViolationAppealReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 违约记录ID列表
     */
    @NotEmpty(message = "违约记录ID列表不能为空")
    @Size(min = 1, max = 100, message = "批量申诉数量必须在1-100之间")
    private List<Integer> violationOfAgreementInfoIds;

    /**
     * 申诉说明
     */
    @NotBlank(message = "申诉说明不能为空")
    private String reason;

    /**
     * 申诉附件URL（JSON格式）
     */
    @NotBlank(message = "申诉附件不能为空")
    private String appealFileUrl;
}