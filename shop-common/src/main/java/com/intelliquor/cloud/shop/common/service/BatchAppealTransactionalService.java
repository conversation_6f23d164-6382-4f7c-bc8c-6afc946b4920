package com.intelliquor.cloud.shop.common.service;

import com.intelliquor.cloud.shop.common.model.FenceViolationOfAgreementAppealModel;

import java.util.List;

/**
 * 批量申诉事务处理服务，仅负责持久化层面的批量更新与插入。
 * 该接口方法应保证事务原子性，避免部分成功导致的数据不一致。
 */
public interface BatchAppealTransactionalService {

    /**
     * 执行批量申诉持久化操作。
     *
     * @param violationOfAgreementInfoIds 待更新申诉状态的违约记录ID列表
     * @param appealsToInsert   待批量插入的申诉实体集合
     */
    void persistBatchAppealOperations(List<Integer> violationOfAgreementInfoIds,
                                      List<FenceViolationOfAgreementAppealModel> appealsToInsert);
}
